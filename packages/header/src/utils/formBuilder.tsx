/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import get from 'lodash/get';
import isNil from 'lodash/isNil';
import isEmpty from 'lodash/isEmpty';
import { Checkbox, FormControlLabel, Grid, MenuItem, TextField as Text, Typography, Box, Button } from '@mui/material';
import { TextField } from 'formik-mui';
import { Field, FormikValues } from 'formik';
import React, { useState } from 'react';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import {
    RichTextEditor,
    EntitySelect,
    formatDate,
    parseDate,
    formatSystemDate,
    formatSystemDateTime,
} from '@tripudiotech/styleguide';
import UnitOfMeasureField from '../components/CreatePaletteV2/UnitOfMeasureField';
import { DateArrayInput, MultiValueInput } from '../components/ArrayAttribute';
import { Attribute, AttributeType, buildAndOperatorQuery, buildCascadedQueries, buildInQuery } from '@tripudiotech/api';
import { useGlobalConfig } from '@tripudiotech/caching-store';
import { CascadingList } from '../components/CascadingList';

const folderPaths = [{ name: 'OwnerFolder', label: 'Owner Folder' }];

const TruncatedErrorMessage = ({ children, maxLength = 100 }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const errorText = typeof children === 'string' ? children : String(children);
    const shouldTruncate = errorText.length > maxLength;
    const displayText = shouldTruncate && !isExpanded
        ? errorText.substring(0, maxLength) + '...'
        : errorText;

    return (
        <Box sx={{ marginTop: '5px', marginLeft: '14px' }}>
            <Typography
                color="error"
                sx={{
                    fontSize: '0.75rem',
                    fontWeight: 400,
                    lineHeight: 1.66,
                    display: 'inline',
                }}
            >
                {displayText}
            </Typography>
            {shouldTruncate && (
                <Button
                    size="small"
                    onClick={() => setIsExpanded(!isExpanded)}
                    sx={{
                        fontSize: '0.75rem',
                        fontWeight: 400,
                        color: 'error.main',
                        textTransform: 'none',
                        padding: '0 4px',
                        minWidth: 'auto',
                        marginLeft: '4px',
                        '&:hover': {
                            backgroundColor: 'rgba(211, 47, 47, 0.04)',
                        },
                    }}
                >
                    {isExpanded ? 'Show less' : 'Show more'}
                </Button>
            )}
        </Box>
    );
};

export const buildInitialValue = (entityAttributeList, defaultValues) =>
    entityAttributeList.reduce((prev, cur) => {
        if (cur.type === AttributeType.DATE) {
            const defaultDate = defaultValues
                ? defaultValues[cur.name]
                : cur.defaultValue
                ? formatSystemDate(cur.defaultValue)
                : formatSystemDate(new Date());
            return {
                ...prev,
                [cur.name]: defaultDate,
            };
        }
        if (cur.type === AttributeType.DATE_TIME) {
            const defaultDate = defaultValues
                ? defaultValues[cur.name]
                : cur.defaultValue
                ? formatSystemDateTime(cur.defaultValue)
                : formatSystemDateTime(new Date());
            return {
                ...prev,
                [cur.name]: defaultDate,
            };
        }

        if ([AttributeType.LONG, AttributeType.FLOAT, AttributeType.INTEGER].includes(cur.type) && cur.unitOfMeasure) {
            return {
                ...prev,
                [cur.name]: defaultValues ? defaultValues[cur.name] : cur.defaultValue,
                [`${cur.name}:unit`]: get(cur, 'unitOfMeasure.quantityUnit', ''),
            };
        }

        return {
            ...prev,
            [cur.name]: defaultValues ? defaultValues[cur.name] : cur.defaultValue,
        };
    }, {});

export const buildFormItem = ({
    attribute,
    setFieldValue,
    size = 'medium',
    attributes = [],
    values = {},
}: {
    attribute: Attribute;
    setFieldValue: (name: string, value: any, shouldValidate?: boolean) => void;
    size?: 'small' | 'medium';
    values?: FormikValues;
    attributes?: Attribute[];
}) => {
    const { id, displayName, name, description, nullable, unitOfMeasure } = attribute;
    const hasUnitOfMeasure = unitOfMeasure && !isEmpty(unitOfMeasure);
    const isRequired = isNil(nullable) ? false : !Boolean(nullable);

    if (attribute.richText) {
        return (
            <Grid item xs={12} key={id}>
                <Field
                    fullWidth
                    variant="outlined"
                    id={id}
                    label={displayName}
                    name={name}
                    helperText={description}
                    rows={4}
                    disabled={false}
                    required={isRequired}
                    component={RichTextEditor}
                    size={size}
                />
            </Grid>
        );
    }

    switch (attribute.type) {
        case AttributeType.INTEGER:
        case AttributeType.LONG:
        case AttributeType.FLOAT:
            return hasUnitOfMeasure ? (
                <Grid item xs={12} key={id}>
                    <Grid container spacing={2}>
                        <Grid item xs={6}>
                            <Field
                                fullWidth
                                variant="outlined"
                                component={TextField}
                                label={displayName}
                                required={isRequired}
                                name={attribute.name}
                                disabled={false}
                                helperText={attribute.description}
                                InputLabelProps={{ shrink: true }}
                                size={size}
                                onChange={(e: any) => {
                                    const {
                                        target: { value },
                                    } = e;
                                    setFieldValue(name, !isRequired && isEmpty(value) ? null : value);
                                }}
                            />
                        </Grid>

                        <UnitOfMeasureField attribute={attribute} size={size} />
                    </Grid>
                </Grid>
            ) : (
                <Grid item key={id} xs={6}>
                    <Field
                        fullWidth
                        variant="outlined"
                        component={TextField}
                        label={displayName}
                        required={isRequired}
                        name={attribute.name}
                        disabled={false}
                        helperText={attribute.description}
                        InputLabelProps={{ shrink: true }}
                        size={size}
                    />
                </Grid>
            );

        case AttributeType.BOOLEAN:
            return (
                <Grid item key={id} xs={12}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={field.value}
                                                onChange={(e) => setFieldValue(name, e.target.checked)}
                                                name={name}
                                                required={isRequired}
                                            />
                                        }
                                        label={displayName}
                                    />
                                    {isErr && (
                                        <Typography
                                            color="error"
                                            sx={{ fontSize: '0.75rem', fontWeight: 400, marginLeft: '14px' }}
                                        >
                                            {error}
                                        </Typography>
                                    )}
                                </>
                            );
                        }}
                    </Field>
                </Grid>
            );

        case AttributeType.STRING: {
            const dataType = get(attribute, ['constraint', 'dataType'], null);
            const enumRange = get(attribute, ['constraint', 'enumRange'], []);

            if (dataType) {
                const constraintCascadingAttrs = get(attribute, ['constraint', 'cascadingAttrs']);
                const cascadingAttributes = constraintCascadingAttrs ? Object.keys(constraintCascadingAttrs) : [];

                const cascadedQueries = buildCascadedQueries(attributes, name, values);
                const additionalQuery = cascadedQueries.length > 0 ? buildAndOperatorQuery(cascadedQueries) : null;
                return (
                    <Grid item xs={12} sm={6} key={id}>
                        <Field name={name} helperText={description}>
                            {({ field, form, meta: { error, touched } }) => {
                                const isErr = touched && !!error;
                                return (
                                    <>
                                        <EntitySelect
                                            label={displayName}
                                            entityType={dataType}
                                            onChange={(newValue) => {
                                                setFieldValue(name, newValue.value);
                                                cascadingAttributes.forEach((cascadedAttribute) => {
                                                    setFieldValue(cascadedAttribute, null);
                                                });
                                            }}
                                            additionalQuery={additionalQuery}
                                            defaultValue={field.value}
                                            onBlur={() => form.setFieldTouched(name)}
                                            isRequired={isRequired}
                                        />
                                        {isErr && (
                                            <TruncatedErrorMessage>
                                                {error}
                                            </TruncatedErrorMessage>
                                        )}
                                    </>
                                );
                            }}
                        </Field>
                    </Grid>
                );
            }

            if (enumRange.length > 0) {
                return (
                    <Grid item xs={12} sm={6} key={id}>
                        <Field
                            fullWidth
                            select
                            type="text"
                            variant="outlined"
                            name={name}
                            label={displayName}
                            required={isRequired}
                            helperText={description}
                            component={TextField}
                            InputLabelProps={{ shrink: true }}
                            disabled={false}
                            size={size}
                            onChange={(e) => {
                                setFieldValue(name, e.target.value);
                            }}
                        >
                            {enumRange.map((enumValue) => (
                                <MenuItem value={enumValue} key={enumValue}>
                                    {enumValue}
                                </MenuItem>
                            ))}
                        </Field>
                    </Grid>
                );
            }

            if (attribute.name === 'fileLocation') {
                return (
                    <Grid item key={id} sx={{ width: '100%' }}>
                        <Grid item xs={12}>
                            <Field
                                fullWidth
                                select
                                type="text"
                                variant="outlined"
                                name="filePath"
                                label="Folder Path"
                                size={size}
                                required={isRequired}
                                component={TextField}
                                InputLabelProps={{ shrink: true }}
                                helperText={description}
                            >
                                {folderPaths.map((folder) => (
                                    <MenuItem value={folder.name} key={folder.name}>
                                        {folder.label}
                                    </MenuItem>
                                ))}
                            </Field>
                        </Grid>
                    </Grid>
                );
            } else {
                return (
                    <Grid item xs={12} sm={6} key={id}>
                        <Field
                            fullWidth
                            variant="outlined"
                            component={TextField}
                            required={isRequired}
                            label={displayName}
                            name={name}
                            size={size}
                            helperText={description}
                            disabled={false}
                            InputLabelProps={{ shrink: true }}
                        />
                    </Grid>
                );
            }
        }

        case AttributeType.TEXT: {
            return (
                <Grid item xs={12} sm={12} key={id}>
                    <Field
                        fullWidth
                        multiline
                        variant="outlined"
                        component={TextField}
                        required={isRequired}
                        label={displayName}
                        name={name}
                        size={size}
                        helperText={description}
                        disabled={false}
                        InputLabelProps={{ shrink: true }}
                        sx={{
                            textarea: {
                                resize: 'both',
                                width: '100%',
                                padding: '0px !important',
                                minHeight: '50px',
                            },
                        }}
                    />
                </Grid>
            );
        }

        case AttributeType.DATE:
            return (
                <Grid item xs={12} sm={6} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <LocalizationProvider dateAdapter={AdapterMoment}>
                                    <DatePicker
                                        label={displayName}
                                        value={parseDate(field.value) || new Date()}
                                        onChange={(newValue) => {
                                            setFieldValue(name, formatSystemDate(newValue), true);
                                        }}
                                        inputFormat={useGlobalConfig.getState().getDateFormat()}
                                        disabled={false}
                                        renderInput={(params) => (
                                            <Text
                                                {...params}
                                                size={size}
                                                InputLabelProps={{ shrink: true }}
                                                error={isErr}
                                                required={isRequired}
                                                helperText={isErr ? error : description}
                                                fullWidth
                                                onBlur={() => form.setFieldTouched(name)}
                                            />
                                        )}
                                    />
                                </LocalizationProvider>
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.DATE_TIME:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <LocalizationProvider dateAdapter={AdapterMoment}>
                                    <DateTimePicker
                                        label={displayName}
                                        value={field.value}
                                        onChange={(newValue) => {
                                            setFieldValue(name, formatSystemDateTime(newValue));
                                        }}
                                        inputFormat={useGlobalConfig.getState().getDateTimeFormat()}
                                        renderInput={(params) => {
                                            return (
                                                <Text
                                                    {...params}
                                                    error={isErr}
                                                    InputLabelProps={{ shrink: true }}
                                                    required={isRequired}
                                                    helperText={isErr ? error : description}
                                                    fullWidth
                                                    //@ts-ignore
                                                    size={size}
                                                    onBlur={() => form.setFieldTouched(name)}
                                                />
                                            );
                                        }}
                                    />
                                </LocalizationProvider>
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.DATE_ARRAY:
        case AttributeType.DATE_TIME_ARRAY:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <DateArrayInput
                                    isDateTime={attribute.type === AttributeType.DATE_TIME_ARRAY}
                                    displayName={displayName}
                                    value={field.value}
                                    onChange={(value) => setFieldValue(name, value, true)}
                                    error={isErr ? error : ''}
                                    required={isRequired}
                                    helperText={description}
                                    onBlur={() => form.setFieldTouched(name)}
                                />
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.STRING_ARRAY:
            const dataType = get(attribute, ['constraint', 'dataType'], null);
            const enumRange = get(attribute, ['constraint', 'enumRange'], []);

            if (dataType) {
                return (
                    <Grid item xs={12} key={id}>
                        <Field name={name} helperText={description}>
                            {({ field, form, meta: { error, touched } }) => {
                                const isErr = touched && !!error;
                                return (
                                    <>
                                        <EntitySelect
                                            label={displayName}
                                            entityType={dataType}
                                            onChange={(newValue) => {
                                                setFieldValue(
                                                    name,
                                                    newValue?.map(({ value }) => value)
                                                );
                                            }}
                                            isMulti
                                            defaultValue={field.value}
                                            isRequired={isRequired}
                                            onBlur={() => form.setFieldTouched(name)}
                                        />
                                        {isErr && (
                                            <TruncatedErrorMessage>
                                                {error}
                                            </TruncatedErrorMessage>
                                        )}
                                    </>
                                );
                            }}
                        </Field>
                    </Grid>
                );
            }
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <MultiValueInput
                                    displayName={displayName}
                                    value={field.value}
                                    onChange={(value) => setFieldValue(name, value)}
                                    error={isErr ? error : ''}
                                    helperText={description}
                                    onBlur={() => {
                                        form.setFieldTouched(name);
                                    }}
                                    enumRange={enumRange}
                                    required={isRequired}
                                />
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.INTEGER_ARRAY:
        case AttributeType.FLOAT_ARRAY:
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <MultiValueInput
                                    type={attribute.type as AttributeType.FLOAT_ARRAY | AttributeType.INTEGER_ARRAY}
                                    displayName={displayName}
                                    value={field.value}
                                    onChange={(value) => setFieldValue(name, value)}
                                    error={isErr ? error : ''}
                                    helperText={description}
                                    onBlur={() => {
                                        form.setFieldTouched(name);
                                    }}
                                    enumRange={enumRange}
                                    required={isRequired}
                                />
                            );
                        }}
                    </Field>
                </Grid>
            );
        case AttributeType.CASCADING_LIST: {
            return (
                <Grid item xs={12} key={id}>
                    <Field name={name} helperText={description}>
                        {({ field, form, meta: { error, touched } }) => {
                            const isErr = touched && !!error;
                            return (
                                <CascadingList
                                    displayName={attribute?.displayName}
                                    value={field.value}
                                    onChange={(value) => setFieldValue(name, value)}
                                    cascadingListItems={attribute?.constraint?.cascadingListItems}
                                />
                            );
                        }}
                    </Field>
                </Grid>
            );
        }
        default:
            return null;
    }
};
