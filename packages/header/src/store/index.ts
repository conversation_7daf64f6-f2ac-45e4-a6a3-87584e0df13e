/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { create } from 'zustand';
import {
    entityUrls,
    fetch,
    assetServiceUrl,
    trackingService,
    migrationUrls,
    Schema,
    AxiosResponse,
    RESPONSE_ERROR_TYPE,
    EntityDetail,
} from '@tripudiotech/api';
import get from 'lodash/get';
import { GROUP_LABEL, OptionType } from '../constants';
import { notifyError } from '@tripudiotech/styleguide';

type SearchStore = {
    deleteEntity: (entityId: string) => Promise<any>;
    recentlyViewedEntities: any[];
    isCreatingEntity: boolean;
    isImporting: boolean;
    getRecentlyViewed: (userEmail: string, limit?: number) => Promise<any>;
    checkinDocument: (entityId: string, request: any) => Promise<any>;
    createEntity: (
        schemaDetail: Schema,
        data: Record<string, any>,
        relations: any,
        lifeCycle: any,
        otherParameters?: any,
        onError?: (error: AxiosResponse) => void
    ) => Promise<{ result: AxiosResponse<EntityDetail>; error: any }>;
    importCsv: (data: any) => Promise<any>;
};

const useLocalStore = create<SearchStore>((set, getState) => ({
    recentlyViewedEntities: [],
    isCreatingEntity: false,
    isImporting: false,
    getRecentlyViewed: async (userEmail: string, limit = 5) => {
        const { data = [] } = await trackingService.getRecentlyViewed(userEmail, limit, 0);
        const options = data.map((item) => ({
            value: item.messageKey,
            label: get(item, ['entity', 'properties', 'name']),
            group: GROUP_LABEL[OptionType.RecentlyViewed],
            type: OptionType.RecentlyViewed,
            entity: item.entity,
            groupOrder: 0,
        }));
        set({ recentlyViewedEntities: options });
    },
    deleteEntity: async (entityId: string) => {
        try {
            await fetch({
                ...entityUrls.deleteEntity,
                params: { entityId },
                skipToast: true,
            });
            return {
                error: null,
            };
        } catch (error) {
            return {
                error,
            };
        }
    },
    checkinDocument: async (entityId: string, request) => {
        try {
            const result = await fetch({
                ...assetServiceUrl.checkinDocument,
                params: { id: entityId },
                data: request,
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            return result;
        } catch (e) {
            return false;
        } finally {
        }
    },
    createEntity: async (
        schemaDetail: Schema,
        data,
        relations: any,
        lifeCycle: any,
        otherParameters?: any,
        onError?: (error: AxiosResponse) => void
    ) => {
        try {
            if (data.lifeCycle) {
                // remove redundant properties
                delete data.lifeCycle;
            }

            set({ isCreatingEntity: true });

            const result: AxiosResponse<EntityDetail> = await fetch({
                ...entityUrls.createEntity,
                params: { entityType: schemaDetail.entityType.name },
                data: {
                    attributes: data,
                    ...(relations ? { relations } : {}),
                    ...(lifeCycle ? { lifeCycle } : {}),
                    ...(otherParameters ? { otherParameters } : {}),
                },
                skipToast: true,
            });
            return {
                result,
                error: null,
            };
        } catch (error) {
            if (error.response.status === 409) {
                const existingEntityId = error?.response?.data?.metadata?.entityId;
                const existingEntityType = error?.response?.data?.metadata?.entityType;
                notifyError(
                    'Unique constraint for <b>' +
                        schemaDetail.entityType.displayName +
                        '</b> is defined as <b>[' +
                        schemaDetail.entityType.uniqueKeys
                            .map((key) => schemaDetail.attributes[key].displayName)
                            .join(', ') +
                        ']</b>. Another ' +
                        schemaDetail.entityType.displayName +
                        ' satisfying the unique constraint already exists in the system. ' +
                        (existingEntityType && existingEntityId
                            ? '<a href=/detail/' +
                              existingEntityType +
                              '/' +
                              existingEntityId +
                              ' target=_blank>View existing record</a>'
                            : '')
                );
            } else {
                onError && onError(error.response);
                const isInvalidAttributeError = error.response?.data?.errors?.some(
                    (e: any) => e?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE
                );
                const needConfirmationOnBusinessRule = error.response?.data?.businessRuleResults?.length > 0;
                if (!isInvalidAttributeError && !needConfirmationOnBusinessRule) {
                    notifyError('Error: ' + error.response.data.errorMessage || error.message);
                }
            }
            return {
                result: null,
                error,
            };
        } finally {
            set({ isCreatingEntity: false });
        }
    },
    importCsv: async (data: any) => {
        try {
            set({ isImporting: true });
            const { file, entityType } = data;
            const formData = new FormData();
            formData.append('file', file);
            formData.append('entityType', entityType);

            await fetch({
                ...migrationUrls.import,
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                data: formData,
                successMessage: 'Successfully imported the file. We will let you know once all data is imported',
            });
        } catch {
        } finally {
            set({ isImporting: false });
        }
    },
}));

export default useLocalStore;
