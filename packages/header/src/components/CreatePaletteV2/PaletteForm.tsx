/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { AccordionDetails, AccordionSummary, Box, Grid, MenuItem, Typography, Button } from '@mui/material';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useAuth, useSchemaDetail, SchemaTreeMapEntry, useCreateEntity } from '@tripudiotech/caching-store';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import isNull from 'lodash/isNull';
import includes from 'lodash/includes';
import {
    SYSTEM_RELATION,
    PERMISSION_TYPES,
    PERMISSION,
    buildCheckInForm,
    AttributeType,
    SYSTEM_ENTITY_TYPE,
    RESPONSE_ERROR_TYPE,
    AxiosResponse,
    EntityDetail,
} from '@tripudiotech/api';
import {
    Loading,
    ExpandMoreIcon,
    ImagePreview,
    EntitySelect,
    notifyError,
    notifySuccess,
    LoadingOverlay,
    OwnerSelector,
    useDialogStore,
} from '@tripudiotech/styleguide';
import SectionTitle from './SectionTitle';
import { buildFormItem } from '../../utils/formBuilder';
import { Formik, Form, Field } from 'formik';
import Accordion from '../Accordion';
import Dropzone from 'react-dropzone';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { TextField, CheckboxWithLabel } from 'formik-mui';
import {
    getRequireRelations,
    isValidAttributeToRender,
    useInitialValues,
    useRequiredRelations,
} from '../../hooks/usePaletteData';
import { useNavigate } from 'react-router-dom';
import useLocalStore from '../../store';

const TruncatedErrorMessage = ({ children, maxLength = 100 }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const errorText = typeof children === 'string' ? children : String(children);
    const shouldTruncate = errorText.length > maxLength;
    const displayText = shouldTruncate && !isExpanded
        ? errorText.substring(0, maxLength) + '...'
        : errorText;

    return (
        <Box sx={{ marginTop: '5px', marginLeft: '16px' }}>
            <Typography
                sx={{
                    fontWeight: 400,
                    fontSize: '0.75rem',
                    lineHeight: 1.66,
                    color: '#d32f2f',
                    display: 'inline',
                }}
            >
                {displayText}
            </Typography>
            {shouldTruncate && (
                <Button
                    size="small"
                    onClick={() => setIsExpanded(!isExpanded)}
                    sx={{
                        fontSize: '0.75rem',
                        fontWeight: 400,
                        color: '#d32f2f',
                        textTransform: 'none',
                        padding: '0 4px',
                        minWidth: 'auto',
                        marginLeft: '4px',
                        '&:hover': {
                            backgroundColor: 'rgba(211, 47, 47, 0.04)',
                        },
                    }}
                >
                    {isExpanded ? 'Show less' : 'Show more'}
                </Button>
            )}
        </Box>
    );
};

// Keep the original ErrorTypography for backward compatibility
const ErrorTypography = ({ children }) => {
    return <TruncatedErrorMessage maxLength={100}>{children}</TruncatedErrorMessage>;
};
const dropZoneStyle: any = {
    display: 'inline-flex',
    borderRadius: 4,
    border: '2px dashed #eaeaea',
    width: '96%',
    height: 100,
    padding: 4,
    cursor: 'pointer',
    textAlign: 'center',
};

const getCanCreateStates = (states) => {
    return states.filter(
        (state) =>
            includes(state[PERMISSION.CAN_CREATE], PERMISSION_TYPES.ANY) &&
            Object.values(PERMISSION_TYPES).some((type) => includes(state[PERMISSION.CAN_CONNECT_AS_FROM_SIDE], type))
    );
};
const changePayloadKey = (field: any) => {
    const { lock, file, filePath, ...rest } = field;
    return rest;
};

const AttributeBuilder = ({
    schema,
    label = 'Attributes',
    setFieldValue,
    setFieldTouched,
    errors,
    excludedAttributes = null,
    values = {},
    touched,
}) => {
    let sortedKeys = {};
    const items = useMemo(() => {
        const attributeOrder = get(schema, 'entityType.attributeOrder', []);
        const attributes = get(schema, 'attributes', {});
        let sortedAttributes = [];

        attributeOrder.forEach((attribute: any) => {
            const isGroup = attribute.includes(':');
            if (isGroup) {
                const [groupName, groupString] = attribute.split(':');
                const groupAttributes = groupString?.split(',') || [];
                let sortedGroupAttributes = [];
                groupAttributes.forEach((groupAttribute) => {
                    const schemaAttribute = attributes[groupAttribute];
                    sortedKeys[groupAttribute] = true;
                    if (isValidAttributeToRender(schemaAttribute) && !excludedAttributes?.[groupAttribute]) {
                        sortedGroupAttributes.push(schemaAttribute);
                    }
                });
                if (sortedGroupAttributes.length > 0) {
                    sortedAttributes.push({
                        type: AttributeType.GROUP,
                        attributes: sortedGroupAttributes,
                        name: groupName,
                    });
                }
            } else {
                const schemaAttribute = attributes[attribute];
                sortedKeys[attribute] = true;
                if (isValidAttributeToRender(schemaAttribute) && !excludedAttributes?.[schemaAttribute.name]) {
                    sortedAttributes.push(attributes[attribute]);
                }
            }
        });
        Object.values(get(schema, 'attributes', {})).forEach((attribute: any) => {
            if (
                !sortedKeys[attribute.name] &&
                isValidAttributeToRender(attribute) &&
                !excludedAttributes?.[attribute.name]
            ) {
                sortedAttributes.push(attribute);
            }
        });
        return sortedAttributes;
    }, [schema]);
    const requiredRelations = useRequiredRelations(schema);
    return schema && items.length > 0 ? (
        <>
            <SectionTitle>{label}</SectionTitle>
            <Grid container spacing={2}>
                {items.map((item) => {
                    if (item.type === AttributeType.GROUP) {
                        return (
                            <Grid item xs={12} key={`${AttributeType.GROUP}-${item.name}`}>
                                <Accordion defaultExpanded>
                                    <AccordionSummary
                                        className="summary"
                                        expandIcon={<ExpandMoreIcon className="expandIcon" />}
                                    >
                                        <Typography variant="label2-med" className="label">
                                            {item.name}
                                        </Typography>
                                    </AccordionSummary>
                                    <AccordionDetails className="accordionDetails">
                                        <Grid container spacing={2}>
                                            {item.attributes.map((attribute) =>
                                                buildFormItem({
                                                    attribute,
                                                    setFieldValue,
                                                    values,
                                                    attributes: Object.values(schema?.attributes || {}),
                                                })
                                            )}
                                        </Grid>
                                    </AccordionDetails>
                                </Accordion>
                            </Grid>
                        );
                    }
                    return buildFormItem({
                        attribute: item,
                        setFieldValue,
                        values,
                        attributes: Object.values(schema?.attributes || {}),
                    });
                })}
                {!isEmpty(requiredRelations) && (
                    <Grid item xs={12}>
                        <Accordion defaultExpanded>
                            <AccordionSummary
                                className="summary"
                                expandIcon={<ExpandMoreIcon className="expandIcon" />}
                            >
                                <Typography variant="label2-med" className="label">
                                    Required Relations
                                </Typography>
                            </AccordionSummary>
                            <AccordionDetails className="accordionDetails">
                                <Grid container spacing={2}>
                                    {requiredRelations.map((rel) => (
                                        <Grid key={`rel-${rel.name}`} item xs={12} md={6}>
                                            <EntitySelect
                                                label={rel?.displayName}
                                                entityType={rel.toEntityType}
                                                onChange={(newValue) => {
                                                    setFieldValue(`relation-${rel.name}`, newValue);
                                                }}
                                                onBlur={() => setFieldTouched(`relation-${rel.name}`)}
                                                isMulti={!rel?.singleRelation}
                                                isRequired
                                                name={`relation-${rel.name}`}
                                            />
                                            {touched?.[`relation-${rel.name}`] && errors[`relation-${rel.name}`] && (
                                                <TruncatedErrorMessage>
                                                    {errors[`relation-${rel.name}`]}
                                                </TruncatedErrorMessage>
                                            )}
                                        </Grid>
                                    ))}
                                </Grid>
                            </AccordionDetails>
                        </Accordion>
                    </Grid>
                )}
            </Grid>
        </>
    ) : null;
};

export interface PaletteFormProps {
    selectedEntityType: SchemaTreeMapEntry;
    onClose: () => void;
    isCreateAnother: boolean;
}
const PaletteForm = ({ selectedEntityType, onClose, isCreateAnother = false }: PaletteFormProps) => {
    const userInfo = useAuth((state) => state.userInfo);
    const hasMaster = useMemo(
        () => selectedEntityType?.relations.find((r) => r.name === SYSTEM_RELATION.HAS_MASTER),
        [selectedEntityType]
    );
    const isDocumentType = useMemo(
        () => get(selectedEntityType, ['path'], []).includes('Document'),
        [selectedEntityType]
    );
    const { deleteEntity, createEntity } = useLocalStore();
    const { onOpenDialog } = useDialogStore();
    const [imagePreview, setImagePreview] = useState(null);
    const [fileName, setFileName] = useState('');
    const [isImage, setIsImage] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { checkinDocument } = useLocalStore();
    const [schemaDetail, masterSchemaDetail, getSchemaDetail, getAttributeName, loading] = useSchemaDetail((state) => [
        state.schema[selectedEntityType?.name],
        hasMaster ? state.schema[hasMaster.toEntityType] : null,
        state.getSchema,
        state.getAttributeName,
        state.isLoading[selectedEntityType?.name] || (hasMaster && state.isLoading[hasMaster?.toEntityType]),
    ]);
    const { disableNavigateOnCreate, postSubmitHandler } = useCreateEntity();

    const canCreateLifeCycleStates = useMemo(() => {
        let lifecycleMap = {};
        if (schemaDetail) {
            schemaDetail.lifecycles?.forEach((lf) => {
                const { states, lifeCycle } = lf;
                lifecycleMap[lifeCycle.id] = getCanCreateStates(Object.values(states));
            });
        }

        return lifecycleMap;
    }, [schemaDetail]);

    const [initialValues, validationSchema] = useInitialValues(schemaDetail, masterSchemaDetail);

    useEffect(() => {
        if (selectedEntityType) {
            getSchemaDetail(selectedEntityType?.name, true);
            const masterRel = selectedEntityType?.relations?.find((r) => r.name === SYSTEM_RELATION.HAS_MASTER);
            if (masterRel) {
                getSchemaDetail(masterRel.toEntityType);
            }
        }
    }, [selectedEntityType]);

    const handleLifecycleChange = useCallback(
        (e, setFieldValue) => {
            const lifecycleId = e.target.value;
            setFieldValue('lifeCycle.id', lifecycleId);
            const canCreateStates: any[] = getCanCreateStates(
                Object.values(schemaDetail?.lifecycles?.find((lf) => lf.lifeCycle.id === lifecycleId)?.states || {})
            );
            if (canCreateStates.length > 0) {
                setFieldValue('lifeCycle.startState', canCreateStates[0].id);
            }
        },
        [schemaDetail]
    );

    const navigate = useNavigate();

    const handleError = (error, entityType, name) => {
        const errResponse = error.response;
        if (errResponse && errResponse.status === 400) {
            const errMsg = errResponse.data?.errorMessage || 'An error occurred';
            notifyError(errMsg);
            return;
        }
        notifyError(
            <span>
                An error occurred while creating{' '}
                <b>
                    {entityType} {name}
                </b>
            </span>
        );
    };

    const handleDocumentCheckin = async (entity: EntityDetail, values: Record<string, any>, setSubmitting) => {
        const formData = buildCheckInForm({ file: values.file, primary: 'true', lock: values.lock ?? 'false' });
        const part = await checkinDocument(entity.id, formData);
        if (part) {
            setIsSubmitting(false);
            notifySuccess(
                <span>
                    <b>
                        {entity.properties.type} {entity.properties.name}
                    </b>{' '}
                    has been created successfully
                </span>
            );
            if (!isCreateAnother) {
                if (postSubmitHandler) {
                    postSubmitHandler(entity.id, entity.properties);
                }
                if (!disableNavigateOnCreate) {
                    navigate(`../detail/${selectedEntityType.name}/${entity.id}/properties`);
                }
                onClose();
            }
        } else {
            // delete entity due to checkin fail
            const { error } = await deleteEntity(entity.id);
            setIsSubmitting(false);
            if (!isNull(error)) {
                setSubmitting(false);
                handleError(error, entity.properties.displayName, null);
                return;
            }
        }
    };

    const handleSubmit = async (values, setSubmitting, setFieldError) => {
        setSubmitting(true);
        setIsSubmitting(true);
        const selectedType = selectedEntityType.name;
        const entityTypeDisplayName = schemaDetail?.entityType?.displayName;
        let relations = [];
        let { owner, ...payload } = values;
        if (owner) {
            relations.push({
                name: 'OWNED_BY',
                entityId: owner,
                entityType: SYSTEM_ENTITY_TYPE.AGENT,
            });
            delete payload.owner;
        }

        const requiredRelations = [...getRequireRelations(schemaDetail), ...getRequireRelations(masterSchemaDetail)];
        requiredRelations.forEach((rel) => {
            if (Array.isArray(get(values, `relation-${rel.name}`, []))) {
                get(values, `relation-${rel.name}`, []).forEach((relation) => {
                    relations.push({
                        name: rel.name,
                        entityId: relation.value,
                        entityType: relation.type,
                    });
                });
            } else {
                if (values?.[`relation-${rel.name}`]) {
                    const relation = values?.[`relation-${rel.name}`];
                    relations.push({
                        name: rel.name,
                        entityId: relation.value,
                        entityType: relation.type,
                    });
                }
            }

            delete payload[`relation-${rel.name}`];
        });

        payload = changePayloadKey(payload);

        if (isDocumentType) {
            payload['fileLocation'] = '/' + userInfo.email;
        }

        const onCreateEntitySuccess = (data) => {
            if (isDocumentType && values.file) {
                handleDocumentCheckin(data, values, setSubmitting);
                return;
            }
            setSubmitting(false);
            setIsSubmitting(false);
            notifySuccess(
                <span>
                    <b>
                        {entityTypeDisplayName} {values.name}
                    </b>{' '}
                    has been created successfully
                </span>
            );
            if (!isCreateAnother) {
                if (postSubmitHandler) {
                    postSubmitHandler(data.id, data.properties);
                }
                if (!disableNavigateOnCreate) {
                    navigate(`../detail/${selectedType}/${data.id}/properties`);
                }
                onClose();
            }
        };

        const onError = (error: AxiosResponse) => {
            const invalidAttributeErrors = error.data?.errors?.filter(
                (e: any) => e?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE
            );
            if (invalidAttributeErrors?.length) {
                invalidAttributeErrors.forEach((errItem: any) => {
                    const invalidAttributeId = errItem?.metadata?.data;
                    const invalidAttributeName =
                        getAttributeName(schemaDetail, invalidAttributeId) ||
                        getAttributeName(masterSchemaDetail, invalidAttributeId);

                    if (invalidAttributeName) {
                        setFieldError(invalidAttributeName, errItem?.errorDetail);
                    }
                });
            }
            // Handle confirmation on business rule warnings
            const rules = error.data?.businessRuleResults;
            const warningHandShakeId = error.data?.otherParameters?.warningHandShakeId;
            if (rules?.length > 0 && warningHandShakeId) {
                const warningListHtml = `
                <div>
                    <p> Some business rules were violated</p>
                    <ul style="margin: 4px;">
                        ${rules.map((rule) => `<li>${rule.message}</li>`).join('')}
                    </ul>
                    <p><strong>Do you still want to proceed?</strong></p>
                </div>
            `;
                const otherParameters = {
                    warningHandShakeId: warningHandShakeId,
                };
                onOpenDialog(
                    'Review Warnings',
                    warningListHtml,
                    async () => {
                        const { result: part, error: partCreateError }: any = await createEntity(
                            schemaDetail,
                            payload,
                            relations,
                            values.lifeCycle,
                            otherParameters,
                            onError
                        );

                        if (!isNull(partCreateError) || isEmpty(part.data)) {
                            setIsSubmitting(false);
                            setSubmitting(false);
                            return;
                        }
                        onCreateEntitySuccess(part.data);
                    },
                    'info'
                );
                return;
            }
        };

        const { result: part, error: partCreateError }: any = await createEntity(
            schemaDetail,
            payload,
            relations,
            values.lifeCycle,
            null,
            onError
        );

        if (!isNull(partCreateError) || isEmpty(part.data)) {
            setIsSubmitting(false);
            setSubmitting(false);
            return;
        }

        onCreateEntitySuccess(part.data);
    };

    return (
        <Box>
            {isSubmitting && <LoadingOverlay />}
            <Formik
                enableReinitialize
                initialValues={initialValues}
                //validationSchema={validationSchema}
                onSubmit={(values, { setSubmitting, setFieldError }) =>
                    handleSubmit(values, setSubmitting, setFieldError)
                }
            >
                {({ values, setFieldValue, setFieldTouched, errors, touched }) => {
                    return loading ? (
                        <Loading />
                    ) : (
                        <Form id="create-palette-form">
                            {hasMaster && masterSchemaDetail && (
                                <AttributeBuilder
                                    setFieldTouched={setFieldTouched}
                                    setFieldValue={setFieldValue}
                                    schema={masterSchemaDetail}
                                    label="Master Attributes"
                                    errors={errors}
                                    values={values}
                                    touched={touched}
                                />
                            )}
                            <AttributeBuilder
                                setFieldTouched={setFieldTouched}
                                schema={schemaDetail}
                                setFieldValue={setFieldValue}
                                errors={errors}
                                excludedAttributes={masterSchemaDetail?.attributes}
                                label={hasMaster ? 'Revision Attributes' : 'Attributes'}
                                values={values}
                                touched={touched}
                            />

                            {isDocumentType && (
                                <>
                                    <SectionTitle>Upload File</SectionTitle>
                                    <Grid
                                        item
                                        xs={12}
                                        key="file"
                                        sx={{
                                            section: {
                                                width: '100%!important',
                                                boxSizing: 'border-box',
                                            },
                                            p: {},
                                        }}
                                    >
                                        <Dropzone
                                            onDrop={(acceptedFiles) => {
                                                setFieldValue('file', acceptedFiles[0]);
                                                acceptedFiles.map((file) => {
                                                    const reader = new FileReader();
                                                    setFileName(file.name);
                                                    const fileExtension = file.name.split('.').pop().toLowerCase();
                                                    const isImageFile =
                                                        fileExtension &&
                                                        ImagePreview.IMAGE_EXTENSIONS.includes(fileExtension);
                                                    setIsImage(isImageFile);
                                                    reader.onload = function (e) {
                                                        setImagePreview(e.target.result);
                                                    };
                                                    reader.readAsDataURL(file);
                                                    return file;
                                                });
                                            }}
                                        >
                                            {({ getRootProps, getInputProps }) => (
                                                <section style={dropZoneStyle}>
                                                    <div {...getRootProps()} style={{ width: '100%' }}>
                                                        <input {...getInputProps()} />
                                                        <p>Drag and drop document or click here</p>
                                                        <CloudUploadIcon />
                                                    </div>
                                                </section>
                                            )}
                                        </Dropzone>
                                        {!isImage ? (
                                            <Typography
                                                sx={{
                                                    color: (theme) => theme.palette.text.primary,
                                                    my: 1,
                                                }}
                                            >
                                                {fileName}
                                            </Typography>
                                        ) : (
                                            <Box>
                                                <ImagePreview image={imagePreview} />
                                                <Typography
                                                    sx={{
                                                        color: (theme) => theme.palette.text.primary,
                                                        overflowWrap: 'break-word',
                                                        mb: 1,
                                                    }}
                                                >
                                                    {fileName}
                                                </Typography>
                                            </Box>
                                        )}
                                    </Grid>
                                    <Grid item xs={12} key="lock">
                                        <Field
                                            fullWidth
                                            component={CheckboxWithLabel}
                                            type="checkbox"
                                            name="lock"
                                            Label={{ label: 'Lock File' }}
                                        />
                                    </Grid>
                                </>
                            )}
                            <SectionTitle>Lifecycle</SectionTitle>
                            <Grid container spacing={2}>
                                <Grid item xs={12} sm={6} key="lifecycles">
                                    <Field
                                        fullWidth
                                        select
                                        type="text"
                                        variant="outlined"
                                        name="lifeCycle.id"
                                        label="Lifecycle"
                                        size="medium"
                                        component={TextField}
                                        InputLabelProps={{ shrink: true }}
                                        onChange={(e) => handleLifecycleChange(e, setFieldValue)}
                                        disabled={!schemaDetail?.lifecycles || schemaDetail.lifecycles?.length === 0}
                                    >
                                        {schemaDetail?.lifecycles?.map((item) => (
                                            <MenuItem value={item.lifeCycle.id} key={item.lifeCycle.id}>
                                                {item.lifeCycle.name}
                                            </MenuItem>
                                        ))}
                                    </Field>
                                    {schemaDetail?.lifecycles?.length === 0 && (
                                        <ErrorTypography>There's no valid lifecycle to create</ErrorTypography>
                                    )}
                                </Grid>
                                {values?.lifeCycle && (
                                    <Grid item xs={12} sm={6} key="states">
                                        <Field
                                            fullWidth
                                            select
                                            type="text"
                                            variant="outlined"
                                            name="lifeCycle.startState"
                                            label="Start State"
                                            size="medium"
                                            component={TextField}
                                            InputLabelProps={{ shrink: true }}
                                            disabled={
                                                get(canCreateLifeCycleStates, values?.lifeCycle?.id, []).length === 0
                                            }
                                        >
                                            {get(canCreateLifeCycleStates, values?.lifeCycle?.id, []).map((item) => (
                                                <MenuItem value={item.id} key={item.id}>
                                                    {item.name}
                                                </MenuItem>
                                            ))}
                                        </Field>
                                        {get(canCreateLifeCycleStates, values?.lifeCycle?.id, []).length === 0 && (
                                            <ErrorTypography>There's no valid state to create</ErrorTypography>
                                        )}
                                    </Grid>
                                )}
                            </Grid>

                            <SectionTitle>Ownership</SectionTitle>
                            <Field name="owner">
                                {({ meta }) => {
                                    const isError = Boolean(meta?.touched && meta?.error);
                                    return (
                                        <OwnerSelector
                                            onChange={(value: string) => {
                                                setFieldValue('owner', value);
                                            }}
                                            onBlur={() => {
                                                setFieldTouched('owner', true, true);
                                            }}
                                            isError={isError}
                                            helperText={isError ? meta.error : ''}
                                        />
                                    );
                                }}
                            </Field>
                        </Form>
                    );
                }}
            </Formik>
        </Box>
    );
};

export default PaletteForm;
