/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
export { default as Themes } from './themes';
export { default as Loading } from './components/Loading';
export { default as InfiniteLoading } from './components/InfiniteLoading';
export { default as RichTextEditor } from './components/Base/RichTextEditor';
export { default as LoadingOverlay } from './components/LoadingOverlay';
export * from './utils/dateTimeUtils';
export * from './utils/lifecycleUtils';
export * from './utils/export';

const unsecuredCopyToClipboard = (text) => {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
        document.execCommand('copy');
    } catch (err) {
        console.error('Unable to copy to clipboard', err);
    }
    document.body.removeChild(textArea);
};

/**
 * Copies the text passed as param to the system clipboard
 * Check if using HTTPS and navigator.clipboard is available
 * Then uses standard clipboard API, otherwise uses fallback
 */
export const copyToClipboard = (content) => {
    if (window.isSecureContext && navigator.clipboard) {
        navigator.clipboard.writeText(content);
    } else {
        unsecuredCopyToClipboard(content);
    }
};

export function formatBytes(bytes, decimals = 2) {
    if (!+bytes) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}
export { URL_PARAMS } from './components/UrlConstants';
export * from './components/icons/Icons';
export { default as DocThumbnail } from './components/DocThumbnail/DocThumbnail';
export { default as PropertyValueRenderer } from './components/Renderer/PropertyValueRenderer';
export { default as RichTextRenderer, sanitizeRichText } from './components/Renderer/RichtextRenderer';
export { useDebounce } from './utils/useDebounce';
export { FlagIcon } from './components/icons/FlagIcon';
export { NOTIFICATION_EVENT } from './constants/notification';
export {
    notify,
    notifySuccess,
    notifyError,
    persistToast,
    dismiss,
    TOAST_PERSIST_KEYS,
    notifyBusinessRuleErrors,
    notifyBusinessRuleWarnings,
} from './utils/notificationUtils';
export { default as EditEndabledIcon } from './components/icons/EditEndabledIcon';
export { default as TrashLightIcon } from './components/icons/TrashLightIcon';
export { default as ZoomOutIcon } from './components/icons/ZoomOutIcon';
export { default as Notfound } from './components/NotFound';
export { ActivitiesSortIcon } from './components/icons/ActivitiesSortIcon';
export { ConnectIcon } from './components/icons/ConnectIcon';
export { default as AnimatedPage } from './components/Base/AnimatedPage/AnimatedPage';
export { default as NoRowsOverlay } from './components/NoRowsOverlay/NoRowsOverlay';
export { default as TreeList } from './components/TreeList/TreeList';
export { default as commonMessages } from './constants/commonMessages';
export { default as MainTooltip } from './components/Base/Tooltip/MainTooltip';
export { default as TextOverflow } from './components/TextOverflow/TextOverflow';
export { default as TruncatedErrorMessage } from './components/TruncatedErrorMessage/TruncatedErrorMessage';
export {
    filterParams,
    getFilterParamsByType,
    buildQueryBasedOnFilter,
    buildSortParams,
} from './components/AgGrid/Filter';
export { default as ResizableDrawer, type ResizableDrawerProps } from './components/Drawer/ResizableDrawer';
export { DRAWER_COMPONENT_NAME } from './components/Drawer/DrawerConstants';
export {
    buildValidationSchema,
    buildFormItem,
    buildInitialValue,
    buildOwnerSelection,
} from './components/Form/FormBuilder';
export { tableStyles, tableIcons } from './themes/ag-grid/tableStyles';
export { default as EntityNameRenderer } from './components/AgGrid/EntityNameRenderer';
export { default as OwnerRenderer } from './components/AgGrid/OwnerRenderer';
export { default as ChevronDown } from './components/icons/ChevronDown';
export { default as ChevronRight } from './components/icons/ChevronRight';
export { default as UploadForm } from './components/Form/UploadForm';
export { default as AsyncSelect } from './components/AsyncSelect/AsyncSelect';
export { default as EntitySelect } from './components/AsyncSelect/EntitySelect';
export { default as ImagePreview } from './components/ImagePreview';
export { default as Thumbnail } from './components/Thumbnail/Thumbnail';
export { default as EntityNameLoader } from './components/EntityNameLoader';

export { checkingCanUnlock, checkingPermission, isEntityLocked } from './utils/checkingPermission';
export * from './constants/common';
export * from './utils/helper';
export * from './components/Dialog';
export { default as Scheduler } from './components/Scheduler/Scheduler';
export * from './components/Scheduler/Scheduler';
export { default as ErrorBoundary } from './components/ErrorBoundary';
export { default as OwnerSelector } from './components/OwnerSelector/OwnerSelector';
export { default as TablePagination } from './components/Pagination/Pagination';
export { default as RecipientSelector } from './components/RecipientSelector';
export * from './components/RecipientSelector';

export * from './components/OwnerSelector/OwnerSelector';
export * from './components/TextOverflow/OverflowingContentRenderer';
export { default as DataTypeProperty } from './components/Renderer/DataTypeProperty';
