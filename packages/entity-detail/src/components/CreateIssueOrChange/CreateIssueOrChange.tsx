/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import { Box, Button, Grid, Typography } from '@mui/material';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
    Loading,
    buildValidationSchema,
    persistToast,
    dismiss,
    DRAWER_COMPONENT_NAME,
    LoadingOverlay,
    buildOwnerSelection,
    OwnerSelector,
    useDialogStore,
} from '@tripudiotech/styleguide';
import {
    entityUrls,
    changeUrls,
    SYSTEM_ENTITY_TYPE,
    SYSTEM_RELATION,
    fetch,
    Attribute,
    AttributeType,
    RESPONSE_ERROR_TYPE,
} from '@tripudiotech/api';
import { useSelector } from 'react-redux';
import { selectAppReducer } from '../../selectors';
import isNil from 'lodash/isNil';
import isEmpty from 'lodash/isEmpty';
import { Form, Formik, Field } from 'formik';
import { CheckboxWithLabel } from 'formik-mui';
import get from 'lodash/get';
import { useNavigate, useSearchParams } from 'react-router-dom';
import RightTray from '../Relations/RightTray';
import { getUserIdFromUserInfo, useAgent, useAuth, useDraftForm, useSchemaDetail } from '@tripudiotech/caching-store';
import { isChangeRequest, isIssueEntity } from '../../utils/helper';
import AttributeBuilder from '../AttributeDetail/AttributeBuilder';

const TITLE = {
    [SYSTEM_ENTITY_TYPE.ISSUE]: 'Report an Issue',
    [SYSTEM_ENTITY_TYPE.CHANGE_REQUEST]: 'Create a Change Request',
    [SYSTEM_ENTITY_TYPE.CHANGE_ORDER]: 'Create a Change Order',
};

const buildFormRequest = (detailEntity, changeType, formValues) => {
    switch (changeType) {
        case SYSTEM_ENTITY_TYPE.ISSUE:
            return {
                ...entityUrls.createEntity,
                params: { entityType: SYSTEM_ENTITY_TYPE.ISSUE },
                data: {
                    attributes: formValues,
                    relations: [
                        {
                            name: SYSTEM_RELATION.REPORTED_ON,
                            entityId: detailEntity.id,
                            entityType: detailEntity.properties.type,
                        },
                        {
                            name: SYSTEM_RELATION.AFFECTED_ITEM,
                            entityId: detailEntity.id,
                            entityType: detailEntity.properties.type,
                        },
                        {
                            name: SYSTEM_RELATION.OWNED_BY,
                            entityId: formValues?.owner,
                            entityType: SYSTEM_ENTITY_TYPE.AGENT,
                        },
                    ],
                },
                successMessage: (
                    <span>
                        Issue <b>{formValues.name}</b> for the entity <b>{detailEntity.properties.name}</b> has been
                        created successfully
                    </span>
                ),
            };
        case SYSTEM_ENTITY_TYPE.CHANGE_REQUEST:
            return {
                ...entityUrls.createEntity,
                params: { entityType: SYSTEM_ENTITY_TYPE.CHANGE_REQUEST },
                data: {
                    attributes: formValues,
                    relations: [
                        {
                            name: SYSTEM_RELATION.AFFECTED_ITEM,
                            entityId: detailEntity.id,
                            entityType: detailEntity.properties.type,
                        },
                        {
                            name: SYSTEM_RELATION.OWNED_BY,
                            entityId: formValues?.owner,
                            entityType: SYSTEM_ENTITY_TYPE.AGENT,
                        },
                    ],
                },
                successMessage: (
                    <span>
                        Change Request <b>{formValues.name}</b> for the entity <b>{detailEntity.properties.name}</b> has
                        been created successfully
                    </span>
                ),
            };
        case SYSTEM_ENTITY_TYPE.CHANGE_ORDER:
            const { isCopyAllAffectedItems: copyAffectedItem, ...restOfValues } = formValues;
            const successMessage = (
                <span>
                    Change Order <b>{formValues.name}</b> for the entity <b>{detailEntity.properties.name}</b> has been
                    created successfully
                </span>
            );

            const fromEntityType = get(detailEntity, 'properties.type', '');

            if (isChangeRequest(fromEntityType)) {
                return {
                    ...changeUrls.createChangeOrder,
                    data: {
                        attributes: restOfValues,
                        relations: [
                            {
                                name: SYSTEM_RELATION.IMPLEMENTS,
                                entityId: detailEntity.id,
                                entityType: detailEntity.properties.type,
                            },
                            {
                                name: SYSTEM_RELATION.OWNED_BY,
                                entityId: formValues?.owner,
                                entityType: SYSTEM_ENTITY_TYPE.AGENT,
                            },
                        ],
                        copyAffectedItem,
                    },
                    successMessage,
                };
            }

            if (isIssueEntity(fromEntityType)) {
                return {
                    ...entityUrls.createEntity,
                    params: { entityType: SYSTEM_ENTITY_TYPE.CHANGE_ORDER },
                    data: {
                        attributes: restOfValues,
                        relations: [
                            {
                                name: SYSTEM_RELATION.RESOLVED_BY,
                                entityId: detailEntity.id,
                                entityType: detailEntity.properties.type,
                            },
                            {
                                name: SYSTEM_RELATION.OWNED_BY,
                                entityId: formValues?.owner,
                                entityType: SYSTEM_ENTITY_TYPE.AGENT,
                            },
                        ],
                    },
                    successMessage,
                };
            }

            return {
                ...entityUrls.createEntity,
                params: { entityType: SYSTEM_ENTITY_TYPE.CHANGE_ORDER },
                data: {
                    attributes: restOfValues,
                    relations: [
                        {
                            name: SYSTEM_RELATION.AFFECTED_ITEM,
                            entityId: detailEntity.id,
                            entityType: detailEntity.properties.type,
                        },
                        {
                            name: SYSTEM_RELATION.OWNED_BY,
                            entityId: formValues?.owner,
                            entityType: SYSTEM_ENTITY_TYPE.AGENT,
                        },
                    ],
                },
                successMessage,
            };
    }
};

const buildDraftFormInfo = (detailEntity, formValues, changeType, formId) => {
    const type = get(detailEntity, ['properties', 'type']);
    const name = get(detailEntity, ['properties', 'name']);
    const entityId = get(detailEntity, ['id']);

    switch (changeType) {
        case SYSTEM_ENTITY_TYPE.ISSUE:
            return {
                id: formId,
                type: changeType,
                title: 'Continue reporting issue',
                content: `You are reporting an issue on the entity <b>${name}</b>`,
                formValues,
                viewLocation: {
                    contextPath: `/detail/${type}/${entityId}`,
                    fullPath: `/detail/${type}/${entityId}/properties`,
                    query: `?draftForm=${formId}`,
                },
            };
        case SYSTEM_ENTITY_TYPE.CHANGE_REQUEST:
            return {
                id: formId,
                type: changeType,
                title: 'Continue creating change request',
                content: `You are creating a change request for the entity <b>${name}</b>`,
                formValues,
                viewLocation: {
                    contextPath: `/detail/${type}/${entityId}`,
                    fullPath: `/detail/${type}/${entityId}/properties`,
                    query: `?draftForm=${formId}`,
                },
            };
        case SYSTEM_ENTITY_TYPE.CHANGE_ORDER:
            return {
                id: formId,
                type: changeType,
                title: 'Continue creating change order',
                content: `You are creating a change order for the entity <b>${name}</b>`,
                formValues,
                viewLocation: {
                    contextPath: `/detail/${type}/${entityId}`,
                    fullPath: `/detail/${type}/${entityId}/properties`,
                    query: `?draftForm=${formId}`,
                },
            };
    }
};

const CreateIssueOrChange = ({ open, onOpen, onClose, formId = '', type, detailEntityProp }) => {
    const appSelector = useSelector(selectAppReducer);
    const { onOpenDialog } = useDialogStore();
    const detailEntity = detailEntityProp ?? appSelector.detailEntity;
    const isCreateFormCR = isChangeRequest(detailEntity?.properties?.type);
    const [schema, isSchemaLoading, getSchema, getAttributeName] = useSchemaDetail(
        ({ schema, isLoading, getSchema, getAttributeName }) => [schema, isLoading, getSchema, getAttributeName]
    );
    const issueSchema = schema[type];
    const [owner, setOwner] = useState([]);
    const userInfo = useAuth((state) => state.userInfo);
    const userId = useMemo(() => {
        return getUserIdFromUserInfo(userInfo);
    }, [userInfo]);
    const [forms] = useDraftForm(({ forms, removeForm }) => [forms, removeForm]);

    const formRef = useRef(null);
    const [searchParams, setSearchParams] = useSearchParams();
    const draftForm = searchParams.get('draftForm');
    const [draftValues, setDraftValues] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const navigate = useNavigate();
    const entityName = get(detailEntity, ['properties', 'name']);

    const { companies: agentCompanies, departments: agentDepartments, teams: agentTeams, getAgents } = useAgent();
    useEffect(() => {
        if (agentTeams) {
            const options = buildOwnerSelection(agentCompanies, agentTeams, agentDepartments);
            setOwner(options);
        }
    }, [agentTeams]);

    useEffect(() => {
        if (userId && open) {
            getAgents(userId);
        }
    }, [userId, open]);
    const handleClose = (e?, reason?) => {
        onClose();
    };

    const filterAndSortAttributes = (attributes: Record<string, Attribute>, attributeOrder: string[]) => {
        return Object.values(attributes)
            .filter((attr) => attr.visible && isEmpty(attr.identifier))
            .sort((a, b) => {
                if (!attributeOrder) return a.name.localeCompare(b.name);
                return attributeOrder.indexOf(a.name) - attributeOrder.indexOf(b.name);
            });
    };

    const form = useMemo(() => {
        if (isNil(issueSchema)) {
            return {};
        }
        const {
            attributes,
            entityType: { attributeOrder },
        } = issueSchema;
        const allAttributes = filterAndSortAttributes(attributes, attributeOrder);
        let initialValues = Object.fromEntries(
            allAttributes.map((attribute) => {
                if (get(attribute, ['constraint', 'enumRange'], false)) {
                    return [attribute.name, attribute.defaultValue || null];
                }
                return [attribute.name, attribute.defaultValue || null];
            })
        );
        const validationSchema = buildValidationSchema([
            ...allAttributes,
            {
                name: 'owner',
                type: AttributeType.STRING,
                displayName: 'Owner',
                nullable: false,
                hyperlink: false,
                constraint: null,
                isMulti: false,
                unitOfMeasure: null,
            },
        ]);

        if (isCreateFormCR) {
            initialValues = { ...initialValues, isCopyAllAffectedItems: false };
        }
        if (detailEntity?.owner?.id && owner?.some((option) => option.id === detailEntity.owner.id)) {
            initialValues = {
                ...initialValues,
                owner: detailEntity.owner.id,
            };
        }

        return {
            allAttributes,
            initialValues,
            validationSchema,
        };
    }, [issueSchema, detailEntity, owner]);

    const handleSubmit = async (values, { setFieldError }) => {
        const request = buildFormRequest(detailEntity, type, values);
        try {
            setIsLoading(true);
            const { data } = await fetch(request);
            handleClose();
            navigate(`/detail/${type}/${data.id}/properties`);
        } catch (error) {
            const invalidAttributeErrors = error.response.data?.errors?.filter(
                (e: any) => e?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE
            );
            if (invalidAttributeErrors?.length) {
                invalidAttributeErrors.forEach((errItem: any) => {
                    const invalidAttributeId = errItem?.metadata?.data;
                    const invalidAttributeName = getAttributeName(issueSchema, invalidAttributeId);

                    if (invalidAttributeName) {
                        setFieldError(invalidAttributeName, errItem?.errorDetail);
                    }
                });
            }
            // Handle confirmation on business rule warnings
            const rules = error.response.data.businessRuleResults;
            const warningHandShakeId = error.response.data.otherParameters?.warningHandShakeId;
            if (rules?.length > 0 && warningHandShakeId) {
                const warningListHtml = `
                <div>
                    <p> Some business rules were violated</p>
                    <ul style="margin: 4px;">
                        ${rules.map((rule) => `<li>${rule.message}</li>`).join('')}
                    </ul>
                    <p><strong>Do you still want to proceed?</strong></p>
                </div>
            `;
                const requestWithHandshake = { ...request };
                requestWithHandshake.data['otherParameters'] = {
                    warningHandShakeId: warningHandShakeId,
                };
                onOpenDialog(
                    'Review Warnings',
                    warningListHtml,
                    async () => {
                        try {
                            setIsLoading(true);
                            const { data } = await fetch(requestWithHandshake);
                            handleClose();
                            navigate(`/detail/${type}/${data.id}/properties`);
                        } catch (err) {
                            if (err.response?.status === 400) {
                                const invalidAttributeErrors = err.response.data?.errors?.filter(
                                    (e: any) => e?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE
                                );
                                if (invalidAttributeErrors?.length) {
                                    invalidAttributeErrors.forEach((errItem: any) => {
                                        const invalidAttributeId = errItem?.metadata?.data;
                                        const invalidAttributeName = getAttributeName(issueSchema, invalidAttributeId);

                                        if (invalidAttributeName) {
                                            setFieldError(invalidAttributeName, errItem?.errorDetail);
                                        }
                                    });
                                }
                            }
                        } finally {
                            setIsLoading(false);
                        }
                    },
                    'info'
                );
                return;
            }
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (open) {
            getSchema(type);
        }
        if (!open) {
            setDraftValues(null);
        }
    }, [open, type]);

    const handleSaveDraft = () => {
        const formValues = formRef.current.values;
        persistToast(buildDraftFormInfo(detailEntity, formValues, type, formId), {
            autoClose: false,
            position: 'bottom-left',
            closeOnClick: false,
        });
        handleClose();
    };

    useEffect(() => {
        if (draftForm !== formId) return;
        if (draftForm) {
            const draft = forms.find((form) => form.id === formId);
            searchParams.delete('draftForm');
            dismiss(formId);
            setSearchParams(searchParams);
            onOpen();
            if (draft?.formValues) {
                setDraftValues(draft.formValues);
            }
        }
    }, [draftForm]);
    return (
        <RightTray
            onClose={onClose}
            title={entityName ? `${TITLE[type]} for ${entityName}` : TITLE[type]}
            open={open}
            hideConfirm
            componentName={DRAWER_COMPONENT_NAME.CREATE_CHANGE}
            disableCloseOnClickOutside
            disableEscapeKeyDown
        >
            {isLoading && <LoadingOverlay />}
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%',
                    overflow: 'hidden',
                }}
            >
                <Box sx={{ flexGrow: 1, p: '24px', overflowY: 'auto' }}>
                    {isSchemaLoading[type] ? (
                        <Loading />
                    ) : (
                        <Formik
                            enableReinitialize
                            initialValues={draftValues || form.initialValues}
                            validationSchema={form.validationSchema}
                            innerRef={formRef}
                            onSubmit={(values, { setFieldError }) => {
                                handleSubmit(values, { setFieldError });
                            }}
                        >
                            {({ values, errors, setFieldValue, setFieldTouched, ...rest }) => {
                                return (
                                    <Form id="create-issue-form">
                                        <Grid container spacing="12px">
                                            <AttributeBuilder
                                                setFieldTouched={setFieldTouched}
                                                schema={issueSchema}
                                                setFieldValue={setFieldValue}
                                                errors={errors}
                                                values={values}
                                            />
                                            {isCreateFormCR && (
                                                <Grid item xs={12}>
                                                    <Field
                                                        fullWidth
                                                        component={CheckboxWithLabel}
                                                        type="checkbox"
                                                        name="isCopyAllAffectedItems"
                                                        Label={{
                                                            label: 'Propagate planned change from ECR',
                                                        }}
                                                        size="small"
                                                    />
                                                </Grid>
                                            )}
                                            <Typography
                                                sx={{
                                                    width: '100%',
                                                    fontWeight: 500,
                                                    lineHeight: '18px',
                                                    fontSize: '14px',
                                                    color: (theme) => theme.palette.info.main,
                                                    margin: '16px',
                                                    marginBottom: 0,
                                                }}
                                            >
                                                Ownership
                                            </Typography>
                                            <Grid item xs={12}>
                                                <Field name="owner">
                                                    {({ field, form, meta }) => {
                                                        const isError = Boolean(meta?.touched && meta?.error);
                                                        return (
                                                            <OwnerSelector
                                                                value={field.value}
                                                                onChange={(value: string) => {
                                                                    setFieldValue('owner', value);
                                                                }}
                                                                onBlur={() => form.setFieldTouched('owner', true, true)}
                                                                isError={isError}
                                                                helperText={isError ? meta.error : ''}
                                                            />
                                                        );
                                                    }}
                                                </Field>
                                            </Grid>
                                        </Grid>
                                    </Form>
                                );
                            }}
                        </Formik>
                    )}
                </Box>
                <Box
                    sx={{
                        marginTop: 'auto',
                        p: '24px',
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between',
                    }}
                >
                    <Button className="actionBtn" variant="ghost" size="large" onClick={handleClose}>
                        Cancel
                    </Button>
                    <Box sx={{ display: 'flex', gap: '8px', justifyContent: 'flex-start' }}>
                        <Button
                            className="actionBtn"
                            variant="contained"
                            color="secondary"
                            size="large"
                            onClick={handleSaveDraft}
                            sx={{ minWidth: '120px', justifyContent: 'flex-start' }}
                        >
                            Save draft
                        </Button>
                        <Button
                            onClick={() => formRef.current.submitForm()}
                            className="actionBtn"
                            variant="contained"
                            color="primary"
                            size="large"
                            sx={{ minWidth: '180px', justifyContent: 'flex-start' }}
                        >
                            Create
                        </Button>
                    </Box>
                </Box>
            </Box>
        </RightTray>
    );
};

export default CreateIssueOrChange;
