/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE." GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */
import React, { useEffect, useMemo, useRef, memo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import { useDispatch } from 'react-redux';

import EntityProperty from './EntityProperty';
import ApplicationField from './ApplicationField';
import ViewMaster from './ViewMaster';
import Status from './Status';
import { AttributeType, AxiosResponse, PERMISSION, RESPONSE_ERROR_TYPE } from '@tripudiotech/api';
import {
    MainTooltip,
    commonMessages,
    checkingPermission,
    ExpandMoreIcon,
    getStatus,
    useDialogStore,
} from '@tripudiotech/styleguide';
import { useAuth, useSchemaDetail, useSchemaTree } from '@tripudiotech/caching-store';
import IconButton from '@mui/material/IconButton';
import EditPropertiesPanel from './EditProperties';
import get from 'lodash/get';
import { EditIcon } from '../icons/icons';
import useToggle from '../../hooks/useToggle';
import { updateAttributes, updateDetailEntity } from '../../actions';
import { sortAttributes } from '../../utils/helper';
import { useUIConfiguration } from '../../store/useUIConfiguration';
import { AccordionDetails, AccordionSummary, FormControlLabel, Switch, Typography } from '@mui/material';
import OverflowMenu from '../Navigation/OverflowMenu';
import AttributeRender from './AttributeRender';
import { StyledAccordion } from './ClassificationAttributes';

const fixedFields = [
    {
        name: 'owner.name',
        displayName: 'Owner',
        type: AttributeType.STRING,
    },
    {
        name: 'authoredIn.name',
        displayName: 'Authoring Application',
    },
];

const getFixedFields = (data: any, viewMore: boolean) => {
    return fixedFields
        .map((field) => {
            return { ...field, value: get(data, field.name) };
        })
        .filter((field) => viewMore || field.name !== 'properties.type');
};

interface ActionItem {
    label: string;
    icon: React.ReactNode;
    onClick: () => void;
    title?: string;
    disabled?: boolean;
    renderer: () => React.ReactNode;
}

interface AttributesLayoutProps {
    title: string;
    viewMore?: boolean;
    detailUrl?: string;
    children: React.ReactNode;
    isReadOnly?: boolean;
    canModify?: boolean;
    onEdit?: () => void;
    customActions?: ActionItem[];
    hidePropertyDescriptions?: boolean;
}

export const AttributesLayout: React.FC<AttributesLayoutProps> = ({
    title,
    viewMore = false,
    detailUrl = '',
    children,
    isReadOnly = false,
    canModify = false,
    onEdit = () => {},
    customActions = [],
    hidePropertyDescriptions = false,
}) => {
    const navigate = useNavigate();
    const { isShowPropertyDescription, setIsShowPropertyDescription } = useUIConfiguration();

    const actionItems: ActionItem[] = useMemo(() => {
        return [
            {
                label: isShowPropertyDescription ? 'Hide Property Description' : 'Show Property Description',
                icon: <div />,
                onClick: () => setIsShowPropertyDescription(!isShowPropertyDescription),
                renderer: () => {
                    return (
                        !hidePropertyDescriptions && (
                            <FormControlLabel
                                key="toggle-property-description"
                                sx={{
                                    fontSize: '0.75em',
                                    fontWeight: 400,
                                    gap: '8px',
                                    color: (theme) => theme.palette.glide.text.tertiary,
                                    ml: 0,
                                }}
                                label="Property Description"
                                control={
                                    <Switch
                                        size="small"
                                        checked={isShowPropertyDescription}
                                        onChange={() => setIsShowPropertyDescription(!isShowPropertyDescription)}
                                    />
                                }
                            />
                        )
                    );
                },
            },
            ...customActions,
            {
                label: 'Edit Properties',
                icon: <EditIcon />,
                onClick: onEdit,
                title: canModify ? '' : commonMessages.noPermission,
                disabled: !canModify,
                renderer: () =>
                    !isReadOnly && (
                        <MainTooltip key="edit-props" title={canModify ? '' : commonMessages.noPermission}>
                            <span>
                                <IconButton
                                    color="primary"
                                    sx={{ color: (theme) => theme.palette.glide.text.normal.main, padding: 0 }}
                                    size="xs"
                                    onClick={onEdit}
                                    disabled={!canModify}
                                >
                                    <EditIcon />
                                </IconButton>
                            </span>
                        </MainTooltip>
                    ),
            },
        ];
    }, [canModify, onEdit, isShowPropertyDescription]);

    return (
        <Box sx={{ m: '16px' }}>
            <Box
                sx={{
                    display: 'flex',
                    width: '100%',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '8.5px',
                }}
            >
                <Typography variant="ol1">{title}</Typography>
                <Box sx={{ display: 'flex', gap: '4px' }}>
                    {!isReadOnly && (
                        <>
                            <Box sx={{ display: { lg: 'flex', xs: 'none' }, flexWrap: 'nowrap', gap: '4px' }}>
                                {actionItems.map((action) => action.renderer())}
                            </Box>
                            <Box sx={{ display: { lg: 'none', xs: 'block' } }}>
                                <OverflowMenu items={actionItems} />
                            </Box>
                        </>
                    )}
                    {viewMore && (
                        <Button
                            variant="outlined"
                            color="info"
                            size="small"
                            sx={{
                                marginLeft: 'auto',
                            }}
                            onClick={() => navigate(detailUrl)}
                        >
                            View More
                        </Button>
                    )}
                </Box>
            </Box>
            <Box sx={{ height: '100%' }} className="detail-content">
                {children}
            </Box>
        </Box>
    );
};

const AttributeDetail = ({
    title,
    data,
    detailSchema,
    hasMasterEntity = false,
    hideMasterToggle = false,
    viewMore = false,
    onChangeView = (e) => {},
    showMaster = true,
    isReadOnly = false,
    subContent = undefined,
    isDocument = false,
    propertyGrid = { xs: 12, sm: 6, md: 6, lg: 4 },
    updateDetailEntityDispatch = updateDetailEntity,
    customActions = [],
    showName = false,
    hidePropertyDescriptions = false,
}) => {
    if (!data || !detailSchema) {
        return null;
    }

    const userInfo = useAuth((state) => state.userInfo);
    const distpatch = useDispatch();
    const { properties, id: entityId } = data;
    const { schemaTreeMap } = useSchemaTree();
    const { onOpenDialog } = useDialogStore();

    const fixedAttributes = useMemo(
        () => [
            {
                name: 'properties.type',
                displayName: 'Type',
                value: schemaTreeMap?.[data?.properties?.type]?.displayName || data?.properties?.type,
                type: AttributeType.STRING,
            },
            ...getFixedFields(data, viewMore),
        ],
        [data, viewMore, schemaTreeMap]
    );
    const { isShowPropertyDescription } = useUIConfiguration();
    const getAttributeName = useSchemaDetail((state) => state.getAttributeName);
    const appliedClassifications = useRef<string[]>();

    useEffect(() => {
        // To avoid closure state, since detailEntity is not a dependency of EditPropertiesPanel component
        appliedClassifications.current = data.classifications.map((classification) => classification.name);
    }, [data.classifications]);

    const dynamicAttributes = useMemo(() => {
        return sortAttributes(detailSchema, properties);
    }, [detailSchema, properties]);

    const entityStatus = useMemo(() => getStatus(data?.state), [data]);
    const [openEditor, editorAction] = useToggle(false);

    const handleChangeView = (e: any) => {
        onChangeView(e);
    };

    const handleSave = async (values, { setFieldError }) => {
        const payload = { attributes: values, classifications: appliedClassifications.current };
        const successMessage = (
            <span>
                Updated attributes for <b>{properties.name}</b> successfully
            </span>
        );

        const onError = (error: AxiosResponse) => {
            const invalidAttributeErrors = error.data?.errors?.filter(
                (e: any) => e?.metadata?.type === RESPONSE_ERROR_TYPE.INVALID_ATTRIBUTE_VALUE
            );
            if (error.status === 400 && invalidAttributeErrors?.length) {
                invalidAttributeErrors.forEach((errItem: any) => {
                    const invalidAttributeId = errItem?.metadata?.data;
                    const invalidAttributeName = getAttributeName(detailSchema, invalidAttributeId);

                    if (invalidAttributeName) {
                        setFieldError(invalidAttributeName, errItem?.errorDetail);
                    }
                });
            }
            // Handle confirmation on business rule warning
            const rules = error.data?.businessRuleResults;
            const warningHandShakeId = error.data?.otherParameters?.warningHandShakeId;
            if (rules?.length > 0 && warningHandShakeId) {
                const warningListHtml = `
                    <div>
                        <p> Some business rules were violated</p>
                        <ul style="margin: 4px;">
                            ${rules.map((rule) => `<li>${rule.message}</li>`).join('')}
                        </ul>
                        <p><strong>Do you still want to proceed?</strong></p>
                    </div>
                `;
                const payloadWithHandsake = {
                    ...payload,
                    otherParameters: {
                        warningHandShakeId: warningHandShakeId,
                    },
                };
                onOpenDialog(
                    'Review Warnings',
                    warningListHtml,
                    async () => {
                        const status = await distpatch(
                            updateAttributes(
                                entityId,
                                payloadWithHandsake,
                                updateDetailEntityDispatch,
                                successMessage,
                                onError
                            )
                        );
                        if (status) editorAction.close();
                    },
                    'info'
                );
            }
        };

        const status = await distpatch(
            updateAttributes(entityId, payload, updateDetailEntityDispatch, successMessage, onError)
        );

        if (status) editorAction.close();
    };

    const canModify = useMemo(() => {
        return checkingPermission(data, userInfo, PERMISSION.CAN_MODIFY);
    }, [data, userInfo]);

    return (
        <AttributesLayout
            title={title}
            viewMore={viewMore}
            detailUrl={`/detail/${properties.type}/${entityId}/properties`}
            isReadOnly={isReadOnly}
            canModify={canModify as boolean}
            onEdit={editorAction.open}
            customActions={customActions}
            hidePropertyDescriptions={hidePropertyDescriptions}
        >
            <Box className="detail-content">
                <Box className="basic-infos">
                    {subContent}
                    <Grid container spacing={2}>
                        {showName && (
                            <Grid
                                item
                                xs={propertyGrid.xs}
                                sm={propertyGrid.sm}
                                md={propertyGrid.md}
                                lg={propertyGrid.lg}
                                sx={{ zIndex: 1 }}
                            >
                                <EntityProperty
                                    sx={{
                                        '& a, a:visited, a:active': {
                                            color: (theme) => theme.palette.info.main,
                                            fontSize: '14px',
                                            fontWeight: 500,
                                        },
                                        marginBottom: '8.5px',
                                    }}
                                    label="Name"
                                >
                                    <Link to={`/detail/${data?.properties?.type}/${data?.id}/properties`}>
                                        {data?.properties?.name}
                                    </Link>
                                </EntityProperty>
                            </Grid>
                        )}
                        <Grid
                            item
                            xs={propertyGrid.xs}
                            sm={propertyGrid.sm}
                            md={propertyGrid.md}
                            lg={propertyGrid.lg}
                            sx={{ zIndex: 1 }}
                        >
                            <EntityProperty
                                sx={{
                                    '& a, a:visited, a:active': {
                                        color: (theme) => theme.palette.info.main,
                                        fontSize: '14px',
                                        fontWeight: 500,
                                    },
                                    marginBottom: '8.5px',
                                }}
                                label="Lifecycle"
                            >
                                <Link to={`/detail/${data?.properties?.type}/${data?.id}/lifecycle`}>
                                    {data?.lifecycle?.name}
                                </Link>
                            </EntityProperty>
                        </Grid>
                        {viewMore && (
                            <Grid
                                item
                                xs={propertyGrid.xs}
                                sm={propertyGrid.sm}
                                md={propertyGrid.md}
                                lg={propertyGrid.lg}
                            >
                                <Status status={entityStatus.status} label={data?.state?.name} />
                            </Grid>
                        )}
                        {!hideMasterToggle && hasMasterEntity && (
                            <Grid item xs={4} sx={{ zIndex: 1 }}>
                                <ViewMaster
                                    label="Master Detail"
                                    helpText="Toogle to view Master Detail"
                                    checked={showMaster}
                                    onChange={handleChangeView}
                                />
                            </Grid>
                        )}

                        {/* <Grid item xs={12} sx={{ zIndex: 1 }}>
                            {dynamicAttributes
                                .filter((attr) => attr.name === 'description')
                                .map((attr) => (
                                    <AttributeRender
                                        key={attr.id}
                                        attribute={attr}
                                        hideDescription={!isShowPropertyDescription}
                                    />
                                ))}
                        </Grid> */}

                        <Grid item xs={12} sx={{ paddingTop: '0!important' }}>
                            <Grid container spacing={3}>
                                {/* Fixed attributes */}
                                <Grid container item spacing={3}>
                                    {fixedAttributes.map((attr) => (
                                        <Grid
                                            item
                                            xs={propertyGrid.xs}
                                            sm={propertyGrid.sm}
                                            md={propertyGrid.md}
                                            lg={propertyGrid.lg}
                                            key={attr.name}
                                        >
                                            {attr.name === 'application' ? (
                                                <ApplicationField
                                                    label={attr.displayName}
                                                    value={attr.value}
                                                    sx={{ marginBottom: 0 }}
                                                />
                                            ) : (
                                                <AttributeRender
                                                    attribute={attr}
                                                    hideDescription={!isShowPropertyDescription}
                                                    sx={{ marginBottom: 0 }}
                                                />
                                            )}
                                        </Grid>
                                    ))}
                                </Grid>
                                {/* Dynamic attributes */}
                                {dynamicAttributes.map((attr: any) => {
                                    if (attr.type === AttributeType.GROUP) {
                                        return (
                                            <Grid item xs={12} key={`${AttributeType.GROUP}-${attr.name}`}>
                                                <StyledAccordion defaultExpanded>
                                                    <AccordionSummary
                                                        className="summary"
                                                        expandIcon={
                                                            <ExpandMoreIcon
                                                                sx={{
                                                                    width: '16px',
                                                                    height: '16px',
                                                                }}
                                                            />
                                                        }
                                                    >
                                                        <Typography className="label" variant="label2-med">
                                                            {attr.name}
                                                        </Typography>
                                                    </AccordionSummary>
                                                    <AccordionDetails className="accordionDetails">
                                                        <Grid container spacing={2}>
                                                            {attr.attributes.map((groupAttr) => (
                                                                <Grid
                                                                    item
                                                                    xs={propertyGrid.xs}
                                                                    sm={propertyGrid.sm}
                                                                    md={propertyGrid.md}
                                                                    lg={propertyGrid.lg}
                                                                    key={groupAttr.id}
                                                                >
                                                                    <AttributeRender
                                                                        sx={{ marginBottom: 0 }}
                                                                        attribute={groupAttr}
                                                                        hideDescription={!isShowPropertyDescription}
                                                                    />
                                                                </Grid>
                                                            ))}
                                                        </Grid>
                                                    </AccordionDetails>
                                                </StyledAccordion>
                                            </Grid>
                                        );
                                    } else {
                                        return (
                                            <Grid
                                                item
                                                xs={propertyGrid.xs}
                                                sm={propertyGrid.sm}
                                                md={propertyGrid.md}
                                                lg={propertyGrid.lg}
                                                key={attr.id}
                                            >
                                                <AttributeRender
                                                    sx={{ marginBottom: 0 }}
                                                    attribute={attr}
                                                    hideDescription={!isShowPropertyDescription}
                                                />
                                            </Grid>
                                        );
                                    }
                                })}
                            </Grid>
                        </Grid>
                    </Grid>
                </Box>
            </Box>
            <EditPropertiesPanel
                open={openEditor}
                onClose={editorAction.close}
                onSave={handleSave}
                attributes={dynamicAttributes}
                title={'Edit ' + data.properties.name}
                entityType={get(detailSchema, ['entityType', 'displayName'])}
            />
        </AttributesLayout>
    );
};

export default memo(AttributeDetail);
