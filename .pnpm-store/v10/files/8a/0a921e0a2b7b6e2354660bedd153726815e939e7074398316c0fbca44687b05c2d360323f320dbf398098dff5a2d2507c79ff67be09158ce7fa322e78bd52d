import { WParagraphFormat } from '../format/paragraph-format';
import { WSectionFormat } from '../format/section-format';
import { WCharacterFormat } from '../format/character-format';
import { WListFormat } from '../format/list-format';
import { WListLevel } from '../list/list-level';
import { EditorHistory } from '../index';
import { IWidget, FieldElementBox, TableWidget, TableRowWidget, BookmarkElementBox, EditRangeStartElementBox, CommentElementBox, FormField } from '../viewer/page';
import { DocumentEditor } from '../../document-editor';
import { Action } from '../../index';
import { TextPosition } from '../index';
import { ElementBox } from '../viewer/page';
import { WTableFormat, WRowFormat, WCellFormat } from '../format/index';
import { DocumentHelper } from '../viewer';
import { ProtectionType } from '../../base/types';
/**
 * @private
 */
export declare class BaseHistoryInfo {
    private ownerIn;
    documentHelper: DocumentHelper;
    private actionIn;
    private removedNodesIn;
    private modifiedPropertiesIn;
    private modifiedNodeLength;
    private selectionStartIn;
    private selectionEndIn;
    private insertPositionIn;
    private endPositionIn;
    private currentPropertyIndex;
    private ignoredWord;
    insertedText: string;
    insertedData: ImageData;
    type: string;
    headerFooterStart: number;
    headerFooterEnd: number;
    private tableRelatedLength;
    insertNodesIn: IWidget[];
    cellOperation: Operation[];
    fieldBegin: FieldElementBox;
    private startIndex;
    private insertIndex;
    private endIndex;
    ignoreStartOffset: boolean;
    insertedElement: ElementBox;
    splittedRevisions: MarkerData[];
    isAcceptOrReject: string;
    insertedNodes: IWidget[];
    pasteContent: string;
    private insertedTableFormat;
    private insertedRowFormat;
    private insertedCellFormat;
    private insertedParagraphFormat;
    private insertedCharacterFormat;
    private insertedSectionFormat;
    insertedFormat: Object;
    /**
     * @private
     */
    lastElementRevision: ElementBox;
    /**
     * @private
     */
    endRevisionLogicalIndex: string;
    /**
     * @private
     */
    markerData: MarkerData[];
    /**
     * @private
     */
    formFieldType: string;
    /**
     * @private
     */
    isEditHyperlink: boolean;
    /**
     * @private
     */
    dropDownIndex: number;
    readonly owner: DocumentEditor;
    readonly editorHistory: EditorHistory;
    action: Action;
    readonly modifiedProperties: Object[];
    readonly removedNodes: IWidget[];
    selectionStart: string;
    selectionEnd: string;
    insertPosition: string;
    endPosition: string;
    constructor(node: DocumentEditor);
    private readonly viewer;
    updateSelection(): void;
    setBookmarkInfo(bookmark: BookmarkElementBox): void;
    setFormFieldInfo(field: FieldElementBox, value: string | number | boolean): void;
    setEditRangeInfo(editStart: EditRangeStartElementBox): void;
    private revertFormTextFormat;
    private revertFormField;
    private revertBookmark;
    private revertComment;
    private revertEditRangeRegion;
    revert(): void;
    private highlightListText;
    private removeContent;
    updateEndRevisionInfo(): void;
    private retrieveEndPosition;
    /**
     * Method to retrieve exact spitted node which is marked as last available element.
     *
     * @param {ElementBox} elementBox - Specifies the element box
     * @returns {ElementBox} - Returns element box
     */
    private checkAdjacentNodeForMarkedRevision;
    private revertModifiedProperties;
    private redoAction;
    private revertModifiedNodes;
    private insertRemovedNodes;
    undoRevisionForElements(start: TextPosition, end: TextPosition, id: string): void;
    private revertResizing;
    private revertTableDialogProperties;
    addModifiedPropertiesForSection(format: WSectionFormat, property: string, value: Object): Object;
    addModifiedProperties(format: WCharacterFormat, property: string, value: Object): Object;
    addModifiedPropertiesForParagraphFormat(format: WParagraphFormat, property: string, value: Object): Object;
    addModifiedPropertiesForContinueNumbering(paragraphFormat: WParagraphFormat, value: Object): Object;
    addModifiedPropertiesForRestartNumbering(listFormat: WListFormat, value: Object): Object;
    addModifiedPropertiesForList(listLevel: WListLevel): Object;
    private revertProperties;
    addModifiedCellOptions(applyFormat: WCellFormat, format: WCellFormat, table: TableWidget): WCellFormat;
    private copyCellOptions;
    addModifiedTableOptions(format: WTableFormat): void;
    private copyTableOptions;
    private getProperty;
    private getCharacterPropertyValue;
    addModifiedTableProperties(format: WTableFormat, property: string, value: Object): Object;
    addModifiedRowProperties(rowFormat: WRowFormat, property: string, value: Object): Object;
    addModifiedCellProperties(cellFormat: WCellFormat, property: string, value: Object): Object;
    /**
     * @private
     * @returns {void}
     */
    destroy(): void;
    /**
     * @private
     */
    getDeleteOperationsForTrackChanges(): Operation[];
    /**
     * @private
     */
    getDeleteOperationForTrackChanges(element: ElementBox): Operation;
    /**
     * @private
     */
    getActionInfo(isInvertOperation?: boolean): Operation[];
    /**
     * @private
     */
    private getElementAbsolutePosition;
    /**
     * @private
     */
    getFieldOperation(): Operation[];
    private getEditHyperlinkOperation;
    private getPasteContentLength;
    /**
     * @private
     * @returns {Operation}
     */
    getUpdateOperation(): Operation;
    private getResizingOperation;
    /**
     * @private
     * @returns {Operation}
     */
    getDeleteOperation(action: Action, setEndIndex?: boolean, text?: string): Operation;
    /**
     * @private
     * @returns {Operation}
     */
    getInsertOperation(action: Action): Operation;
    private buildTableRowCellOperation;
    private assignRevisionData;
    private createAcceptRejectOperation;
    private beforeInsertTableRevision;
    private afterInsertTableRrevision;
    private buildRowOperation;
    /**
     * @private
     */
    buildRowOperationForTrackChanges(row: TableRowWidget, action?: Action): void;
    private buildCellOperation;
    private deleteColumnOperation;
    private getPasteMergeOperation;
    private deleteCell;
    /**
     * @private
     * @returns {Operation}
     */
    getFormatOperation(element?: ElementBox, action?: string): Operation;
    private getRemovedText;
    private getRemovedFieldCode;
    private getParagraphText;
    private getTableText;
    private getRowText;
    /**
     * @private
     * @returns {Operation}
     */
    getCommentOperation(operation: Operation, comment?: CommentElementBox): Operation;
    /**
     * @private
     */
    getDeleteCommentOperation(modifiedActions: BaseHistoryInfo[], operations: Operation[]): void;
    /**
     * @private
     * @returns {Operation}
     */
    buildFormatOperation(action: Action, ischarFormat: boolean, isCellFormat: boolean): Operation[];
    /**
     * @private
     * @returns {Operation}
     */
    getSelectedCellOperation(action: Action, ischarFormat?: boolean, isBorder?: boolean, isShading?: boolean): Operation[];
    private createListFormat;
    private createCharacterFormat;
    private createParagraphFormat;
    /**
     * @private
     * @returns {void}
     */
    createTableFormat(action: Action): void;
    /**
     * @private
     * @returns {void}
     */
    createRowFormat(action: Action): void;
    /**
     * @private
     * @returns {void}
     */
    createCellFormat(action: Action): void;
    private getTableFormatString;
    private createSectionFormat;
    private getRowString;
    private getCellString;
}
/**
 * @private
 */
export interface Operation {
    action?: 'Insert' | 'Delete' | 'Format' | 'Update';
    offset?: number;
    text?: string;
    length?: number;
    skipOperation?: boolean;
    imageData?: ImageData;
    type?: string;
    markerData?: MarkerData;
    protectionData?: ProtectionData;
    enableTrackChanges?: boolean;
    pasteContent?: string;
    characterFormat?: string;
    tableFormat?: string;
    rowFormat?: string;
    cellFormat?: string;
    paragraphFormat?: string;
    styleData?: string;
    sectionFormat?: string;
    listData?: string;
}
/**
 * @private
 */
export interface ImageData {
    imageString?: string;
    height?: number;
    width?: number;
    metaString?: string;
}
/**
 * @private
 */
export interface MarkerData {
    bookmarkName?: string;
    type?: string;
    user?: string;
    editRangeId?: number;
    skipOperation?: boolean;
    columnFirst?: string;
    columnLast?: string;
    isAfterParagraphMark?: boolean;
    isAfterTableMark?: boolean;
    isAfterRowMark?: boolean;
    isAfterCellMark?: boolean;
    formFieldData?: FormField;
    checkBoxValue?: boolean;
    commentId?: string;
    author?: string;
    date?: string;
    initial?: string;
    done?: boolean;
    commentIndex?: number;
    text?: string;
    ownerCommentId?: string;
    isReply?: boolean;
    revisionId?: string;
    revisionType?: string;
    isAcceptOrReject?: string;
    splittedRevisions?: MarkerData[];
    removedIds?: string[];
    dropDownIndex?: number;
    isSkipTracking?: boolean;
    revisionForFootnoteEndnoteContent?: MarkerData;
}
/**
 * @private
 */
export interface ProtectionData {
    saltValue?: string;
    hashValue?: string;
    protectionType?: ProtectionType;
}
