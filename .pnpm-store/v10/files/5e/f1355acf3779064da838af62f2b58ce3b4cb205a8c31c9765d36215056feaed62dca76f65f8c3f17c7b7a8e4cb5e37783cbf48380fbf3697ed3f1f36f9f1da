@include export-module('documenteditor-layout') {
  .e-de-dlg-container {
    padding-bottom: $de-padding-full;
  }

  .e-de-dlg-row {
    display: flex;
  }

  .e-de-svg-border-color {
    stroke: $de-svg-border-stroke;
  }

  .e-de-svg-border-fill-color {
    fill: $de-svg-border-stroke;
  }

  .e-de-container-row {
    display: flex;
    padding-bottom: $de-padding-full;
  }

  .e-de-subcontainer-left {
    padding-right: $de-padding-half;
    width: 50%;
  }

  .e-de-subcontainer-right {
    padding-left: $de-padding-half;
    width: 50%;
  }

  .e-de-dlg-tab-first-child {
    padding-top: $de-padding-half;
  }

  .e-de-dlg-heading {
    display: block;
    font-size: $de-para-dlg-heading-font-size;
    font-weight: $de-para-dlg-heading-font-weight;
    margin-bottom: $de-ff-dlg-heading-mrgn-btm;
  }

  .e-rtl {
    .e-de-subcontainer-left {
      padding-left: $de-padding-half;
      padding-right: 0;
    }

    .e-de-subcontainer-right {
      padding-left: 0;
      padding-right: $de-padding-half;
      width: 50%;
    }
  }

  .e-bigger {
    .e-de-dlg-container {
      padding-bottom: $de-padding-full-bigger;
    }

    .e-de-container-row {
      padding-bottom: $de-padding-full-bigger;
    }

    .e-de-subcontainer-left {
      padding-right: $de-padding-half-bigger;
      width: 50%;
    }

    .e-de-subcontainer-right {
      padding-left: $de-padding-half-bigger;
      width: 50%;
    }

    .e-de-dlg-tab-first-child {
      padding-top: $de-padding-half-bigger;
    }

    .e-rtl {
      .e-de-subcontainer-left {
        padding-left: $de-padding-half-bigger;
        padding-right: 0;
      }

      .e-de-subcontainer-right {
        padding-left: 0;
        padding-right: $de-padding-half-bigger;
        width: 50%;
      }
    }
  }

  .e-de-blink-cursor {
    border-left: 1px solid $de-blink-cursor-clr;
    pointer-events: none;
    position: absolute;
    z-index: 3;
  }

  .e-de-cursor-animation {
    animation-duration: 1s;
    animation-iteration-count: infinite;
    animation-name: FadeInFadeOut;
  }

  @keyframes FadeInFadeOut {
    from {
      opacity: 1;
    }

    13% {
      opacity: 0;
    }

    50% {
      opacity: 0;
    }

    63% {
      opacity: 1;
    }

    to {
      opacity: 1;
    }
  }

  .e-de-text-target {
    border: 0;
    height: 1px;
    opacity: 0;
    outline-style: none;
    overflow: hidden;
    pointer-events: none;
    position: absolute;
    top: -10000px;
    width: 625px;
  }

  .e-de-txt-form {
    .e-de-txt-field {
      display: block;
    }

    .e-de-num-field {
      display: none;
    }

    .e-de-date-field {
      display: none;
    }

    .e-de-ddl-field {
      display: none;
    }
  }

  .e-de-num-form {
    .e-de-txt-field {
      display: none;
    }

    .e-de-num-field {
      display: block;
    }

    .e-de-date-field {
      display: none;
    }

    .e-de-ddl-field {
      display: none;
    }
  }

  .e-de-date-form {
    .e-de-txt-field {
      display: none;
    }

    .e-de-num-field {
      display: none;
    }

    .e-de-date-field {
      display: block;
    }

    .e-de-ddl-field {
      display: none;
    }
  }

  .e-de-ddl-form {
    .e-de-txt-field {
      display: none;
    }

    .e-de-num-field {
      display: none;
    }

    .e-de-date-field {
      display: none;
    }

    .e-de-ddl-field {
      display: block;
    }
  }

  .e-documenteditor {
    .e-de-op-close-button {
      left: 267px;
      position: absolute;
      top: $de-op-close-button-top;
    }

    .e-de-op-close-button.e-de-rtl {
      left: 14px;
    }
  }
}
