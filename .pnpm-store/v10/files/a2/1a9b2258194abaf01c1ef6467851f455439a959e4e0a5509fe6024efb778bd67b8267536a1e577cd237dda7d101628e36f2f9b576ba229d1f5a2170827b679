@import 'ej2-base/styles/definition/highcontrast-light.scss';
@import '../spreadsheet-ribbon/highcontrast-light-definition.scss';
@import 'ej2-buttons/styles/button/highcontrast-light-definition.scss';
@import 'ej2-buttons/styles/check-box/highcontrast-light-definition.scss';
@import 'ej2-buttons/styles/radio-button/highcontrast-light-definition.scss';
@import 'ej2-buttons/styles/switch/highcontrast-light-definition.scss';
@import 'ej2-navigations/styles/toolbar/highcontrast-light-definition.scss';
@import 'ej2-navigations/styles/tab/highcontrast-light-definition.scss';
@import 'ej2-navigations/styles/context-menu/highcontrast-light-definition.scss';
@import 'ej2-navigations/styles/menu/highcontrast-light-definition.scss';
@import 'ej2-navigations/styles/treeview/highcontrast-light-definition.scss';
@import 'ej2-grids/styles/excel-filter/highcontrast-light-definition.scss';
@import 'ej2-calendars/styles/datepicker/highcontrast-light-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/highcontrast-light-definition.scss';
@import 'ej2-inputs/styles/color-picker/highcontrast-light-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/highcontrast-light-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/highcontrast-light-definition.scss';
@import 'ej2-dropdowns/styles/list-box/highcontrast-light-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/highcontrast-light-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/highcontrast-light-definition.scss';
@import 'highcontrast-light-definition.scss';
@import 'all.scss';
