import { WCharacterFormat } from '../format/character-format';
import { DocumentHelper } from '../viewer';
import { TextSizeInfo } from '../viewer/text-helper';
/**
 * Class which performs regular text measuring logic to find font height.
 */
export declare class Regular {
    /**
     * @private
     */
    documentHelper: DocumentHelper;
    /**
     * Gets module name.
     *
     * @returns {string} - the module name.
     */
    private getModuleName;
    /**
     * Constructor to initialize Regular module.
     *
     * @param {DocumentHelper} documentHelper - the document helper object
     */
    constructor(documentHelper: DocumentHelper);
    /**
     * @private
     * @param {WCharacterFormat} characterFormat - character format to apply.
     * @returns {TextSizeInfo} returns text size information.
     */
    getHeightInternal(characterFormat: WCharacterFormat, fontToRender: string): TextSizeInfo;
    applyStyle(spanElement: HTMLSpanElement, characterFormat: WCharacterFormat, fontToRender: string): void;
    /**
     * @private
     * @returns {void}
     */
    destroy(): void;
}
