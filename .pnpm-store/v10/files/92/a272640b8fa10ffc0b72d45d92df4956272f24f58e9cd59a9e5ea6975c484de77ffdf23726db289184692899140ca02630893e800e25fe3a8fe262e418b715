/**
 * Specifies Workbook internal events.
 */
/** @hidden */
export declare const workbookDestroyed: string;
/** @hidden */
export declare const updateSheetFromDataSource: string;
/** @hidden */
export declare const dataSourceChanged: string;
/** @hidden */
export declare const dataChanged: string;
/** @hidden */
export declare const triggerDataChange: string;
/** @hidden */
export declare const workbookOpen: string;
/** @hidden */
export declare const beginSave: string;
/** @hidden */
export declare const beginAction: string;
/** @hidden */
export declare const sortImport: string;
/** @hidden */
export declare const ribbonFind: string;
/** @hidden */
export declare const exportDialog: string;
/** @hidden */
export declare const getFilteredCollection: string;
/** @hidden */
export declare const saveCompleted: string;
/** @hidden */
export declare const applyNumberFormatting: string;
/** @hidden */
export declare const getFormattedCellObject: string;
/** @hidden */
export declare const refreshCellElement: string;
/** @hidden */
export declare const setCellFormat: string;
/** @hidden */
export declare const findAllValues: string;
/** @hidden */
export declare const textDecorationUpdate: string;
/** @hidden */
export declare const applyCellFormat: string;
/** @hidden */
export declare const updateUsedRange: string;
/** @hidden */
export declare const updateRowColCount: string;
/** @hidden */
export declare const workbookFormulaOperation: string;
/** @hidden */
export declare const workbookEditOperation: string;
/** @hidden */
export declare const checkDateFormat: string;
/** @hidden */
export declare const getFormattedBarText: string;
/** @hidden */
export declare const activeCellChanged: string;
/** @hidden */
export declare const openSuccess: string;
/** @hidden */
export declare const openFailure: string;
/** @hidden */
export declare const sheetCreated: string;
/** @hidden */
export declare const sheetsDestroyed: string;
/** @hidden */
export declare const aggregateComputation: string;
/** @hidden */
export declare const getUniqueRange: string;
/** @hidden */
export declare const removeUniquecol: string;
/** @hidden */
export declare const checkUniqueRange: string;
/** @hidden */
export declare const reApplyFormula: string;
/** @hidden */
export declare const clearFormulaDependentCells: string;
/** @hidden */
export declare const formulaInValidation: string;
/** @hidden */
export declare const beforeSort: string;
/** @hidden */
export declare const initiateSort: string;
/** @hidden */
export declare const updateSortedDataOnCell: string;
/** @hidden */
export declare const sortComplete: string;
/** @hidden */
export declare const sortRangeAlert: string;
/** @hidden */
export declare const initiatelink: string;
/** @hidden */
export declare const beforeHyperlinkCreate: string;
/** @hidden */
export declare const afterHyperlinkCreate: string;
/** @hidden */
export declare const beforeHyperlinkClick: string;
/** @hidden */
export declare const afterHyperlinkClick: string;
/** @hidden */
export declare const addHyperlink: string;
/** @hidden */
export declare const setLinkModel: string;
/** @hidden */
export declare const beforeFilter: string;
/** @hidden */
export declare const initiateFilter: string;
/** @hidden */
export declare const filterComplete: string;
/** @hidden */
export declare const filterRangeAlert: string;
/** @hidden */
export declare const clearAllFilter: string;
/** @hidden */
export declare const wrapEvent: string;
/** @hidden */
export declare const onSave: string;
/** @hidden */
export declare const insert: string;
/** @hidden */
export declare const deleteAction: string;
/** @hidden */
export declare const insertModel: string;
/** @hidden */
export declare const deleteModel: string;
/** @hidden */
export declare const isValidation: string;
/** @hidden */
export declare const cellValidation: string;
/** @hidden */
export declare const addHighlight: string;
/** @hidden */
export declare const dataValidate: string;
/** @hidden */
export declare const find: string;
/** @hidden */
export declare const goto: string;
/** @hidden */
export declare const findWorkbookHandler: string;
/** @hidden */
export declare const replace: string;
/** @hidden */
export declare const replaceAll: string;
/** @hidden */
export declare const showDialog: string;
/** @hidden */
export declare const findKeyUp: string;
/** @hidden */
export declare const removeHighlight: string;
/** @hidden */
export declare const queryCellInfo: string;
/** @hidden */
export declare const count: string;
/** @hidden */
export declare const findCount: string;
/** @hidden */
export declare const protectSheetWorkBook: string;
/** @hidden */
export declare const updateToggle: string;
/** @hidden */
export declare const protectsheetHandler: string;
/** @hidden */
export declare const replaceAllDialog: string;
/** @hidden */
export declare const unprotectsheetHandler: string;
/** @hidden */
export declare const workBookeditAlert: string;
/** @hidden */
export declare const setLockCells: string;
/** @hidden */
export declare const applyLockCells: string;
/** @hidden */
export declare const setMerge: string;
/** @hidden */
export declare const applyMerge: string;
/** @hidden */
export declare const mergedRange: string;
/** @hidden */
export declare const activeCellMergedRange: string;
/** @hidden */
export declare const insertMerge: string;
/** @hidden */
export declare const hideShow: string;
/** @hidden */
export declare const setCFRule: string;
/** @hidden */
export declare const applyCF: string;
/** @hidden */
export declare const clearCFRule: string;
/** @hidden */
export declare const clear: string;
/** @hidden */
export declare const clearCF: string;
/** @hidden */
export declare const setImage: string;
/** @hidden */
export declare const setChart: string;
/** @hidden */
export declare const initiateChart: string;
/** @hidden */
export declare const refreshRibbonIcons: string;
/** @hidden */
export declare const refreshChart: string;
/** @hidden */
export declare const refreshChartSize: string;
/** @hidden */
export declare const updateChart: string;
/** @hidden */
export declare const deleteChartColl: string;
/** @hidden */
export declare const initiateChartModel: string;
/** @hidden */
export declare const focusChartBorder: string;
/** @hidden */
export declare const saveError: string;
/** @hidden */
export declare const validationHighlight: string;
/** @hidden */
export declare const updateFilter: string;
/** @hidden */
export declare const beforeInsert: string;
/** @hidden */
export declare const beforeDelete: string;
/** @hidden */
export declare const deleteHyperlink: string;
/** @hidden */
export declare const moveOrDuplicateSheet: string;
/** @hidden */
export declare const setAutoFill: string;
/** @hidden */
export declare const refreshCell: string;
/** @hidden */
export declare const getFillInfo: string;
/** @hidden */
export declare const getautofillDDB: string;
/** @hidden */
export declare const rowFillHandler: string;
/** @hidden */
export declare const getTextSpace: string;
/** @hidden */
export declare const refreshClipboard: string;
/** @hidden */
export declare const updateView: string;
/** @hidden */
export declare const selectionComplete: string;
/** @hidden */
export declare const refreshInsertDelete: string;
/** @hidden */
export declare const getUpdatedFormulaOnInsertDelete: string;
/** @hidden */
export declare const beforeCellUpdate: string;
/** @hidden */
export declare const duplicateSheetFilterHandler: string;
/** @hidden */
export declare const unMerge: string;
/** @hidden */
export declare const addFormatToCustomFormatDlg: string;
/** @hidden */
export declare const checkFormulaRef: string;
