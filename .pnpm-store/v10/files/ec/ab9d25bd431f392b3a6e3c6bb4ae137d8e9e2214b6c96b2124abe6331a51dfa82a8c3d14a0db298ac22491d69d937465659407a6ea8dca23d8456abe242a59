/**
 * Specifies spreadsheet internal events
 */
/** @hidden */
export declare const ribbon: string;
/** @hidden */
export declare const formulaBar: string;
/** @hidden */
export declare const sheetTabs: string;
/** @hidden */
export declare const refreshSheetTabs: string;
/** @hidden */
export declare const isFormulaBarEdit: string;
/** @hidden */
export declare const contentLoaded: string;
/** @hidden */
export declare const mouseDown: string;
/** @hidden */
export declare const spreadsheetDestroyed: string;
/** @hidden */
export declare const editOperation: string;
/** @hidden */
export declare const formulaOperation: string;
/** @hidden */
export declare const formulaBarOperation: string;
/** @hidden */
export declare const click: string;
/** @hidden */
export declare const keyUp: string;
/** @hidden */
export declare const keyDown: string;
/** @hidden */
export declare const formulaKeyUp: string;
/** @hidden */
export declare const formulaBarUpdate: string;
/** @hidden */
export declare const onVerticalScroll: string;
/** @hidden */
export declare const onHorizontalScroll: string;
/** @hidden */
export declare const beforeContentLoaded: string;
/** @hidden */
export declare const beforeVirtualContentLoaded: string;
/** @hidden */
export declare const virtualContentLoaded: string;
/** @hidden */
export declare const contextMenuOpen: string;
/** @hidden */
export declare const cellNavigate: string;
/** @hidden */
export declare const mouseUpAfterSelection: string;
/** @hidden */
export declare const cMenuBeforeOpen: string;
/** @hidden */
export declare const insertSheetTab: string;
/** @hidden */
export declare const removeSheetTab: string;
/** @hidden */
export declare const renameSheetTab: string;
/** @hidden */
export declare const ribbonClick: string;
/** @hidden */
export declare const refreshRibbon: string;
/** @hidden */
export declare const enableToolbarItems: string;
/** @hidden */
export declare const tabSwitch: string;
/** @hidden */
export declare const selectRange: string;
/** @hidden */
export declare const rangeSelectionByKeydown: string;
/** @hidden */
export declare const cut: string;
/** @hidden */
export declare const copy: string;
/** @hidden */
export declare const paste: string;
/** @hidden */
export declare const clearCopy: string;
/** @hidden */
export declare const dataBound: string;
/** @hidden */
export declare const beforeDataBound: string;
/** @hidden */
export declare const addContextMenuItems: string;
/** @hidden */
export declare const removeContextMenuItems: string;
/** @hidden */
export declare const enableContextMenuItems: string;
/** @hidden */
export declare const enableFileMenuItems: string;
/** @hidden */
export declare const hideFileMenuItems: string;
/** @hidden */
export declare const addFileMenuItems: string;
/** @hidden */
export declare const hideRibbonTabs: string;
/** @hidden */
export declare const enableRibbonTabs: string;
/** @hidden */
export declare const addRibbonTabs: string;
/** @hidden */
export declare const addToolbarItems: string;
/** @hidden */
export declare const hideToolbarItems: string;
/** @hidden */
export declare const beforeRibbonCreate: string;
/** @hidden */
export declare const rowHeightChanged: string;
/** @hidden */
export declare const colWidthChanged: string;
/** @hidden */
export declare const onContentScroll: string;
/** @hidden */
export declare const deInitProperties: string;
/** @hidden */
export declare const activeSheetChanged: string;
/** @hidden */
export declare const initiateCustomSort: string;
/** @hidden */
export declare const applySort: string;
/** @hidden */
export declare const collaborativeUpdate: string;
/** @hidden */
export declare const autoFit: string;
/** @hidden */
export declare const updateToggleItem: string;
/** @hidden */
export declare const initiateHyperlink: string;
/** @hidden */
export declare const editHyperlink: string;
/** @hidden */
export declare const openHyperlink: string;
/** @hidden */
export declare const removeHyperlink: string;
/** @hidden */
export declare const createHyperlinkElement: string;
/** @hidden */
export declare const sheetNameUpdate: string;
/** @hidden */
export declare const hideSheet: string;
/** @hidden */
export declare const performUndoRedo: string;
/** @hidden */
export declare const updateUndoRedoCollection: string;
/** @hidden */
export declare const setActionData: string;
/** @hidden */
export declare const getBeforeActionData: string;
/** @hidden */
export declare const clearUndoRedoCollection: string;
/** @hidden */
export declare const initiateFilterUI: string;
/** @hidden */
export declare const renderFilterCell: string;
/** @hidden */
export declare const reapplyFilter: string;
/** @hidden */
export declare const filterByCellValue: string;
/** @hidden */
export declare const clearFilter: string;
/** @hidden */
export declare const getFilteredColumn: string;
/** @hidden */
export declare const completeAction: string;
/** @hidden */
export declare const filterCellKeyDown: string;
/** @hidden */
export declare const getFilterRange: string;
/** @hidden */
export declare const setAutoFit: string;
/** @hidden */
export declare const refreshFormulaDatasource: string;
/** @hidden */
export declare const setScrollEvent: string;
/** @hidden */
export declare const initiateDataValidation: string;
/** @hidden */
export declare const validationError: string;
/** @hidden */
export declare const startEdit: string;
/** @hidden */
export declare const invalidData: string;
/** @hidden */
export declare const clearInvalid: string;
/** @hidden */
export declare const protectSheet: string;
/** @hidden */
export declare const applyProtect: string;
/** @hidden */
export declare const unprotectSheet: string;
/** @hidden */
export declare const protectCellFormat: string;
/** @hidden */
export declare const gotoDlg: string;
/** @hidden */
export declare const findDlg: string;
/** @hidden */
export declare const findHandler: string;
/** @hidden */
export declare const created: string;
/** @hidden */
export declare const editAlert: string;
/** @hidden */
export declare const setUndoRedo: string;
/** @hidden */
export declare const enableFormulaInput: string;
/** @hidden */
export declare const protectSelection: string;
/** @hidden */
export declare const hiddenMerge: string;
/** @hidden */
export declare const checkPrevMerge: string;
/** @hidden */
export declare const checkMerge: string;
/** @hidden */
export declare const removeDataValidation: string;
/** @hidden */
export declare const showAggregate: string;
/** @hidden */
export declare const goToSheet: string;
/** @hidden */
export declare const showSheet: string;
/** @hidden */
export declare const renderCFDlg: string;
/** @hidden */
export declare const clearViewer: string;
/** @hidden */
export declare const initiateFormulaReference: string;
/** @hidden */
export declare const initiateCur: string;
/** @hidden */
export declare const clearCellRef: string;
/** @hidden */
export declare const editValue: string;
/** @hidden */
export declare const addressHandle: string;
/** @hidden */
export declare const initiateEdit: string;
/** @hidden */
export declare const forRefSelRender: string;
/** @hidden */
export declare const insertImage: string;
/** @hidden */
export declare const refreshImgElem: string;
/** @hidden */
export declare const refreshImgCellObj: string;
/** @hidden */
export declare const getRowIdxFromClientY: string;
/** @hidden */
export declare const getColIdxFromClientX: string;
/** @hidden */
export declare const createImageElement: string;
/** @hidden */
export declare const deleteImage: string;
/** @hidden */
export declare const deleteChart: string;
/** @hidden */
export declare const refreshChartCellObj: string;
/** @hidden */
export declare const refreshImagePosition: string;
/** @hidden */
export declare const updateTableWidth: string;
/** @hidden */
export declare const focusBorder: string;
/** @hidden */
export declare const clearChartBorder: string;
/** @hidden */
export declare const insertChart: string;
/** @hidden */
export declare const chartRangeSelection: string;
/** @hidden */
export declare const insertDesignChart: string;
/** @hidden */
export declare const removeDesignChart: string;
/** @hidden */
export declare const chartDesignTab: string;
/** @hidden */
export declare const addChartEle: string;
/** @hidden */
export declare const undoRedoForChartDesign: string;
/** @hidden */
export declare const protectWorkbook: string;
/** @hidden */
export declare const unProtectWorkbook: string;
/** @hidden */
export declare const getPassWord: string;
/** @hidden */
export declare const setProtectWorkbook: string;
/** @hidden */
export declare const removeWorkbookProtection: string;
/** @hidden */
export declare const importProtectWorkbook: string;
/** @hidden */
export declare const selectionStatus: string;
/** @hidden */
export declare const freeze: string;
/** @hidden */
export declare const overlayEleSize: string;
/** @hidden */
export declare const updateScroll: string;
/** @hidden */
export declare const positionAutoFillElement: string;
/** @hidden */
export declare const hideAutoFillOptions: string;
/** @hidden */
export declare const performAutoFill: string;
/** @hidden */
export declare const selectAutoFillRange: string;
/** @hidden */
export declare const autoFill: string;
/** @hidden */
export declare const hideAutoFillElement: string;
/** @hidden */
export declare const unProtectSheetPassword: string;
/** @hidden */
export declare const updateTranslate: string;
/** @hidden */
export declare const getUpdatedScrollPosition: string;
/** @hidden */
export declare const updateScrollValue: string;
/** @hidden */
export declare const beforeCheckboxRender: string;
/** @hidden */
export declare const refreshCheckbox: string;
/** @hidden */
export declare const renderInsertDlg: string;
/** @hidden */
export declare const toggleProtect: string;
