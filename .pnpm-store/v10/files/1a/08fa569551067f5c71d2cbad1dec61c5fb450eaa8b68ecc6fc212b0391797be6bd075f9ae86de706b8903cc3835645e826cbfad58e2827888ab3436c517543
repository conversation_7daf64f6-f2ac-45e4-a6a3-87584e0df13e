/* eslint-disable */
import { isNullOrUndefined } from '@syncfusion/ej2-base';
/**
 * @private
 */
var XmlHttpRequestHandler = /** @class */ (function () {
    function XmlHttpRequestHandler() {
        /**
         * A boolean value indicating whether the request should be sent asynchronous or not.
         *
         * @default true
         */
        this.mode = true;
    }
    /**
     * Send the request to server
     *
     * @param  {object} jsonObject - To send to service
     */
    XmlHttpRequestHandler.prototype.send = function (jsonObject, httpRequestEventArgs, isAsync) {
        var _this = this;
        this.xmlHttpRequest = new XMLHttpRequest();
        var timeout = 0;
        isAsync = isNullOrUndefined(isAsync) ? true : isAsync;
        if (!isNullOrUndefined(httpRequestEventArgs)) {
            this.xmlHttpRequest.withCredentials = httpRequestEventArgs.withCredentials;
            timeout = (httpRequestEventArgs.timeout >= 0 ? httpRequestEventArgs.timeout : 0);
            this.customHeaders = httpRequestEventArgs.headers;
        }
        this.xmlHttpRequest.onreadystatechange = function () {
            _this.stateChange(_this);
        };
        this.xmlHttpRequest.onerror = function () {
            _this.error(_this);
        };
        if (!this.mode) {
            setTimeout(function () {
                _this.sendRequest(jsonObject, timeout, isAsync);
            });
        }
        else {
            this.sendRequest(jsonObject, timeout, isAsync);
        }
    };
    XmlHttpRequestHandler.prototype.sendRequest = function (jsonObj, timeout, isAsync) {
        this.xmlHttpRequest.open('POST', this.url, isAsync);
        if (isAsync) {
            this.xmlHttpRequest.timeout = timeout;
        }
        if (this.contentType) {
            this.xmlHttpRequest.setRequestHeader('Content-Type', this.contentType);
        }
        this.setCustomAjaxHeaders();
        if (this.responseType) {
            this.xmlHttpRequest.responseType = this.responseType;
        }
        var data = jsonObj instanceof FormData ? jsonObj : JSON.stringify(jsonObj);
        this.xmlHttpRequest.send(data); // jshint ignore:line
    };
    XmlHttpRequestHandler.prototype.stateChange = function (proxyReq) {
        if (proxyReq.xmlHttpRequest.readyState === 4 && proxyReq.xmlHttpRequest.status === 200) {
            var data = void 0;
            if (this.responseType) {
                data = proxyReq.xmlHttpRequest.response;
            }
            else {
                data = proxyReq.xmlHttpRequest.responseText;
            }
            var result = {
                name: 'onSuccess',
                data: data,
                readyState: proxyReq.xmlHttpRequest.readyState,
                status: proxyReq.xmlHttpRequest.status
            };
            proxyReq.successHandler(result);
        }
        else if (proxyReq.xmlHttpRequest.readyState === 4 && !(proxyReq.xmlHttpRequest.status === 200)) { // jshint ignore:line)
            var result = {
                name: 'onFailure',
                status: proxyReq.xmlHttpRequest.status,
                statusText: proxyReq.xmlHttpRequest.statusText,
                url: proxyReq.url
            };
            proxyReq.failureHandler(result);
        }
    };
    XmlHttpRequestHandler.prototype.error = function (proxyReq) {
        var result = {
            name: 'onError',
            status: this.xmlHttpRequest.status,
            statusText: this.xmlHttpRequest.statusText
        };
        proxyReq.errorHandler(result);
    };
    XmlHttpRequestHandler.prototype.successHandler = function (response) {
        if (this.onSuccess) {
            this.onSuccess(response);
        }
        return response;
    };
    XmlHttpRequestHandler.prototype.failureHandler = function (response) {
        if (this.onFailure) {
            this.onFailure(response);
        }
        return response;
    };
    XmlHttpRequestHandler.prototype.errorHandler = function (response) {
        if (this.onError) {
            this.onError(response);
        }
        return response;
    };
    XmlHttpRequestHandler.prototype.setCustomAjaxHeaders = function () {
        for (var i = 0; i < this.customHeaders.length; i++) {
            var header = this.customHeaders[i];
            for (var _i = 0, _a = Object.keys(header); _i < _a.length; _i++) {
                var key = _a[_i];
                this.xmlHttpRequest.setRequestHeader(key, header[key]);
            }
        }
    };
    return XmlHttpRequestHandler;
}());
export { XmlHttpRequestHandler };
