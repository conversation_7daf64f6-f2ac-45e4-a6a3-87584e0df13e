@include export-module('document-editor-container-theme') {
    /* stylelint-disable */
    .e-de-toolbar {
        height: 100%;
    }
    .e-de-prop-pane .e-de-ctnr-group-btn.e-btn-group:not(.e-outline){
        box-shadow: none;
        height: $de-group-btn-hgt;
        #{if(&, '&', '*')}:focus {
            box-shadow: none;
        }
        #{if(&, '&', '*')}:active {
            box-shadow: none;
        }
        #{if(&, '&', '*')}:hover {
            box-shadow: none;
        }
    }
    .e-de-ctnr-group-btn-middle button {
        border-radius: 0px;
    }
    @if ($skin-name=='Material3') {
        .e-de-review-pane button.e-de-close-icon {
            background: transparent;
            box-shadow: none;
        }
        .e-de-op-more-less > div:last-child label {
            left: 35px;
        }
        .e-bigger .e-de-ctn .e-de-bzr-button {
            line-height: 17px;
            padding-top: 12px;
        }
        .e-de-ctn .e-de-bzr-button {
            box-shadow: none !important;
        }
        .e-bigger .e-de-ctn .e-de-bzr-button {
            box-shadow: none !important;
        }
        .e-de-char-fmt-btn-left button:not(:first-child) {
            border: 1px solid transparent;
            border-left: 1px solid rgba($border-light);
        }
        .e-de-ctnr-group-btn {
            .e-de-prop-font-button {
                position: relative;
                border: 1px solid transparent rgba($border-light);
                border-right-width: 1px;
            }
            .e-de-prop-font-last-button {
                position: relative;
                border: 1px solid transparent rgba($border-light);
                border-left-width: 1px;
            }
        }
        .e-de-ctnr-group-btn {
            .e-de-prop-indent-button {
                position: relative;
                border: 1px solid transparent rgba($border-light);
                border-right-width: 1px;
            }
            .e-de-prop-indent-last-button {
                position: relative;
                border: 1px solid transparent rgba($border-light);
                border-left-width: 1px;
            }
        }
        .e-de-grp-btn-ctnr {
            .e-de-ctnr-group-btn-middle {
                > * {
                    border-radius: 0px;
                }
                margin-bottom: -1px;
            }
        }
        .e-bigger {
            .e-de-grp-btn-ctnr {
                .e-de-ctnr-group-btn-middle {
                    > * {
                        border-radius: 0px;
                    }
                }
                .e-de-ctnr-group-btn-top {
                    > * {
                        border-bottom-left-radius: 0px;
                        border-bottom-right-radius: 0px;
                    }
                }
                .e-de-ctnr-group-btn-bottom {
                    > * {
                        border-top-left-radius: 0px;
                        border-top-right-radius: 0px;
                    }
                }
            }
        }
    }
    .de-tbl-fill-clr .e-dropdown-btn.e-btn {
        box-shadow: none;
    }
    .e-de-prop-pane .e-de-ctnr-group-btn button,
    .e-de-ctn .e-de-status-bar button {
        box-shadow: none;
        height: $de-group-btn-hgt;
        #{if(&, '&', '*')}:focus {
            box-shadow: none;
        }
        #{if(&, '&', '*')}:active {
            box-shadow: none;
        }
        #{if(&, '&', '*')}:hover {
            box-shadow: none;
        }
    }
    .e-de-statusbar-pageweb {
        background: $de-ctnr-bg-clr;
        border: 0;
        box-shadow: none;
        float: right;
        @if $skin-name!='bootstrap5' and $skin-name!='tailwind' and $skin-name!='FluentUI' { 
            height: 33px;
            width: 33px;
        }
        @if $skin-name=='bootstrap4' {
            color: $e-de-statusbar-separator-color;
        }
        @if $skin-name=='bootstrap5' {
            color: $icon-color;
        }
        #{if(&, '&', '*')}:hover {
            @if $skin-name=='bootstrap5' {
                color: $e-de-bzr-button-hover-font-color;
            }
            box-shadow: none;
        }
    }
    
    .e-split-btn-wrapper.e-de-prop-splitbutton,
    .e-split-btn-wrapper.e-de-btn-hghlclr,
    .e-btn.de-split-button {
        @if $skin-name=='Material3' {
            box-shadow: none;
        }
    }
    .e-de-statusbar-pageweb .e-de-printlayout,
    .e-de-statusbar-pageweb .e-de-weblayout {
        font-size: $e-de-bzr-btn-font-size;
    }
    .e-de-statusbar-pageweb .e-de-printlayout:hover,
    .e-de-statusbar-pageweb .e-de-weblayout:hover {
        font-size: $e-de-bzr-btn-font-size;
        @if $skin-name=='bootstrap5' {
          color: $e-de-bzr-button-hover-font-color;
        }
    }
    .e-bigger {
        @if $skin-name !='bootstrap5' and $skin-name !='tailwind' and $skin-name!='FluentUI'   { 
            .e-btn.e-de-statusbar-pageweb {
                padding: 0;
            }
        }
        .e-de-statusbar-pageweb .e-de-printlayout,
        .e-de-statusbar-pageweb .e-de-weblayout {
            font-size: $e-de-bzr-btn-font-size-big;
            @if $skin-name=='bootstrap' {
                margin-top: -6px;
            }
        }
    }
    .e-listview .e-list-icon {
        height: 24px;
        width: 16px;
        margin-right: $e-de-ctnr-break-listview-margin-right;
    }
    .e-de-listview-icon {
        height: auto;
        width: auto;
        line-height: 22px;
        margin-right: $e-de-ctnr-break-listview-margin-right;
    }
    .e-de-linespacing {
        margin-top: $e-de-ctnr-linespacing-tick-icon;
    }
    .e-de-statusbar-zoom {
        background: $de-ctnr-bg-clr;
        border: 0;
        color: $de-font-color;
        float: right;
        font-weight: 400;
        height: 33px;
        @if $skin-name =='bootstrap5' {
            box-shadow: none;
        }
    }
    .e-de-pagenumber-text {
        border: none !important;
        // height: 22px !important;
        // margin-top: $de-pagenumber-text-margin-top !important;
    }

    @if $skin-name=='Material3' {
        .e-de-prop-pane .e-de-ctnr-group-btn.e-btn-group button {
            background: $de-prop-btn-bg-color;
        }
        .e-de-font-clr-picker button, .e-de-prop-font-colorpicker button {
            background: $de-prop-btn-bg-color !important;
        }
        .e-de-style-font-color-picker .e-de-style-icon-button-size button:first-child {
            margin-right: 0px;
        }
        .e-de-ctnr-group-btn .e-btn-group button, 
        .e-documenteditorcontainer.e-lib .e-split-btn-wrapper button,
        .e-documenteditorcontainer.e-lib .e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn,
        .e-de-ctnr-segment button, 
        .e-de-char-fmt-btn-right button,
        .e-de-border-size-button,
        .e-de-cell-div button,
        .e-de-insert-del-cell button,
        .e-de-align-text button {
            border-radius: 4px;
            background: $de-prop-btn-bg-color;
        }
        .e-de-char-fmt-btn-right button:not(:first-child),
        .e-de-char-fmt-btn-left button:not(:first-child), 
        .e-de-align-text button:not(:first-child) {
            border-left: 1.7px solid rgba($border-light);
            background: $de-prop-btn-bg-color;
        }
        .e-de-insert-del-cell button:not(:first-child) {
            &:not(.e-rtl) {
                border-left: 1.7px solid rgba($border-light);
            }
        }
        .e-de-ctnr-segment-rtl {
            .e-de-char-fmt-btn-left,
            .e-de-char-fmt-btn-right {
              .e-de-prop-font-last-button,
              .e-de-prop-indent-last-button {
                border-left: 0;
              }
            }
        }
        .e-rtl {
            .e-de-char-fmt-btn-right button:not(:last-child),
            .e-de-char-fmt-btn-left button:not(:last-child),
            .e-de-insert-del-cell button:not(:last-child),
            .e-de-align-text button:not(:last-child) {
                border-right: 1.7px solid rgba($border-light);
                background: $de-prop-btn-bg-color;
            }
        }
        
        .e-de-char-fmt-btn-right.e-rtl button:not(:first-child),
        .e-de-char-fmt-btn-left.e-rtl button:not(:first-child),
        .e-de-insert-del-cell .e-rtl button:not(:first-child),
        .e-de-align-text .e-rtl button:not(:first-child)
        {
            border-right: 1px solid rgba($border-light) !important;
        }
        .e-de-grp-btn-ctnr {
            .e-de-ctnr-group-btn {
                .e-de-prop-font-button {
                    background: $de-prop-btn-bg-color;
                    border: 1px solid rgba($border-light) !important;
                }
            }
            .e-de-ctnr-group-btn-top {
                margin-bottom: -1px;
            }
        }
        .e-de-font-clr-picker > *,
        .de-split-button>div:first-child {
            margin-right: 8px;
        }
    }

    .e-de-pagenumber-input {
        background: $e-de-pagenumber-bg;
        @if $skin-name =='Material3'{
            border: none !important;
        } 
        @else {
            border: 1px solid $de-panel-border;
        }
        border-radius: $de-pagenumber-text-border-radius;
        color: $de-font-color;
        @if $skin-name=='bootstrap5' or  $skin-name=='tailwind' or $skin-name =='FluentUI' {
          font-family: inherit;
          font-size: $e-de-btn-font-size;
        }
        padding: 0px;
        text-align: center;
        width: 22px;
    }
    @if $skin-name=='bootstrap5' or  $skin-name=='tailwind' or $skin-name =='FluentUI'  {
        .e-bigger {
            .e-de-pagenumber-input {
                font-size: $e-de-btn-font-size-big;
                //margin-top: $de-page-number-margin-top-big;
            }
        }
    }
    .e-btn-pageweb-toggle {
        background-color: $de-pageweb-toggle-color;
        box-shadow: none;
        outline: $de-pageweb-toggle-outline;
        outline-offset: 0;
        @if $skin-name=='bootstrap4' {
            color: $de-prop-btn-bg-color;
        }
    }
    .e-btn-pageweb-spellcheck {
        border: 0;
        box-shadow: none;
        float: right;
        margin-left: $de-zoom-tlbr-margin-left;
    }
    @if $skin-name=='tailwind' $skin-name=='material' or $skin-name=='bootstrap-dark' or $skin-name=='bootstrap' or $skin-name=='bootstrap4' or $skin-name=='bootstrap5' or $skin-name=='fabric-dark' or $skin-name=='fabric' or $skin-name=='highcontrast' or $skin-name=='material-dark' {
        .e-bigger .e-btn-pageweb-spellcheck {
            margin-left: $de-zoom-tlbr-margin-left-big;
        }
    }
    .e-de-ctn-title {
        background-color: $de-title-bar-bg-clr;
        border-bottom: none !important;
        color: $de-title-bar-font-clr;
        @if $skin-name=='tailwind' or $skin-name == 'bootstrap5' or $skin-name =='FluentUI' or $skin-name =='Material3' {
            font-size: $e-de-btn-font-size !important;
            button {
                font-size: $e-de-btn-font-size !important;
                height: 36px !important;
                @if  $skin-name !='tailwind' {
                    margin: 0 !important;
                }
                .e-btn-icon {
                    font-size: $e-de-btn-font-icon-size !important;
                }
            }
        }
    }
    @if $skin-name=='tailwind' or $skin-name == 'bootstrap5' or $skin-name =='FluentUI' or $skin-name =='Material3' {
        .e-bigger {
            .e-de-ctn-title {
                font-size: $e-de-btn-font-size-big !important;
                height: 40px !important;
                button {
                    font-size: $e-de-btn-font-size-big !important;
                    height: auto !important;
                    @if  $skin-name !='tailwind' {
                        margin: 0 !important;
                    }
                    .e-btn-icon {
                        font-size: $e-de-btn-font-icon-size-big !important;
                    }
                }
            }
        }
    }
    .e-de-tool-ctnr-properties-pane {
        display: flex;
        height: $de-tool-ctnr-editor-height;
        width: 100%;
    }
    .e-de-ctnr-properties-pane {
        display: flex;
        height: $de-ctnr-editor-height;
        width: 100%;
    }
    .e-de-statusbar-separator {
        border-left: 1px solid $e-de-statusbar-separator-color;
        height: 16px;
        margin-left: $de-spellcheck-tlbr-margin-left;
        margin-right: $de-spellcheck-tlbr-margin-left;
        margin-top: $de-status-br-top;
    }
    .e-bigger .e-de-statusbar-separator {
        border-left: 1px solid $e-de-statusbar-separator-color;
        @if $skin-name == 'Material3' {
            height: 20px;
        } @else {
            height: 16px;
        }        
        margin-left: $de-spellcheck-tlbr-margin-left;
        margin-right: $de-spellcheck-tlbr-margin-left;
        margin-top: $de-status-br-top-big;
    }
    .e-de-statusbar-spellcheck {
        border-radius: 2px;
        font-weight: 400;
        
        @if $skin-name == 'Material3' {
            background: $de-ctnr-bg-clr;
        }
    }
    .e-de-ctn {
        background-color: $de-ctnr-bg-clr;
        border: 1px solid $de-panel-border;
        height: $de-ctnr-height;
        position: relative;
        width: 100%;
    }
    .e-bigger .e-de-statusbar-spellcheck {
        border-radius: 2px;
    }
    .e-de-ctnr-toolbar {
        display: flex;
        height: $de-ctnr-tlbr-height;
        width: 100%;
        @if $skin-name == 'Material3' {
            background: $de-toolbar-background-color;
        }
    }
    .e-de-tlbr-wrapper .e-de-toolbar.e-toolbar,
    .e-de-tlbr-wrapper .e-de-ctnr-properties-pane-btn {
        border: 0;
    }
    .e-de-pane {
        border-left: 1px solid $de-panel-border;
        @if $skin-name == 'Material3' {
            background: $de-pane-background-color;
        }
    }
    .e-de-pane-rtl {
        border-right: 1px solid $de-panel-border;
        @if $skin-name == 'Material3' {
            background: $de-pane-background-color;
        }
    }
    @if $skin-name=='material' {
        .e-de-pane>div button:not(.e-primary),
        .e-de-pane-rtl>div button:not(.e-primary) {
            background-color: $de-panel-btn-bg-clr;
        }
    }
    .e-de-tool-ctnr-properties-pane,
    .e-de-ctnr-properties-pane {
        background: $de-ctnr-prop-bg-clr;
        border-bottom: 1px solid $de-panel-border;
        border-top: 1px solid $de-panel-border;
    }
    .e-de-ctnr-segment {
        margin-bottom: $de-ctnr-margin-bottom;
    }
    @if $skin-name=='tailwind' or $skin-name=='bootstrap5' or $skin-name =='FluentUI' {
        .e-de-insert-del-cell>div:last-child {
            margin-left: $de-pane-margin-right !important;
        }
        .e-bigger {
            .e-de-ctnr-prop-label,
            .e-de-ctnr-segment {
                margin-bottom: 16px;
            }
            .e-de-property-div-padding {
                //padding-bottom: 16px;
                padding: 16px;
            }
            .e-de-insert-del-cell>div:last-child {
                margin-left: $de-pane-margin-right-big !important;
            }
            .e-de-ctnr-group-btn .e-btn .e-btn-icon {
                margin-left: $de-btn-icon-margin;
            }
            .e-de-ctnr-group-btn .e-btn .e-btn-icon:not(.e-caret) {
                font-size: $de-btn-font-size-bigger;
            }
            .e-de-pane-rtl {
                .e-de-ctnr-group-btn .e-btn .e-btn-icon {
                    margin-left: 0px;
                    @if $skin-name=='tailwind' {
                        margin-right: -5px;
                    }
                    @else {
                        margin-right: $de-btn-icon-margin;
                    }
                }
            }
        }
        .e-de-ctnr-group-btn .e-btn .e-btn-icon:not(.e-caret) {
            font-size: $de-btn-font-size;
        }
        .e-de-property-div-padding .e-de-cellmargin-text {
            padding-bottom: 3px;
        }
        .e-de-cntr-pane-padding.e-de-prop-separator-line>div>div:first-child>div:first-child {
            margin-bottom: 0 !important;
        }
    }
    @if $skin-name=='bootstrap' or $skin-name=='bootstrap4' or $skin-name=='tailwind' or $skin-name=='bootstrap5' or $skin-name =='FluentUI'  {
        .e-de-font-clr-picker .e-colorpicker-wrapper:first-child,
        .e-de-font-clr-picker>.e-split-btn-wrapper {
            margin-right: $de-pane-margin-right;
        }
        .e-de-font-clr-picker.e-rtl .e-colorpicker-wrapper:first-child,
        .e-de-font-clr-picker.e-rtl>.e-split-btn-wrapper {
            margin-left: $de-pane-margin-right;
            margin-right: 0;
        }
    }
    @if $skin-name=='bootstrap-dark' {
        .e-de-font-clr-picker .e-colorpicker-wrapper:first-child,
        .e-de-font-clr-picker>.e-split-btn-wrapper {
            margin-right: $de-pane-margin-right;
        }
        .e-de-font-clr-picker.e-rtl .e-colorpicker-wrapper:first-child,
        .e-de-font-clr-picker.e-rtl>.e-split-btn-wrapper {
            margin-left: $de-pane-margin-right;
            margin-right: 0;
        }
    }
    .e-de-ctnr-segment>div:first-child:not(.e-rtl),
    .e-de-ctnr-segment-list>div:last-child:not(.e-rtl),
    .e-de-ctnr-segment>button:first-child:not(.e-rtl) {
        margin-right: $de-pane-margin-right;
    }
    .e-de-ctnr-segment.e-de-ctnr-segment-rtl>div:first-child,
    .e-de-ctnr-segment-list.e-de-ctnr-segment-list-rtl>div:last-child,
    .e-de-ctnr-segment.e-de-ctnr-segment-rtl>button:first-child {
        margin-left: $de-pane-margin-right;
        margin-right: 0;
    }
    .e-de-tlbr-wrapper {
        background-color: $de-prpty-btn-bg-clr;
        height: $de-ctnr-tlbr-height;
        width: $de-ctnr-tlbr-width;
    }
    .e-de-ctnr-prop-label {
        color: $de-panel-header-color;
        display: inline-block;
        font-size: $de-panel-header-size;
        font-weight: 500;
        @if $skin-name=='Material3' {
            letter-spacing: 0.3px;
        }
        @else {
            letter-spacing: 0.05px;
        }
        @if $skin-name=='tailwind' {
            line-height: 1.5;
        }
        margin-bottom: $de-ctnr-margin-bottom;
        opacity: 0.87;
    }
    .e-de-table-prop-label {
        margin-left: 12px;
    }
    .e-de-table-prop-label.e-de-rtl {
        margin-left: 0;
        margin-right: 12px;
    }
    .e-de-toolbar.e-toolbar {
        border-radius: 0;
        @if $skin-name =='FluentUI' {
            box-shadow: none;
        }
    }
    .e-de-ctnr-toolbar .e-toolbar-item.e-de-toolbar-btn-first {
        margin-left: 0;
        margin-right: $de-tlbr-margin-right;
    }
    .e-de-ctnr-toolbar.e-de-ctnr-rtl .e-toolbar-item.e-de-toolbar-btn-first {
        margin-left: $de-tlbr-margin-right;
        margin-right: 0;
    }
    .e-bigger .e-de-ctnr-toolbar .e-toolbar-item.e-de-toolbar-btn-first {
        margin-left: 0;
        margin-right: $de-tlbr-margin-right-big;
    }
    .e-bigger .e-de-ctnr-toolbar.e-de-ctnr-rtl .e-toolbar-item.e-de-toolbar-btn-first {
        margin-left: $de-tlbr-margin-right-big;
        margin-right: 0;
    }
    .e-de-ctnr-toolbar .e-toolbar-item.e-de-toolbar-btn-last {
        margin-left: $de-tlbr-margin-right;
        margin-right: 0;
    }
    .e-de-ctnr-toolbar.e-de-ctnr-rtl .e-toolbar-item.e-de-toolbar-btn-last {
        margin-left: 0;
        margin-right: $de-tlbr-margin-right;
    }
    .e-bigger .e-de-ctnr-toolbar .e-toolbar-item.e-de-toolbar-btn-last {
        margin-left: $de-tlbr-margin-right-big;
        margin-right: 0;
    }
    .e-bigger .e-de-ctnr-toolbar.e-de-ctnr-rtl .e-toolbar-item.e-de-toolbar-btn-last {
        margin-left: 0;
        margin-right: $de-tlbr-margin-right-big;
    }
    .e-de-toolbar.e-toolbar .e-toolbar-items {
        height: $de-ctnr-tlbr-height;
        .e-toolbar-item.e-de-separator {
            @if $skin-name=='fabric' {
                border: 1px solid $de-ctnr-separator-clr;
                border-width: 0 1px 0 0;
            }
            height: 59px;
            margin: $de-ctrnr-tblr-separator-margin;
        }
        .e-toolbar-item.e-de-toolbar-btn-start {
            margin-left: $de-tlbr-margin-first !important;
            margin-right: $de-tlbr-margin-right;
        }
        .e-toolbar-item.e-de-toolbar-btn-middle {
            margin-left: $de-tlbr-margin-right;
            margin-right: $de-tlbr-margin-right;
        }
        .e-toolbar-item.e-de-toolbar-btn-end {
            margin-left: $de-tlbr-margin-right;
            margin-right: $de-tlbr-margin-first;
        }
        .e-toolbar-item.e-de-image-focus :focus {
            background-color: $de-toggle-bg-color;
        }
    }
    @if $skin-name=='bootstrap' {
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn {
            flex-direction: column;
            height: $de-tlbr-btn-height;
            padding: 0 1px;
            padding-bottom: 5px;
            #{if(&, '&', '*')}:focus {
                padding: 0;
                padding-bottom: 5px;
            }
            #{if(&, '&', '*')}:active {
                padding: 0;
                padding-bottom: 5px;
            }
            #{if(&, '&', '*')}:hover {
                padding: 0;
                padding-bottom: 5px;
            }
        }
    }
    @if $skin-name !='bootstrap' {
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn {
            flex-direction: column;
            height: $de-tlbr-btn-height;
            padding: 0;
            padding-bottom: $de-toolbar-padding-bottom;
            #{if(&, '&', '*')}:focus {
                padding: 0;
                padding-bottom: $de-toolbar-padding-bottom;
            }
            #{if(&, '&', '*')}:active {
                padding: 0;
                padding-bottom: $de-toolbar-padding-bottom;
            }
            #{if(&, '&', '*')}:hover {
                padding: 0;
                padding-bottom: $de-toolbar-padding-bottom;
            }
        }
    }
    @if $skin-name=='bootstrap4' {
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn {
            flex-direction: column;
            height: $de-tlbr-btn-height;
            padding: 0 1px;
            padding-bottom: 5px;
            #{if(&, '&', '*')}:focus {
                padding: 0;
                padding-bottom: 5px;
            }
            #{if(&, '&', '*')}:active {
                padding: 0;
                padding-bottom: 5px;
            }
            #{if(&, '&', '*')}:hover {
                padding: 0;
                padding-bottom: 5px;
            }
        }
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-btn-icon {
            color: $de-toolbar-icon-clr;
            font-size: 16px;
        }
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover,
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus,
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active,
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:disabled {
            .e-btn-icon {
                color: $de-toggle-btn-color;
            }
        }
        .e-bigger {
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-btn-icon {
                color: $de-toolbar-icon-clr;
                font-size: 18px;
            }
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover,
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus,
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active,
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:disabled {
                .e-btn-icon {
                    color: $de-toggle-btn-color;
                }
            }
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
                font-size: 13px;
            }
        }
    }
    .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text {
        padding: 0;
    }
    .e-de-overlay {
        height: 100%;
        opacity: 0.5;
        pointer-events: none;
        touch-action: none;
        width: 100%;
    }
    @if $skin-name=='bootstrap4' or $skin-name=='bootstrap5' {
        .e-de-font-clr-picker .e-colorpicker-container .e-split-btn-wrapper .e-btn.e-icon-btn,
        .e-de-font-clr-picker .e-colorpicker-container .e-split-btn-wrapper .e-btn.e-icon-btn,
        .e-de-font-clr-picker .e-colorpicker-wrapper .e-btn.e-icon-btn,
        .e-de-font-clr-picker .e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn,
        .e-de-font-clr-picker .e-colorpicker-wrapper .e-btn.e-icon-btn.e-rtl {
            padding: 0;
        }

        .e-de-prop-font-colorpicker .e-btn.e-icon-btn,
        .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-split-colorpicker.e-split-btn {
            padding: 4px 8px !important;
        }

        .e-de-ctnr-style-div {
            width: $de-text-pane-style-width;
        }

        .e-btn-toggle {
            .e-tbar-btn-text {
                color: $de-toggle-btn-color !important;
            }
            .e-btn-icon {
                color: $de-toggle-btn-color !important;
            }
        }

        .e-de-ctnr-list,
        .e-de-list-header-presetmenu {
            color: $de-prop-btn-icon-clr;
        }
        .e-de-ctnr-group-btn {
            .e-btn {
                background: $de-prop-btn-bg-color;
                border-color: $de-prop-btn-border-color;
                #{if(&, '&', '*')}:focus {
                    background-color: $de-toggle-border-hover-color;
                    @if $skin-name !='bootstrap4' and $skin-name!='bootstrap5' {
                        border: 1px;
                    }
                    border-color: $de-toggle-border-color;
                    box-shadow: 0 0 0 0 $de-toggle-bg-color;
                    outline-color: $de-toggle-bg-color;
                }
                #{if(&, '&', '*')}:active,
                .e-btn-toggle {
                    background-color: $de-toggle-border-hover-color;
                    @if $skin-name !='bootstrap4' and $skin-name!='bootstrap5' {
                        border: 1px;
                    }
                    border-color: $de-toggle-border-color;
                }
                #{if(&, '&', '*')}:hover {
                    @if $skin-name !='tailwind' {
                        background-color: $de-toggle-hover-color;
                        @if $skin-name !='bootstrap4' and $skin-name!='bootstrap5' {
                            border: 1px;
                        }
                        @if $skin-name!='bootstrap5' {
                            border-color: $de-toggle-border-hover-color;
                        }
                    }
                }
            }
            @if $skin-name=='bootstrap4' {
                .e-split-btn-wrapper.e-rtl .e-split-btn {
                    border-color: $de-prop-btn-border-color;
                }
            }
        }
        .e-de-ctnr-group-btn .e-btn:disabled {
            background-color: $de-toggle-bg-color;
            border-color: $de-toggle-bg-color;
            opacity: 65%;
        }
        .e-de-ctnr-group-btn .e-btn:hover,
        .e-de-ctnr-group-btn .e-btn:focus,
        .e-de-ctnr-group-btn .e-btn:active,
        .e-de-ctnr-group-btn .e-btn:disabled {
            .e-btn-icon {
                @if $skin-name == 'bootstrap5' {
                    color: $de-white-color;
                }
                @else {
                  color: $de-toggle-btn-color;
                }
            }
        }
        .e-de-ctnr-group-btn .e-btn-icon {
            color: $de-prop-btn-icon-clr;
            font-size: $de-btn-font-size;
        }
        .e-bigger {
            .e-de-ctnr-group-btn .e-btn-icon {
                color: $de-prop-btn-icon-clr;
            }
        }
        .e-btn-toggle .e-btn-icon {
            color: $de-toggle-btn-color;
        }
        .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn,
        .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn-icon,
        .e-de-border-size-button,
        .e-de-border-size-button .e-btn-icon {
            background-color: $de-prop-btn-bg-color;
            border-color: $de-prop-btn-border-color;
            color: $de-prop-btn-icon-clr;
        }
        .e-de-border-size-button:focus,
        .e-de-border-size-button:hover,
        .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn:hover,
        .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn:focus {
            background-color: $de-toggle-bg-color;
            box-shadow: $de-toggle-btn-border;
            .e-btn-icon {
                background-color: $de-toggle-bg-color;
                box-shadow: $de-toggle-btn-border;
                color: $de-toggle-btn-color;
            }
        }
        .e-de-border-size-button .e-btn-icon:focus,
        .e-de-border-size-button .e-btn-icon:hover {
            background-color: $de-toggle-bg-color;
            box-shadow: $de-toggle-btn-border;
            color: $de-toggle-btn-color;
        }

        .e-de-pagenumber-text {
            border: none !important;
            // height: 22px !important;
            // margin-top: $de-pagenumber-text-margin-top !important;
        }

    }
    // .de-split-button>div:first-child {
    //     margin-right: $de-list-button-margin-right;
    // }
    .e-de-ctnr-properties-pane-btn {
        @if $skin-name == 'Material3' {
            width: 78px;
        }
        @else {
            width: 75px;
        }
    }
    .e-de-pane-enable-clr.e-de-ctnr-properties-pane-btn {
        .e-btn {
            color: $de-pane-color-border;
        }
    }
    .e-de-pane-disable-clr.e-de-ctnr-properties-pane-btn {
        .e-btn {
            color: $de-font-color;
        }
    }
    .e-de-ctnr-properties-pane-btn {
        .e-btn {
            background: $de-prpty-btn-bg-clr;
            border-radius: 0;
            box-shadow: none;
            color: $de-pane-color-border;
            min-height: 100%;
            min-width: 100%;
            #{if(&, '&', '*')}:focus {
                box-shadow: none;
            }
            #{if(&, '&', '*')}:active {
                box-shadow: none;
            }
            #{if(&, '&', '*')}:hover {
                box-shadow: none;
            }
        }
    }
    .e-de-showhide-btn {
        border: 0;
        height: $de-ctnr-tlbr-height;
    }
    .e-de-showhide-btn-rtl {
        border: 0;
        height: $de-ctnr-tlbr-height;
    }
    .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item:not(.e-separator) {
        padding: $de-ctrnr-tblr-item-padding;
    }
    .e-de-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text .e-de-text-wrap {
        @if $skin-name =='Material3' {
            line-height: 0.8;
        }
    }
    .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
        display: table;
        font-size: $de-toolbar-font-size;
        margin: $de-ctrnr-tblr-item-margin;
        padding: 0;
        white-space: normal;
    }
    .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item button.e-btn.e-tbtn-txt .e-icons.e-btn-icon,
    .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon {
        padding: 0 !important;
        min-height: 16px;
    }
    .e-de-hdr-ftr-frst-div {
        margin-bottom: $de-hdr-ftr-frst-div-mrgn-btm;
    }
    .e-de-hdr-ftr-top-div {
        margin-bottom: $de-hdr-ftr-top-div-mrgn-btm;
    }
    .e-de-cntr-pane-padding {
        padding: $de-ctnr-padding;
    }
    .e-de-prop-pane {
        height: 100%;
        min-height: 200px;
        overflow: auto;
        width: $de-text-pane-width;
    }
    .e-de-review-pane {
        background: $de-ctnr-prop-bg-clr;
        border-left: 1px solid $de-op-border-clr;
        height: 100%;
        min-height: 200px;
        overflow: auto;
        width: $e-de-review-pane-width;
        @if $skin-name == 'FluentUI' {
            .e-toolbar {
              box-shadow: none;
            }
        }
    }
    .e-bigger {
        .e-de-cntr-pane-padding {
            @if $skin-name=='FluentUI' { 
                padding: 24px;
            }
            @else {
                padding: 16px;
            }
        }
        .e-de-prop-pane {
            height: 100%;
            min-height: 200px;
            overflow: auto;
            width: $de-text-pane-width-big;
        }
        @if $skin-name=='bootstrap4' {
            .e-btn-toggle .e-tbar-btn-text {
                color: $de-toggle-btn-color !important;
            }
            .e-de-prop-font-colorpicker .e-split-btn-wrapper .e-btn {
                border-color: $de-clr-picker-border-clr;
            }
        }
        @if $skin-name=='tailwind' {
            .e-de-prop-sub-label {
                font-size: 14px;
            }
            .e-de-hdr-ftr-top-div,
            .e-de-hdr-ftr-frst-div {
                margin-bottom: 16px;
            }
        }
    }
    .e-de-property-div-padding {
        @if $skin-name=='Material3' {
            border-bottom: 1px solid $de-pane-separator;
        }
        @else {
            border-bottom: 0.5px solid $de-pane-separator;
        }
        //padding-bottom: $de-prop-div-padding-bottom;
        padding: $de-prop-div-padding-top;
    }
    .e-de-ctnr-dropdown-ftr {
        border-top: 1px solid $de-pane-separator;
        color: $de-panel-header-color;
        cursor: pointer;
        display: block;
        font-size: 12px;
        line-height: 40px;
        text-indent: 1.2em;
    }
    .e-de-char-fmt-btn-left>button,
    .e-de-insert-del-cell button {
        width: $de-left-btn-width;
    }
    .e-de-char-fmt-btn-right>button {
        width: $de-right-btn-width;
    }
    .e-de-panel-left-width {
        width: $de-left-panel-width;
    }
    .e-bigger {
        .e-de-panel-left-width {
            width: $de-left-panel-width-big;
        }
        .e-de-char-fmt-btn-left>button,
        .e-de-insert-del-cell button {
            width: $de-left-btn-width-big;
        }
    }
    .e-de-panel-right-width {
        width: $de-right-panel-width;
    }
    @if $skin-name=='bootstrap5' or $skin-name=='FluentUI' or $skin-name=='tailwind' { 
        .e-bigger {
            .e-de-panel-right-width {
                width: $de-right-font-size-width-big;
            }
            .e-de-char-fmt-btn-left > button,
            .e-de-char-fmt-btn-right>button {
                width: $de-right-btn-width-big;
            }
        }
    }
    .e-de-cntr-highlight-pane {
        border: 1px solid $de-panel-border;
    }

    .e-de-btn-hghlclr>button:first-child {
        padding: 1px !important;
    }
    .e-de-ctnr-hglt-color {
        font-size: 12px;
        font-weight: 400;
        height: $de-hlt-clr-height !important;
        width: $de-hlt-clr-height !important;
        @if $skin-name=='bootstrap' or $skin-name=='bootstrap4' or  $skin-name=='bootstrap5' or $skin-name=='tailwind' {
            border-radius: 4px;
        }
    }

    .e-de-font-clr-picker>div div button,
    .e-de-font-clr-picker>div button,
    .e-de-font-clr-picker>button {
        width: $de-clr-pckr-width;
    }

    .e-de-floating-menu.e-de-bullets-menu .e-de-floating-menuitem-md {
        height: 55px;
        padding: 4px !important;
        width: 60px;
    }
    .e-de-floating-menu.e-de-bullets-menu .e-de-floating-menuitem-md:hover {
        border: 3px solid $de-list-hover-bg;
        padding: 2px !important;
    }
    .e-de-floating-menu.e-de-bullets-menu .e-de-floating-bullet-menuitem-md {
        height: 38px;
        padding: 4px !important;
        width: 38px;
    }
    .e-de-floating-menu.e-de-bullets-menu .e-de-floating-bullet-menuitem-md:hover {
        border: 3px solid $de-list-hover-bg;
        padding: 2px !important;
    }
    .e-de-list-header-presetmenu {
        cursor: pointer;
        font-size: 11px;
        line-height: 14px;
        overflow: hidden;
        text-align: left;
        min-width: 50px;
        white-space: nowrap;
        width: 100%;
        @if $skin-name=='tailwind' {
            color: $icon-color;
        }
    }
    .e-de-bullet-list-header-presetmenu {
        cursor: pointer;
        font-size: 14px;
        left: -11px;
        line-height: $de-bullet-icon-line-height;
        min-width: 50px;
        overflow: hidden;
        position: relative;
        white-space: nowrap;
        width: 100%;
    }
    .e-rtl {
        .e-de-bullet-list-header-presetmenu {
            cursor: pointer;
            font-size: 14px;
            left: 10px;
            line-height: $de-bullet-icon-line-height;
            min-width: 50px;
            overflow: hidden;
            position: relative;
            white-space: nowrap;
            width: 100%;
        }
    }
    .e-de-bullet {
        font-size: 42px;
    }
    .e-de-list-header-presetmenu .e-de-list-line {
        border-bottom: 1px solid $de-prop-btn-icon-clr !important;
        margin-left: 5px;
        width: 100%;
    }
    .e-de-toc-optionsdiv {
        margin-bottom: 11.5px;
        margin-left: 5.5px;
        margin-top: 15.5px;
    }
    .e-de-toc-optionsdiv.e-de-rtl {
        margin-right: 5.5px;
        margin-left: 0;
    }
    .e-de-list-header-presetmenu div span {
        //color: #aaa; 
        display: inline-block;
        vertical-align: middle;
    }
    .e-de-floating-menu .e-de-floating-menuitem,
    .e-de-floating-menu .e-de-menuitem-none {
        cursor: pointer;
        height: 70px;
        padding: 0 !important;
        margin: 0 5px 5px 0 !important;
        width: 70px;
    }
    .e-de-list-thumbnail .e-de-list-items {
        float: left;
    }
    .e-de-list-thumbnail .e-de-list-items {
        border: 1px solid $de-list-thmbnail-border-clr;
        @if $skin-name =='Material3' {
            border-radius: 4px;
        }
        clear: initial;
        display: inline-block;
        height: auto;
        margin: 5px;
        padding: 2px;
        text-align: center;
        width: auto;
    }
    .e-de-list-items {
        cursor: pointer;
        background: $de-ctnr-bg-clr;
        box-sizing: border-box;
        list-style: none;
        padding: 7px 10px 7px 10px;
        position: relative;
    }
    .e-de-list-item-size {
        font-size: 14px;
    }
    .e-de-floating-menuitem.e-de-floating-menuitem-md.e-de-list-items.e-de-list-item-size.de-list-item-selected,
    .e-de-floating-menuitem.e-de-floating-bullet-menuitem-md.e-de-list-items.e-de-list-item-size.de-list-item-selected {
        border: 3px solid $de-title-bar-bg-clr;
        padding: 2px !important;
    }
    .e-de-floating-menu {
        padding: 10px 4px 5px 10px !important;
    }
    @if $skin-name !='tailwind' {
        .e-de-list-container {
            @if $skin-name !='Material3' {
                border: 1px solid #ccc;
                border-radius: 2px;
                box-shadow: 0 0 14px rgba(0, 0, 0, 0.2);
            }
            background: $de-ctnr-bg-clr;
            box-sizing: border-box;
            display: inline-block;
            line-height: normal;
            margin: 0;
            outline: 0;
			background: $de-ctnr-bg-clr;
            @if $skin-name =='Material3' {
                padding: 10px 0;
            }
            @else {
                padding: 5px 0;
            }
            position: absolute;
            width: auto;
            z-index: 10020;
        }
    }
    .e-de-ctnr-list {
        font-size: $de-bullet-icon-font-size;
        @if ( $skin-name !='bootstrap4' and $skin-name !='tailwind') {
            vertical-align: top;
        }
    }
    .e-de-image-property {
        padding-left: 32px;
    }
    .e-de-img-prty-span {
        color: $de-font-color;
        left: $de-img-span-left;
        position: absolute;
        top: $de-img-span-top;
    }
    .e-btn-toggle {
        background-color: $de-toggle-bg-color !important;
        @if $skin-name=='highcontrast' {
            color: $de-title-bar-font-clr !important;
        }
        @if $skin-name=='fabric' {
            border-color: $de-toggle-bg-color !important;
        }
        outline: $de-toggle-btn-outline;
        outline-offset: 0;
        box-shadow: $de-toggle-btn-border !important;
        #{if(&, '&', '*')}:hover {
            background-color: $de-toggle-bg-color !important;
            @if $skin-name=='fabric' {
                border-color: $de-toggle-bg-color !important;
            }
            outline: $de-toggle-btn-outline;
            outline-offset: 0;
            box-shadow: $de-toggle-btn-border !important;
        }
    }
    @if $skin-name=='highcontrast' {
        .e-btn-toggle .e-tbar-btn-text {
            color: $de-title-bar-font-clr !important;
        }
        .e-btn-toggle>span {
            color: $de-title-bar-font-clr !important;
        }
    }
    @if $skin-name=='bootstrap' or $skin-name=='bootstrap4' or $skin-name=='tailwind' or or $skin-name=='bootstrap5' or $skin-name =='FluentUI' {
        .e-de-ctnr-group-btn-top>button:first-child {
            border-radius: 0;
            border-top-left-radius: 4px;
        }
        .e-de-ctnr-group-btn-top.e-de-rtl>button:first-child {
            border-top-left-radius: 0;
            border-top-right-radius: 4px;
        }
        .e-de-ctnr-group-btn-top>button:last-child {
            border-radius: 0;
            border-top-right-radius: 4px;
        }
        .e-de-ctnr-group-btn-top.e-de-rtl>button:last-child {
            border-top-left-radius: 4px;
            border-top-right-radius: 0;
        }
        .e-de-ctnr-group-btn-middle>button {
            border-radius: 0;
            border-top: 0;
            border-bottom: 0;
        }
        .e-de-ctnr-group-btn-bottom>button:first-child {
            border-radius: 0;
            border-bottom-left-radius: 4px;
        }
        .e-de-ctnr-group-btn-bottom.e-de-rtl>button:first-child {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 4px;
        }
        .e-de-ctnr-group-btn-bottom>button:last-child {
            border-radius: 0;
            border-bottom-right-radius: 4px;
        }
        .e-de-ctnr-group-btn-bottom.e-de-rtl>button:last-child {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 4px;
        }
    }
    .e-de-toc-template1 {
        @if $skin-name != 'tailwind' {
            background: $de-ctnr-bg-clr;
        }
        border: 1px solid $de-panel-border;
        color: $de-panel-header-color;
        @if $skin-name=='Material3' {
            height: 130px;
            width: 95px;
            margin-left: 80px;
        }
        @else {
            height: 129px;
            width: 94px;
            margin-left: 78px;
        }
        
        
    }
    .e-de-toc-template1.e-de-rtl {
        margin-left: 0;
        margin-right: 78px;
    }

    .e-de-toc-template1-content1 {
        font-size: 10px;
        @if $skin-name=='Material3' {
            height: 16px;
            margin-left: 8px;
            margin-top: 6px;
        }
        @else {
            height: 11px;
            margin-left: 5.4px;
            margin-top: 6.7px;
        }
        width: $de-toc-template-content1;
    }
    .e-de-toc-template1-content2 {
        font-size: 8px;
        height: 9px;
        @if $skin-name=='Material3' {
            margin-left: 23px;
            margin-top: 6px;
        }
        @else {
            margin-left: 20.4px;
            margin-top: 5.7px;
        }
        width: $de-toc-template-content2;
    }
    .e-de-toc-template1-content3 {
        font-size: 7px;
        height: 8px;
        @if $skin-name=='Material3' {
            margin-left: 30px;
            margin-top: 6px;
        }
        @else {
            margin-left: 28.4px;
            margin-top: 6.7px;
        }
        width: $de-toc-template-content3;
    }
    .e-de-prop-sub-label {
        color: $de-panel-sub-label-color;
        @if $skin-name=='tailwind' {
            font-size: 12px;
            line-height: 18px;
        }
        @else if $skin-name=='Material3' {
            font-size: $de-toolbar-font-size;
        }
        @else {
            font-size: 13px;
        }
        margin-bottom: $de-prop-sub-label-mrgn-btm;
        font-weight: $de-header-font-weight;
    }
    .e-de-toc-checkbox1 {
        height: $de-prop-pane-margin;
        margin-top: $de-prop-pane-margin;
    }
    .e-de-toc-checkbox2 {
        height: $de-prop-pane-margin;
        margin-top: $de-prop-pane-margin;
    }
    .e-de-toc-checkbox3 {
        height: $de-prop-pane-margin;
        margin-top: $de-prop-pane-margin;
    }
    .e-de-status-bar {
        background: $de-ctnr-bg-clr;
        //border-bottom: 1px solid $de-panel-border;
        display: flex;
        @if $skin-name !='Material3' {
            padding-top: $e-de-status-bar-padding-top;
        }
        width: 100%;
    }
    .e-de-ctnr-pg-no {
        color: $de-font-color;
        display: inline-flex;
        font-size: $de-ctnr-pg-no-spellout-fnt-size;
        height: 100%;
        padding-top: $e-de-ctnr-pg-no-spellout-padd-top;

        span {
            border: 1px solid transparent;
        }
    }
    .e-de-ctnr-pg-no-spellout {
        color: $de-font-color;
        display: inline-flex;
        font-size: $de-ctnr-pg-no-spellout-fnt-size;
        height: 100%;
        padding-top: $e-de-ctnr-pg-no-spellout-padd-top;
        width: $de-page-no-width-spellout;

        span {
            border: 1px solid transparent;
        }
    }
    .e-bigger .e-de-ctnr-pg-no-spellout {
        color: $de-font-color;
        display: inline-flex;
        font-size: $de-ctnr-pg-no-spellout-fnt-size-bg;
        height: 100%;
        padding-top: $e-de-ctnr-pg-no-spellout-padd-top-big;
        width: $de-page-no-width-spellout-big;
    }
    .e-de-statusbar-zoom-spell {
        @if $skin-name =='Material3' {
            background: $de-ctnr-bg-clr;
        } 
        @else {
            background-color: $de-ctnr-bg-clr;
        }
        border: 0;
        color: $de-font-color;
        float: right;
        height: 34px;
        margin-left: $de-zoom-tlbr-margin-left;
    }
    .e-bigger .e-de-statusbar-zoom-spell {
        @if $skin-name =='Material3' {
            background: $de-ctnr-bg-clr;
        } 
        @else {
            background-color: $de-ctnr-bg-clr;
        }
        border: 0;
        color: $de-font-color;
        float: right;
        height: 34px;
        margin-left: $de-zoom-tlbr-margin-left-big;
    }
    .e-de-btn-cancel {
        margin-left: 10px;
    }
    .e-de-btn-cancel-rtl {
        margin-left: 0;
        margin-right: 10px;
    }
    .e-de-prop-header-label {
        color: $de-panel-header-color;
        display: inline-block;
        @if $skin-name =='tailwind' or $skin-name =='bootstrap5' or $skin-name =='FluentUI' {
            font-size: $de-panel-header-size;
        }
        @else if $skin-name == 'Material3' {
            font-size: $de-toolbar-font-size;
        }
        @else {
            font-size: 13px;
        }
        font-weight: bold;
        letter-spacing: 0.05px;
        opacity: 0.87;
    }
    .e-de-prop-separator-line {
        border-bottom: $de-header-line-color;
    }
    .e-de-status-bar>div label {
        font-weight: $de-status-br-lbl-fnt-weight;
    }
    .e-de-stylediv {
        padding-left: $de-prop-style-padding;
    }
    .e-de-stylediv-rtl {
        padding-left: 0;
        padding-right: $de-prop-style-padding;
    }
    // .e-de-border-style-div {
    //     margin-left: 12px;
    // }
    // .e-de-border-style-div.e-de-rtl {
    //     margin-left: 0;
    //     margin-right: 12px;
    // }
    // .e-de-insert-del-cell {
    //     margin-left: 12px;
    // }
    // .e-de-insert-del-cell.e-de-rtl {
    //     margin-left: 0;
    //     margin-right: 12px;
    // }
    // .e-de-cell-margin {
    //     margin-left: 12px;
    // }
    // .e-de-align-text {
    //     margin-left: 12px;
    // }
    // .e-de-align-text.e-de-rtl {
    //     margin-left: 0;
    //     margin-right: 12px;
    // }
    .e-de-border-size-button {
        height: $de-border-size-button-height;
        margin-top: $de-table-prop-border-margin;
        width: $de-border-size-button-width;
    }
    .e-de-color-picker {
        height: $de-border-size-button-height;
        width: $de-border-size-button-width;
    }
    // .e-de-cell-div {
    //     margin-left: $de-prop-style-padding;
    // }
    // .e-de-cell-div.e-de-rtl {
    //     margin-left: 0;
    //     margin-right: $de-prop-style-padding;
    // }
    .e-de-cell-text-box {
        margin-right: $de-prop-style-padding;
    }
    .e-de-pane-rtl {
        .e-de-cell-text-box {
            margin-left: $de-prop-style-padding;
            margin-right: 0;
        }
    }
    .e-de-prop-fill-label {
        //margin-left: 10.3px;
        @if $skin-name =='Material3' {
            margin-right: 16px;
        }
        @else {
            margin-right: 8px;
        }
    }
    .e-de-prop-fill-label.e-de-rtl {
        @if $skin-name =='Material3' {
            margin-left: 16px;
        }
        @else {
            margin-left: 8px;
        }
        margin-right: 0;
    }

    .e-de-grp-btn-ctnr .e-de-ctnr-group-btn {
        height: 36px !important;
    }
    .e-bigger .e-de-grp-btn-ctnr .e-de-ctnr-group-btn {
        height: 42px !important;
    }

    .e-de-grp-btn-ctnr .e-de-ctnr-group-btn>button {
        @if $skin-name =='bootstrap5' { 
          width: 39px;
          height: 38px;
        }
        @else if $skin-name == 'Material3' {
          width: 36px;
          height: 36px;
        }
        @else {
          width: 38px;
          height: 38px;
        }   
    }
    .e-bigger .e-de-grp-btn-ctnr .e-de-ctnr-group-btn>button {
        height: 42px;
        width: $de-border-btn-width-big;
    }
    .e-de-border-clr-picker .e-split-btn-wrapper>button:first-child {
        @if $skin-name=='Material3' {
            width: 64px;
        }
        @else {
            width: 70px;
        }
    }
    .e-documenteditor-optionspane {
        @if $skin-name=='Material3' {
            background: $de-background-color;
        }
    }
    @if $skin-name=='tailwind' {
        .e-de-prop-fill-label {
            font-size: 16px;
            line-height: 24px;
            color: $de-panel-header-color;
        }
        .e-documenteditorcontainer {
            .e-tab .e-content .e-item {
                padding: 0;
            }
        }
    }
    .e-rtl {
        .e-listview .e-list-icon {
            height: 24px;
            width: 16px;
            margin-left: $e-de-ctnr-break-listview-margin-left;
        }
        .e-de-listview-icon {
            height: auto;
            width: auto;
            line-height: 22px;
            margin-left: $e-de-ctnr-break-listview-margin-left;
        }
    }
    .e-bigger {
        @if $skin-name =='tailwind' or $skin-name =='bootstrap5' or $skin-name =='FluentUI' or $skin-name =='bootstrap4' {
            .de-split-button>div:first-child {
                margin-right: 0;
            }
        }
        @else {
        .de-split-button>div:first-child {
            margin-right: $de-list-button-margin-right-bigger;
        }
        }
        .e-de-border-clr-picker .e-split-btn-wrapper>button:first-child {
            @if $skin-name=='Material3' {
                width: 60px;
            }
            @else {
                width: 66px;
            }
        }
        .e-de-prop-fill-label {
            margin-left: 0;
            margin-right: 9.8px;
        }
        .e-de-prop-fill-label.e-de-rtl {
            margin-left: 9.8px;
            margin-right: 0px;
        }
        // .e-de-cell-text-box {
        //     margin-right: 16px;
        // }
        .e-rtl {
            .e-de-cell-text-box {
                margin-left: 16px;
                margin-right: 0;
            }
        }
        .e-de-cell-div {
            //margin-left: 16px;
            @if $skin-name =='bootstrap5' {
                button {
                   width: 45px !important;
                }
            }
        }

        .e-de-color-picker {
            height: $de-border-size-button-height-bg;
            width: $de-border-size-button-width-bg;
        }
        .e-de-border-size-button {
            height: $de-border-size-button-height-bg;
            margin-top: $de-table-prop-border-margin;
            width: $de-border-size-button-width-bg;
        }

        .e-de-stylediv {
            padding-left: 16px;
        }
        .e-de-stylediv-rtl {
            padding-right: 16px;
        }
        .e-de-tool-ctnr-properties-pane {
            display: flex;
            height: $de-tool-ctnr-editor-height-big;
            min-height: 200px;
            width: 100%;
        }
        .e-de-ctnr-properties-pane {
            display: flex;
            height: $de-ctnr-editor-height-big;
            width: 100%;
        }
        .e-de-ctn {
            background-color: $de-ctnr-bg-clr;
            border: 1px solid $de-panel-border;
            height: $de-ctnr-height;
            position: relative;
            width: 100%;
        }
        .e-de-ctnr-toolbar {
            display: flex;
            height: $de-ctnr-tlbt-height-big;
            width: 100%;
        }
        .e-de-tlbr-wrapper .e-de-toolbar.e-toolbar,
        .e-de-tlbr-wrapper .e-de-ctnr-properties-pane-btn {
            border: 0;
        }
        .e-de-pane {
            border-left: 1px solid $de-panel-border;
        }
        .e-de-pane-rtl {
            border-right: 1px solid $de-panel-border;
        }
        @if $skin-name=='material' {
            .e-de-pane>div button:not(.e-primary),
            .e-de-pane-rtl>div button:not(.e-primary) {
                background-color: $de-panel-btn-bg-clr;
            }
        }
        .e-de-ctnr-segment {
            margin-bottom: $de-ctnr-margin-bottom-big;
        }
        @if $skin-name=='bootstrap' or $skin-name=='bootstrap4' or $skin-name=='tailwind' or $skin-name=='bootstrap5' {
            .e-de-font-clr-picker .e-colorpicker-wrapper:first-child,
            .e-de-font-clr-picker>.e-split-btn-wrapper {
                margin-right: $de-pane-margin-right-big;
            }
            .e-de-font-clr-picker.e-rtl .e-colorpicker-wrapper:first-child,
            .e-de-font-clr-picker.e-rtl>.e-split-btn-wrapper {
                margin-left: $de-pane-margin-right-big;
                margin-right: 0;
            }
        }
        @if $skin-name=='bootstrap-dark' {
            .e-de-font-clr-picker .e-colorpicker-wrapper:first-child,
            .e-de-font-clr-picker>.e-split-btn-wrapper {
                margin-right: $de-pane-margin-right-big;
            }
            .e-de-font-clr-picker.e-rtl .e-colorpicker-wrapper:first-child,
            .e-de-font-clr-picker.e-rtl>.e-split-btn-wrapper {
                margin-left: $de-pane-margin-right-big;
                margin-right: 0;
            }
        }
        
        .e-de-ctnr-segment>div:first-child:not(.e-rtl),
        .e-de-ctnr-segment-list>div:last-child:not(.e-rtl),
        .e-de-ctnr-segment>button:first-child:not(.e-rtl) {
            margin-right: $de-pane-margin-right-big;
        }
        
        .e-de-ctnr-segment.e-de-ctnr-segment-rtl>div:first-child,
        .e-de-ctnr-segment-list.e-de-ctnr-segment-list-rtl>div:last-child,
        .e-de-ctnr-segment.e-de-ctnr-segment-rtl>button:first-child {
            margin-left: $de-pane-margin-right-big;
            margin-right: 0;
        }
        .e-de-tlbr-wrapper {
            background-color: $de-prpty-btn-bg-clr;
            height: $de-ctnr-tlbt-height-big;
            width: $de-ctnr-tlbr-width;
        }
        .e-de-ctnr-prop-label {
            color: $de-panel-header-color;
            display: inline-block;
            @if $skin-name =='tailwind' or $skin-name =='bootstrap5' or $skin-name =='FluentUI' {
                font-size: 16px;
            }
            @else {
                font-size: $de-panel-header-size;
            }
            font-weight: 500;
            letter-spacing: 0.05px;
            margin-bottom: $de-ctnr-margin-bottom-big;
            opacity: 0.87;
            @if $skin-name=='tailwind' {
                line-height: 1.5;
            }
        }
        .e-de-table-prop-label {
            margin-left: 14.5px;
        }
        .e-de-table-prop-label.e-de-rtl {
            margin-left: 0;
            margin-right: 14.5px;
        }
        .e-de-toolbar.e-toolbar .e-toolbar-items {
            height: $de-ctnr-tlbt-height-big;
            .e-toolbar-item.e-de-separator {
                margin: $de-ctrnr-tblr-separator-margin-big;
            }
            .e-toolbar-item.e-de-toolbar-btn-start {
                margin-left: $de-tlbr-margin-first-big !important;
                margin-right: $de-tlbr-margin-right-big;
            }
            .e-toolbar-item.e-de-toolbar-btn-middle {
                margin-left: $de-tlbr-margin-right-big;
                margin-right: $de-tlbr-margin-right-big;
            }
            .e-toolbar-item.e-de-toolbar-btn-end {
                margin-left: $de-tlbr-margin-right-big;
                margin-right: $de-tlbr-margin-first-big;
            }
            .e-toolbar-item.e-de-image-focus :focus {
                background-color: $de-toggle-bg-color;
            }
        }
        @if $skin-name=='bootstrap' {
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn,
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control {
                padding: 0 1px;
                padding-bottom: 6px;
                #{if(&, '&', '*')}:focus {
                    padding: 0;
                    padding-bottom: 6px;
                }
                #{if(&, '&', '*')}:active {
                    padding: 0;
                    padding-bottom: 6px;
                }
                #{if(&, '&', '*')}:hover {
                    padding: 0;
                    padding-bottom: 6px;
                }
            }
        }
        @if $skin-name !='bootstrap' {
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn,
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control {
                padding: 0;
                padding-bottom: $de-toolbar-padding-bottom-bg;
                #{if(&, '&', '*')}:focus {
                    padding: 0;
                    padding-bottom: $de-toolbar-padding-bottom-bg;
                }
                #{if(&, '&', '*')}:active {
                    padding: 0;
                    padding-bottom: $de-toolbar-padding-bottom-bg;
                }
                #{if(&, '&', '*')}:hover {
                    padding: 0;
                    padding-bottom: $de-toolbar-padding-bottom-bg;
                }
            }
        }
        @if $skin-name=='bootstrap4' {
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn,
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control {
                padding: 0 1px;
                padding-bottom: 6px;
                #{if(&, '&', '*')}:focus {
                    padding: 0;
                    padding-bottom: 6px;
                }
                #{if(&, '&', '*')}:active {
                    padding: 0;
                    padding-bottom: 6px;
                }
                #{if(&, '&', '*')}:hover {
                    padding: 0;
                    padding-bottom: 6px;
                }
            }
        }
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text {
            padding: 0 !important;
        }
        .e-de-overlay {
            height: 100%;
            opacity: 0.5;
            pointer-events: none;
            touch-action: none;
        }
        .e-de-ctnr-properties-pane-btn {
            @if $skin-name=='Material3' {
                width: 78px;
            }
            @else {
                width: 75px;
            }
        }
        .e-de-ctnr-properties-pane-btn {
            .e-btn {
                background: $de-prpty-btn-bg-clr;
                border-radius: 0;
                box-shadow: none;
                min-height: 100%;
                min-width: 100%;
                #{if(&, '&', '*')}:focus {
                    box-shadow: none;
                }
                #{if(&, '&', '*')}:active {
                    box-shadow: none;
                }
                #{if(&, '&', '*')}:hover {
                    box-shadow: none;
                }
            }
        }
        .e-de-showhide-btn {
            border: 0;
            height: $de-ctnr-tlbt-height-big;
        }
        .e-de-showhide-btn-rtl {
            border: 0;
            height: $de-ctnr-tlbt-height-big;
        }
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item:not(.e-separator) {
            padding: $de-ctrnr-tblr-item-padding;
        }
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
            display: table;
            @if $skin-name=='material' {
                font-size: 13px;
            }
            @if $skin-name=='material3' {
                font-size: $text-base;
            }
            @else if $skin-name=='tailwind' {
                font-size: 14px;
                line-height: 1.5;
            }
            @else if $skin-name=='bootstrap5' or $skin-name =='FluentUI' {
                font-size: 16px;
            }
            @else {
                font-size: 12px !important;
            }
            margin: $de-ctrnr-tblr-item-margin-big;
            padding: 0;
            white-space: normal;
        }
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item button.e-btn.e-tbtn-txt .e-icons.e-btn-icon,
        .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon {
            @if $skin-name=='material' {
                font-size: 16px;
            }
            padding: 0;
        }
        .e-de-ctnr-group-btn.e-btn-group:not(.e-outline) {
            box-shadow: none;
            height: $de-group-btn-hgt-big;
            @if $skin-name=='bootstrap5' {
                padding-left: 1px;
            }
            #{if(&, '&', '*')}:focus {
                box-shadow: none;
            }
            #{if(&, '&', '*')}:active {
                box-shadow: none;
            }
            #{if(&, '&', '*')}:hover {
                box-shadow: none;
            }
        }
        .e-de-status-bar button
        {
            @if $skin-name=='Material3' {
                height: $de-status-btn-hgt-big;
            } @else {
                height: $de-group-btn-hgt-big;
            }
            box-shadow: none;
            #{if(&, '&', '*')}:focus {
                box-shadow: none;
            }
            #{if(&, '&', '*')}:active {
                box-shadow: none;
            }
            #{if(&, '&', '*')}:hover {
                box-shadow: none;
            }       
        }        
        .e-de-ctnr-group-btn button {
            box-shadow: none;
            height: $de-group-btn-hgt-big;
            #{if(&, '&', '*')}:focus {
                box-shadow: none;
            }
            #{if(&, '&', '*')}:active {
                box-shadow: none;
            }
            #{if(&, '&', '*')}:hover {
                box-shadow: none;
            }
        }
        @if $skin-name !='tailwind' {
            .e-de-property-div-padding {
                border-bottom: 0.5px solid $de-pane-separator;
                @if $skin-name =='FluentUI' {
                    padding: 24px;
                }
                @else {
                    padding: 16px;
                }
            }
            .e-de-font-clr-picker>div button,
            .e-de-font-clr-picker>button {
                @if $skin-name=='bootstrap5' {
                    width:43px;
                }
                @else {
                    width: auto;
                }
            }
        }
        .e-de-ctnr-dropdown-ftr {
            border-top: 1px solid $de-pane-separator;
            color: $de-panel-header-color;
            cursor: pointer;
            display: block;
            font-size: 12px;
            line-height: 40px;
            text-indent: 1.2em;
        }
        .e-de-char-fmt-btn>button {
            width: 38.5px;
        }
        .e-de-btn-hghlclr>button:first-child {
            padding: 0 6px !important;
        }
        .e-de-ctnr-hglt-color {
            font-size: 12px;
            font-weight: 400;
            height: $de-hlt-clr-height-bigger !important;
            width: $de-hlt-clr-height-bigger !important;
            @if $skin-name=='bootstrap' or $skin-name=='bootstrap4' or  $skin-name=='bootstrap5' or $skin-name=='tailwind' {
                border-radius: 4px;
            }
        }

        @if $skin-name=='bootstrap' or $skin-name=='bootstrap4' or $skin-name=='tailwind' {
            .e-de-font-clr-picker>div div button {
                width: 38px;
            }
        }
        @if $skin-name=='bootstrap' or $skin-name=='bootstrap4' {
            .e-de-font-clr-picker>div div button {
                width: 38px;
            }
        }
        .e-de-ctnr-list {
            font-size: $de-bullet-icon-font-size;
            vertical-align: top;
        }
        .e-de-image-property {
            padding-left: 32px;
        }
        .e-de-img-prty-span {
            color: $de-font-color;
            left: 10px;
            position: absolute;
            top: $de-img-span-top-big;
        }
        .e-btn-toggle {
            background-color: $de-toggle-bg-color !important;
            box-shadow: $de-toggle-btn-border !important;
            @if $skin-name=='fabric' {
                border-color: $de-toggle-bg-color !important;
            }
            outline: $de-toggle-btn-outline;
            outline-offset: 0;
            #{if(&, '&', '*')}:hover {
                background-color: $de-toggle-bg-color !important;
                @if $skin-name=='fabric' {
                    border-color: $de-toggle-bg-color !important;
                }
                outline: $de-toggle-btn-outline;
                outline-offset: 0;
                box-shadow: $de-toggle-btn-border !important;
            }
        }
        .e-de-toc-template1 {
            background: $de-ctnr-bg-clr;
            border: 1px solid $de-panel-border;
            color: $de-panel-header-color;
            height: 129px;
            margin-left: 78px;
            width: 94px;
        }
        .e-de-toc-template1-content1 {
            font-size: 10px;
            height: 11px;
            margin-left: 5.4px;
            margin-top: 6.7px;
            width: $de-toc-template-content1;
        }
        .e-de-toc-template1-content2 {
            font-size: 8px;
            height: 9px;
            margin-left: 20.4px;
            margin-top: 5.7px;
            width: $de-toc-template-content2;
        }
        .e-de-toc-template1-content3 {
            font-size: 7px;
            height: 8px;
            margin-left: 28.4px;
            margin-top: 6.7px;
            width: $de-toc-template-content3;
        }
        .e-de-toc-optionsdiv {
            margin-bottom: 11.5px;
            margin-left: 5.5px;
            margin-top: 15.5px;
        }
        .e-de-toc-optionsdiv.e-de-rtl {
            margin-right: 5.5px;
            margin-left: 0;
        }
        @if $skin-name !='tailwind' {
            .e-de-prop-sub-label {
                font-size: 13px;
                margin-bottom: 8.5px;
            }
        }
        .e-de-btn-cancel {
            margin-left: 10px;
        }
        .e-de-status-bar {
            background: $de-ctnr-bg-clr;
            //border-bottom: 1px solid $de-panel-border;
            display: flex;
            @if $skin-name !='Material3' {
                padding-top: $e-de-status-bar-big-padding-top;
            }
            width: 100%;
        }
        .e-de-statusbar-zoom {
            border: 0;
            color: $de-font-color;
            float: right;
            @if $skin-name =='Material3' {
                height: 40px;
                background: $de-ctnr-bg-clr;
            }
            @else {
                height: 34px;
                background-color: $de-ctnr-bg-clr;
            }
        }
        @if $skin-name !='tailwind' {
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
                line-height: 25px;
                @if $skin-name !='material' {
                    padding: 0 5px !important;
                }
            }
            .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text .e-de-text-wrap {
                @if $skin-name !='material' {
                    height: 0;
                }
                @if $skin-name == 'material' {
                    height: 6px;
                }
                @if  $skin-name == 'bootstrap5' or $skin-name == 'FluentUI' {
                    line-height: .5;
                }
                @else {
                    line-height: 0;
                }
                @if $skin-name =='Material3' {
                    line-height: 0.8;
                }
            }
        }
        .e-listview .e-list-icon {
            height: 24px;
            width: 16px;
            margin-right: $e-de-ctnr-break-listview-margin-right-big;
        }
        .e-de-listview-icon {
            height: auto;
            width: auto;
            line-height: 22px;
            margin-right: $e-de-ctnr-break-listview-margin-right-big;
        }
        .e-rtl {
            .e-listview .e-list-icon {
                height: 24px;
                width: 16px;
                margin-left: $e-de-ctnr-break-listview-margin-left-big;
            }
            .e-de-listview-icon {
                height: auto;
                width: auto;
                line-height: 22px;
                margin-left: $e-de-ctnr-break-listview-margin-left-big;
            }
        }
    }
}

.e-de-ctn .e-de-bzr-button {
    @if $skin-name !='Material3' {
        @if $skin-name!='tailwind' and $skin-name!='tailwind-dark' and $skin-name !='bootstrap5' {
            font-size: 12px;
        }
        @if $skin-name !='bootstrap4' and $skin-name !='bootstrap5' {
            font-weight: 400;
        }
        
        background: $e-de-bzr-button-bg-color;
        @if $skin-name!='tailwind' and $skin-name!='tailwind-dark' and $skin-name !='material' and $skin-name !='material-dark' {
            height: 100%;
        }
        @if $skin-name !='fabric'and $skin-name !='material' and $skin-name !='material-dark' {
            color: $e-de-bzr-button-font-color;
        }
    }
    
    @if $skin-name=='bootstrap4' or $skin-name=='bootstrap' or $skin-name=='bootstrap-dark' {
        border: 1px solid $e-de-bzr-button-border-color;
    }
    @if $skin-name=='highcontrast' or $skin-name=='bootstrap' or $skin-name=='bootstrap-dark' or $skin-name=='fabric' or $skin-name=='fabric-dark' {
        box-shadow: none;
        border: none;
    }
    @if $skin-name=='highcontrast' {
        border: 2px solid $e-de-bzr-button-border-color;
    }
    @if $skin-name=='bootstrap' or $skin-name=='bootstrap-dark' {
        padding-bottom: 6px;
    }
    @if $skin-name=='tailwind' or $skin-name=='tailwind-dark' {
        border: none;
        height: calc(100% - 10px);
        line-height: 1.3 !important;
        padding: 10px 6px 10px 6px !important;
        font-size: 14px !important;
    }
}
@if $skin-name =='fabric' {
    .e-de-ctn .e-de-bzr-button:focus {
        outline: none;
    }
}
@if $skin-name !='material' and $skin-name !='material-dark' {
    .e-de-ctn .e-de-bzr-button:hover {
        background: $e-de-bzr-button-hover-bg-color;
        border: $e-de-bzr-button-hover-border;
        border-radius: $e-de-bzr-button-hover-border-radius;
        @if $skin-name !='tailwind' and $skin-name !='tailwind-dark' {
            color: $e-de-bzr-button-hover-font-color;
        }
    }
    @if $skin-name=='bootstrap4' or $skin-name=='bootstrap5' {
        .e-de-ctn .e-de-bzr-button:hover .e-btn-icon {
            color: $e-de-bzr-button-hover-font-color;
        }
    }
}
@if $skin-name !='bootstrap' and $skin-name!='bootstrap-dark' and $skin-name!='tailwind' and $skin-name!='tailwind-dark' {
    .e-de-ctn .e-de-bzr-button:active {
        @if $skin-name !='bootstrap4' {
            box-shadow: none;
        }
        @if $skin-name=='fabric' or $skin-name=='highcontrast' {
            border-radius: 0;
        }
        @if $skin-name !='material' and $skin-name !='material-dark' {
            background-color: $e-de-bzr-button-active-bg-color;
            border-color: $e-de-bzr-button-active-border-color;
            color: $e-de-bzr-button-active-font-color;
        }
    }
    @if $skin-name=='highcontrast' {
        .e-de-ctn .e-de-bzr-button:active .e-btn-icon {
            color: $e-de-bzr-button-active-font-color;
        }
    }
}


@if $skin-name=='material' or $skin-name=='bootstrap' or $skin-name=='bootstrap4' or $skin-name=='fabric' or $skin-name=='highcontrast' or $skin-name=='bootstrap5' or $skin-name=='FluentUI' or $skin-name=='Material3' {
    .e-de-ctn .e-de-bzr-button {
        @if $skin-name!='bootstrap5' and $skin-name!='FluentUI' and $skin-name != 'Material3' {
            font-size: 12px;
        }   
        @if $skin-name !='bootstrap4' {
            font-weight: 400;
        }
        @if  $skin-name=='FluentUI' or $skin-name == 'Material3' {
            font-size: 14px;
            border: none;
        }
        @if $skin-name !='material' {
            background: $e-de-bzr-button-bg-color;
            @if $skin-name !='Material3' {
                height: 100%;
                @if $skin-name !='fabric' {
                    color: $e-de-bzr-button-font-color;
                }
            }
        }
        @if $skin-name =='Material3' {
            height: calc(100% - 10px);
            line-height: 16px;
            font-size: $de-toolbar-font-size !important;
        }
        @if $skin-name=='bootstrap4' or $skin-name=='bootstrap' {
            border: 1px solid $e-de-bzr-button-border-color;
        }
        @if $skin-name=='bootstrap5' {
            border: none;
            line-height: 16px !important;
        }
        @if $skin-name=='highcontrast' or $skin-name=='bootstrap' or $skin-name=='bootstrap5' {
            box-shadow: none;
        }
        @if $skin-name=='highcontrast' {
            border: 2px solid $e-de-bzr-button-border-color;
        }
        @if $skin-name=='bootstrap' {
            padding-bottom: 6px;
        }
    }
    @if $skin-name=='fabric' {
        .e-de-ctn .e-de-bzr-button:focus {
            outline: none;
        }
    }
    @if $skin-name !='material' {
        .e-de-ctn .e-de-bzr-button:hover {
            background: $e-de-bzr-button-hover-bg-color;
            border: $e-de-bzr-button-hover-border;
            border-radius: $e-de-bzr-button-hover-border-radius;
            color: $e-de-bzr-button-hover-font-color;
        }
        @if $skin-name=='bootstrap4' {
            .e-de-ctn .e-de-bzr-button:hover .e-btn-icon {
                color: $e-de-bzr-button-hover-font-color;
            }
        }
    }
    @if $skin-name !='bootstrap' {
        .e-de-ctn .e-de-bzr-button:active {
            @if $skin-name !='bootstrap4' {
                box-shadow: none;
            }
            @if $skin-name=='fabric' or $skin-name=='highcontrast' {
                border-radius: 0;
            }
            @if $skin-name !='material' {
                background-color: $e-de-bzr-button-active-bg-color;
                border-color: $e-de-bzr-button-active-border-color;
                color: $e-de-bzr-button-active-font-color;
            }
        }
        @if $skin-name=='highcontrast' {
            .e-de-ctn .e-de-bzr-button:active .e-btn-icon {
                color: $e-de-bzr-button-active-font-color;
            }
        }
    }
}

.e-de-ctn {
    .e-de-ctnr-toolbar {
        .e-de-bzr-button {
            @if $skin-name == 'FluentUI' {
                font-size: 14px;
            }
            .e-btn-icon {
               font-size: $e-de-bzr-btn-font-size; 
               @if $skin-name=='tailwind' or  $skin-name=='tailwind-dark' {  
                   padding-bottom: 4px !important;
               }
               @if $skin-name == 'FluentUI' {
                    padding-bottom: 2px;
                }
            }
        }

        .e-de-bzr-break.e-de-bzr-button {
            @if $skin-name=='bootstrap4' {
               line-height: 23px !important;
            }
            @if $skin-name=='tailwind' or  $skin-name=='tailwind-dark' { 
                padding-top: 9px !important;
                padding-bottom: 23px !important;
            }
            @if  $skin-name=='material' or  $skin-name=='material-dark' {
                height: calc(100% - 10px);
                line-height: 23px;
            }
            @if $skin-name =='bootstrap5' {
                .e-btn-icon {
                    margin-top: -11px;
                }
            }
            @if $skin-name == 'FluentUI' {
                padding-top: 0 !important;
            }
        }
    }
}

.e-bigger {
    .e-de-ctn {
        .e-de-ctnr-toolbar {
            .e-de-bzr-button {
                @if $skin-name == 'FluentUI' and $skin-name == 'Material3' {
                    font-size: 16px;
                }
                .e-btn-icon {
                   font-size:  $e-de-bzr-btn-font-size-big;
                   @if $skin-name =='bootstrap5' or $skin-name == 'FluentUI'{
                     padding-bottom: 4px;
                   }
                }
            }

            .e-de-bzr-break.e-de-bzr-button {
                @if  $skin-name == 'material' or  $skin-name == 'material-dark' or  $skin-name == 'Material3'{
                    padding-top: 11px !important;
                }
                @if $skin-name == 'FluentUI' {
                    padding-top: 4px !important;
                }
            }
        }
    }
}
