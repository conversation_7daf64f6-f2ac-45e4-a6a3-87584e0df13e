import { isNullOrUndefined } from "@syncfusion/ej2-base";
import { FormFieldsBase } from './index';
import { PdfAnnotationFlag, PdfInkAnnotation, PdfPen, _PdfPath } from "@syncfusion/ej2-pdf";
import { Rect } from '@syncfusion/ej2-drawings';
/**
 * SignatureBase
 *
 * @hidden
 */
var SignatureBase = /** @class */ (function () {
    function SignatureBase(pdfViewer, pdfViewerBase) {
        this.pdfViewer = pdfViewer;
        this.pdfViewerBase = pdfViewerBase;
    }
    /**
     * @private
     * @param jsonObject
     * @param loadedDocument
     */
    SignatureBase.prototype.saveSignatureData = function (jsonObject, loadedDocument) {
        var formfields = new FormFieldsBase(this.pdfViewer, this.pdfViewerBase);
        var signatureDetails = JSON.parse(jsonObject.signatureData);
        if (!isNullOrUndefined(signatureDetails)) {
            for (var i = 0; i < signatureDetails.length; i++) {
                var pageData = signatureDetails[parseInt(i.toString(), 10)];
                // Save signature as data
                if (pageData.length > 0) {
                    for (var p = 0; p < pageData.length; p++) {
                        var data = pageData[parseInt(p.toString(), 10)];
                        var signatureType = data.hasOwnProperty("shapeAnnotationType") && data["shapeAnnotationType"] !== null
                            ? data["shapeAnnotationType"].toString()
                            : null;
                        if (signatureType !== null && signatureType === 'SignatureText') {
                            formfields.drawFreeTextAnnotations(data, loadedDocument, true);
                        }
                        else if (signatureType !== null && signatureType === 'SignatureImage') {
                            formfields.drawImage(data, loadedDocument, true);
                        }
                        else {
                            var pageNumber = data.pageIndex;
                            var page = loadedDocument.getPage(pageNumber);
                            var rotateAngle = this.getRotateAngle(page.rotation.toString());
                            var size = page.size;
                            var pageWidth = size[0];
                            var pageHeight = size[1];
                            if (rotateAngle == 1 || rotateAngle == 3) {
                                pageHeight = size[0];
                                pageWidth = size[1];
                            }
                            else {
                                pageHeight = size[0];
                                pageWidth = size[1];
                            }
                            var bounds = JSON.parse(data.bounds);
                            bounds = this.getSignatureBounds(bounds, this.convertPointToPixel(pageHeight), this.convertPointToPixel(pageWidth), rotateAngle);
                            var stampObjects = JSON.parse(data.data);
                            var left = this.convertPixelToPoint(bounds.left);
                            var top_1 = this.convertPixelToPoint(bounds.top);
                            var width = this.convertPixelToPoint(bounds.width);
                            var height = this.convertPixelToPoint(bounds.height);
                            var opacity = data.opacity;
                            var thickness = data.thickness;
                            var strokeColor = JSON.parse(data.strokeColor);
                            var color = [strokeColor.r, strokeColor.g, strokeColor.b];
                            var minimumX = -1;
                            var minimumY = -1;
                            var maximumX = -1;
                            var maximumY = -1;
                            for (var p_1 = 0; p_1 < stampObjects.length; p_1++) {
                                var value = stampObjects[parseInt(p_1.toString(), 10)];
                                if (minimumX == -1) {
                                    minimumX = value.x;
                                    minimumY = value.y;
                                    maximumX = value.x;
                                    maximumY = value.x;
                                }
                                else {
                                    var point1 = value.x;
                                    var point2 = value.y;
                                    if (minimumX >= point1) {
                                        minimumX = point1;
                                    }
                                    if (minimumY >= point2) {
                                        minimumY = point2;
                                    }
                                    if (maximumX <= point1) {
                                        maximumX = point1;
                                    }
                                    if (maximumY <= point2) {
                                        maximumY = point2;
                                    }
                                }
                            }
                            var newDifferenceX = maximumX - minimumX;
                            var newDifferenceY = maximumY - minimumY;
                            var newPoint1 = [0, 0];
                            var loadedPage = loadedDocument.getPage(pageNumber);
                            var graphics = null;
                            if (loadedPage != null) {
                                graphics = loadedPage.graphics;
                                graphics.save();
                                graphics.setTransparency(opacity);
                                graphics.translateTransform(left, top_1);
                            }
                            var colors = new PdfPen(color, width);
                            colors._width = thickness;
                            if (stampObjects.length > 0) {
                                var dataPath = new _PdfPath();
                                for (var j = 0; j < stampObjects.length; j++) {
                                    var value = stampObjects[parseInt(j.toString(), 10)];
                                    var path = value.command.toString();
                                    var differenceX = ((newDifferenceX) / width);
                                    var differenceY = ((newDifferenceY) / height);
                                    var newX = ((value.x - minimumX) / differenceX);
                                    var currentY = ((value.y - minimumY) / differenceY);
                                    if (path == "M") {
                                        if (j != 0) {
                                            page.graphics._drawPath(dataPath, colors, null);
                                            dataPath = new _PdfPath();
                                        }
                                        newPoint1 = [newX, currentY];
                                        if (!isNullOrUndefined(graphics)) {
                                            dataPath._addLine(newX, currentY, newX, currentY);
                                        }
                                    }
                                    else if (path = 'L') {
                                        var newPoint2 = [newX, currentY];
                                        if (graphics != null) {
                                            // Removed this line to fix the issue EJ2-60295
                                            // graphics.DrawLine(colors, newpoint1, newpoint2);
                                            dataPath._addLine(newPoint1[0], newPoint1[1], newPoint2[0], newPoint2[1]);
                                        }
                                        newPoint1 = newPoint2;
                                    }
                                    if (j == stampObjects.length - 1) {
                                        page.graphics._drawPath(dataPath, colors, null);
                                    }
                                }
                            }
                            if (graphics != null) {
                                graphics.restore();
                            }
                        }
                    }
                }
            }
        }
    };
    /**
     * getSignatureBounds
     */
    SignatureBase.prototype.getSignatureBounds = function (bounds, pageHeight, pageWidth, rotateAngle) {
        var bound;
        if (rotateAngle == 0) {
            bound = { "left": bounds.left, "top": bounds.top, "width": bounds.width, "height": bounds.height };
        }
        else if (rotateAngle == 1) {
            bound = { "left": (pageWidth - bounds.top - bounds.height), "top": bounds.left, "width": bounds.height, "height": bounds.width };
        }
        else if (rotateAngle == 2) {
            bound = { "left": (pageWidth - bounds.left - bounds.width), "top": (pageHeight - bounds.top - bounds.height), "width": bounds.width, "height": bounds.height };
        }
        else if (rotateAngle == 3) {
            bound = { "left": bounds.top, "top": (pageHeight - bounds.width), "width": bounds.height, "height": bounds.width };
        }
        return bound;
    };
    /**
     * @private
     * @param jsonObject
     * @param loadedDocument
     */
    SignatureBase.prototype.saveSignatureAsAnnotatation = function (jsonObject, loadedDocument) {
        var formfields = new FormFieldsBase(this.pdfViewer, this.pdfViewerBase);
        var signatureDetails = JSON.parse(jsonObject.signatureData);
        if (!isNullOrUndefined(signatureDetails)) {
            for (var i = 0; i < signatureDetails.length; i++) {
                var pageData = signatureDetails[parseInt(i.toString(), 10)];
                // Save signature as data
                if (pageData.length > 0) {
                    for (var p = 0; p < pageData.length; p++) {
                        var formfields_1 = new FormFieldsBase(this.pdfViewer, this.pdfViewerBase);
                        var signatureAnnotation = pageData[parseInt(p.toString(), 10)];
                        var signatureType = signatureAnnotation.hasOwnProperty("shapeAnnotationType") && signatureAnnotation["shapeAnnotationType"] !== null
                            ? signatureAnnotation["shapeAnnotationType"].toString()
                            : null;
                        if (signatureType !== null && signatureType === 'SignatureText') {
                            formfields_1.drawFreeTextAnnotations(signatureAnnotation, loadedDocument, false);
                        }
                        else if (signatureType !== null && signatureType === 'SignatureImage') {
                            formfields_1.drawImage(signatureAnnotation, loadedDocument, false);
                        }
                        else {
                            var bounds = JSON.parse(signatureAnnotation.bounds);
                            var stampObjects = JSON.parse(signatureAnnotation.data);
                            var left = this.convertPixelToPoint(bounds.left);
                            var top_2 = this.convertPixelToPoint(bounds.top);
                            var width = this.convertPixelToPoint(bounds.width);
                            var height = this.convertPixelToPoint(bounds.height);
                            var pageNumber = signatureAnnotation.pageIndex;
                            var page = loadedDocument.getPage(pageNumber);
                            // let cropX = 0;
                            // let cropY = 0;
                            // if(page.cropBox.x)
                            var opacity = signatureAnnotation.opacity;
                            var thickness = signatureAnnotation.thickness;
                            var strokeColor = JSON.parse(signatureAnnotation.strokeColor);
                            var color = [strokeColor.r, strokeColor.g, strokeColor.b];
                            var minimumX = -1;
                            var minimumY = -1;
                            var maximumX = -1;
                            var maximumY = -1;
                            for (var p_2 = 0; p_2 < stampObjects.length; p_2++) {
                                var value = stampObjects[parseInt(p_2.toString(), 10)];
                                if (minimumX == -1) {
                                    minimumX = value.x;
                                    minimumY = value.y;
                                    maximumX = value.x;
                                    maximumY = value.x;
                                }
                                else {
                                    var point1 = value.x;
                                    var point2 = value.y;
                                    if (minimumX >= point1) {
                                        minimumX = point1;
                                    }
                                    if (minimumY >= point2) {
                                        minimumY = point2;
                                    }
                                    if (maximumX <= point1) {
                                        maximumX = point1;
                                    }
                                    if (maximumY <= point2) {
                                        maximumY = point2;
                                    }
                                }
                            }
                            var newDifferenceX = maximumX - minimumX;
                            var newDifferenceY = maximumY - minimumY;
                            var linePoints = [];
                            var isNewValues = 0;
                            for (var j = 0; j < stampObjects.length; j++) {
                                var element = stampObjects[parseInt(j.toString(), 10)];
                                var value = stampObjects[parseInt(j.toString(), 10)];
                                var path = value.command.toString();
                                if (path == "M" && j !== 0) {
                                    isNewValues = j;
                                    break;
                                }
                                var differenceX = ((newDifferenceX) / width);
                                var differenceY = ((newDifferenceY) / height);
                                linePoints.push(((value.x - minimumX) / differenceX) + left);
                                var newX = ((value.y - minimumY) / differenceY);
                                linePoints.push(loadedDocument.getPage(pageNumber).size[1] - newX - top_2);
                            }
                            var highestY = 1;
                            for (var k = 0; k < linePoints.length - 1; k++) {
                                if (linePoints[parseInt(k.toString(), 10)] > highestY) {
                                    highestY = linePoints[parseInt(k.toString(), 10)];
                                }
                            }
                            var rectangle = new Rect(left, top_2, width, height);
                            var inkAnnotation = new PdfInkAnnotation([rectangle.x, rectangle.y, rectangle.width, rectangle.height], linePoints);
                            var bound = new Rect(inkAnnotation.bounds.x, inkAnnotation.bounds.y, inkAnnotation.bounds.width, inkAnnotation.bounds.height);
                            inkAnnotation.bounds = bound;
                            inkAnnotation.color = color;
                            linePoints = [];
                            for (var i_1 = isNewValues; i_1 < stampObjects.length; i_1++) {
                                var val = stampObjects[parseInt(i_1.toString(), 10)];
                                var path = val["command"].toString();
                                if (path === "M" && i_1 !== isNewValues) {
                                    inkAnnotation.inkPointsCollection.push(linePoints);
                                    linePoints = [];
                                }
                                var differenceX = newDifferenceX / width;
                                var differenceY = newDifferenceY / height;
                                linePoints.push((parseFloat(val["x"].toString()) - minimumX) / differenceX + left);
                                var newX = (parseFloat(val["y"].toString()) - minimumY) / differenceY;
                                linePoints.push(loadedDocument.getPage(pageNumber).size[1] - newX - top_2);
                            }
                            if (linePoints.length > 0) {
                                inkAnnotation.inkPointsCollection.push(linePoints);
                            }
                            inkAnnotation.border.width = thickness;
                            inkAnnotation.opacity = opacity;
                            inkAnnotation._dictionary.set('NM', signatureAnnotation.signatureName.toString());
                            inkAnnotation._annotFlags = PdfAnnotationFlag.print;
                            if (signatureAnnotation.hasOwnProperty('author') && signatureAnnotation['author'] !== null) {
                                var author = signatureAnnotation['author'].toString();
                                if (author !== 'Guest') {
                                    inkAnnotation.author = author;
                                }
                            }
                            page.annotations.add(inkAnnotation);
                        }
                    }
                }
            }
        }
    };
    SignatureBase.prototype.convertPointToPixel = function (number) {
        return number * 96 / 72;
    };
    SignatureBase.prototype.convertPixelToPoint = function (value) {
        return (value * (72 / 96));
    };
    SignatureBase.prototype.getRotateAngle = function (angleString) {
        var angle = 0;
        switch (angleString) {
            case 'RotateAngle0':
                angle = 0;
                break;
            case 'RotateAngle180':
                angle = 2;
                break;
            case 'RotateAngle270':
                angle = 3;
                break;
            case 'RotateAngle90':
                angle = 1;
                break;
        }
        return angle;
    };
    return SignatureBase;
}());
export { SignatureBase };
