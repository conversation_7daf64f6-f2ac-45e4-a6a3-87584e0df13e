@include export-module('document-editor-container-fabric-icons') {
  .e-de-ctnr-linespacing::before {
    content: '\eb8a';
  }

  .e-de-ctnr-lock::before {
    content: '\eb7d';
  }

  .e-de-ctnr-italic::before {
    content: '\eb99';
  }

  .e-de-ctnr-justify::before {
    content: '\eb67';
  }

  .e-de-ctnr-tableofcontent::before {
    content: '\eb98';
  }

  .e-de-cnt-track::before {
    content: '\e20a';
    font-family: 'e-icons';
  }

  .e-de-selected-spellcheck-item::before {
    content: '\e935';
    font-family: 'e-icons';
    font-size: 10px;
  }

  .e-de-selected-underline-item::before {
    content: '\e935';
    font-family: 'e-icons';
    font-size: 10px;
  }

  .e-de-ctnr-pagenumber::before {
    content: '\eb74';
  }

  .e-de-ctnr-highlight::before {
    content: '\eb6e';
  }

  .e-de-ctnr-bold::before {
    content: '\eb77';
  }

  .e-de-ctnr-subscript::before {
    content: '\eb96';
  }

  .e-de-ctnr-aligncenter::before {
    content: '\eb9e';
  }

  .e-de-ctnr-fontcolor::before {
    content: '\eb87';
  }

  .e-de-ctnr-change-case::before {
    content: '\e88c';
  }

  .e-de-ctnr-pagesetup::before {
    content: '\eb6d';
  }

  .e-de-ctnr-strokestyle::before {
    content: '\eb62';
  }

  .e-de-ctnr-strikethrough::before {
    content: '\eb7f';
  }

  .e-de-ctnr-image::before {
    content: '\eb64';
  }

  .e-de-ctnr-bookmark::before {
    content: '\eb63';
  }

  .e-de-ctnr-increaseindent::before {
    content: '\eb76';
  }

  .e-de-ctnr-header::before {
    content: '\eb7e';
  }

  .e-de-ctnr-superscript::before {
    content: '\eb6f';
  }

  .e-de-ctnr-numbering::before {
    content: '\eb9c';
  }

  .e-de-ctnr-bullets::before {
    content: '\eb92';
  }

  .e-de-ctnr-borders::before {
    content: '\e93d';
  }

  .e-de-ctnr-decreaseindent::before {
    content: '\eb69';
  }

  .e-de-ctnr-showhide::before {
    content: '\eb8b';
    font-size: 16px;
  }

  .e-de-ctnr-alignright::before {
    content: '\eb82';
  }

  .e-de-ctnr-footer::before {
    content: '\eb79';
  }

  .e-de-ctnr-clearall::before {
    content: '\eb80';
  }

  .e-de-ctnr-outsideborder::before {
    content: '\eb66';
  }

  .e-de-ctnr-allborders::before {
    content: '\eb95';
  }

  .e-de-ctnr-insideborders::before {
    content: '\eb88';
  }

  .e-de-ctnr-highlightcolor::before {
    content: '\eb6e';
  }

  .e-de-ctnr-mergecell::before {
    content: '\eb93';
  }

  .e-de-ctnr-bullet-none::before {
    content: '\e256';
  }

  .e-de-ctnr-bullet-dot::before {
    content: '\e270';
  }

  .e-de-ctnr-bullet-circle::before {
    content: '\e254';
  }

  .e-de-ctnr-bullet-square::before {
    content: '\e271';
  }

  .e-de-ctnr-bullet-flower::before {
    content: '\e267';
  }

  .e-de-ctnr-bullet-arrow::before {
    content: '\e253';
  }

  .e-de-ctnr-bullet-tick::before {
    content: '\e259';
  }

  .e-de-selected-item::before {
    content: '\e935';
  }

  .e-de-ctnr-break::before {
    content: '\e58d';
  }

  .e-de-ctnr-page-break::before {
    content: '\e590';
  }

  .e-de-ctnr-section-break::before {
    content: '\e58e';
  }

  .e-de-ctnr-upload::before {
    content: '\e60f';
  }

  .e-de-ctnr-leftborders::before {
    content: '\e291';
  }

  .e-de-ctnr-topborder::before {
    content: '\e281';
  }

  .e-de-ctnr-rightborder::before {
    content: '\e288';
  }

  .e-de-ctnr-insertleft::before {
    content: '\e285';
  }

  .e-de-ctnr-insertright::before {
    content: '\e284';
  }

  .e-de-ctnr-insertabove::before {
    content: '\e506';
  }

  .e-de-ctnr-insertbelow::before {
    content: '\e505';
  }

  .e-de-ctnr-deleterows::before {
    content: '\e283';
  }

  .e-de-ctnr-deletecolumns::before {
    content: '\e282';
  }

  .e-de-ctnr-undo::before {
    content: '\ebed';
  }

  .e-de-ctnr-bottomborder::before {
    content: '\e298';
  }

  .e-de-ctnr-strokesize::before {
    content: '\ebfe';
  }

  .e-de-ctnr-download::before {
    content: '\e603';
  }

  .e-de-ctnr-find::before {
    content: '\e275';
  }

  .e-de-ctnr-new::before {
    content: '\e7d5';
  }

  .e-de-ctnr-paste::before {
    content: '\e601';
  }

  .e-de-ctnr-redo::before {
    content: '\ebfa';
  }

  .e-de-ctnr-open::before {
    content: '\ebdd';
  }

  .e-de-ctnr-underline::before {
    content: '\ebf0';
  }

  .e-de-ctnr-insideverticalborder::before {
    content: '\e287';
  }

  .e-de-ctnr-insidehorizondalborder::before {
    content: '\e276';
  }

  .e-de-ctnr-aligncenter-table::before {
    content: '\ea94';
  }

  .e-de-ctnr-alignleft::before {
    content: '\ebeb';
  }

  .e-de-ctnr-close::before {
    content: '\ea7f';
  }

  .e-de-ctnr-link::before {
    content: '\e290';
  }

  .e-de-ctnr-table::before {
    content: '\e294';
  }

  .e-de-ctnr-backgroundcolor::before {
    content: '\ebf2';
  }

  .e-de-ctnr-print::before {
    content: '\ebf9';
  }

  .e-de-ctnr-aligntop::before {
    content: '\ea98';
  }

  .e-de-ctnr-alignbottom::before {
    content: '\ea91';
  }

  .e-de-ctnr-cellbg-clr-picker::before {
    content: '\ebf2';
  }

  .e-de-flip {
    transform: scaleX(-1);
  }

  .e-de-cnt-cmt-add::before {
    content: '\e814';
    font-family: 'e-icons';
  }

  .e-de-printlayout::before {
    content: '\e257';
    font-family: 'e-icons';
  }

  .e-de-weblayout::before {
    content: '\e529';
    font-family: 'e-icons';
  }

  .e-de-textform::before {
    content: '\e198';
    font-family: 'e-icons';
  }

  .e-de-formproperties::before {
    content: '\e199';
    font-family: 'e-icons';
  }

  .e-de-clearform::before {
    content: '\e19a';
    font-family: 'e-icons';
  }

  .e-de-dropdownform::before {
    content: '\e19b';
    font-family: 'e-icons';
  }

  .e-de-formfield::before {
    content: '\e19c';
    font-family: 'e-icons';
  }

  .e-de-checkbox-form::before {
    content: '\e19d';
    font-family: 'e-icons';
  }

  .e-de-arrow-up::before {
    content: '\e834';
    font-family: 'e-icons';
  }

  .e-de-arrow-down::before {
    content: '\e83d';
    font-family: 'e-icons';
  }

  .e-de-update-field::before {
    content: '\e19e';
    font-family: 'e-icons';
  }

  .e-de-footnote::before {
    content: '\e435';
    font-family: 'e-icons';
  }

  .e-de-endnote::before {
    content: '\e436';
    font-family: 'e-icons';
  }

  .e-de-e-paragraph-mark::before{
    content: '\e353';
    font-family: 'e-icons';
  }

  .e-de-e-paragraph-style-mark::before{
    content: '\e353';
    font-family: 'e-icons';
  }

  .e-de-e-character-style-mark::before{
    content: '\e982';
    font-family: 'e-icons';
  }

  .e-de-e-linked-style-mark::before{
    content: '\e983';
    font-family: 'e-icons';
  }
  
  .e-de-ctnr-columns::before {
    content: '\e972';
    font-family: 'e-icons';
  }

  .e-de-ctnr-page-size::before {
    content: '\e947';
    font-family: 'e-icons';
  }

  .e-de-ctnr-page-break-column::before {
    content: '\e973';
    font-family: 'e-icons';
  }

  .e-de-ctnr-page-break-text-wrapping::before {
    content: '\e94a';
    font-family: 'e-icons';
  }

  .e-de-ctnr-section-break-continuous::before {
    content: '\e9754';
    font-family: 'e-icons';
  }
  
  .e-de-ctnr-section-break-even-page::before {
    content: '\e970';
    font-family: 'e-icons';
  }

  .e-de-ctnr-section-break-odd-page::before {
    content: '\e971';
    font-family: 'e-icons';
  }

  .e-de-ctnr-columns-one::before {
    content: '\e979';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }

  .e-de-ctnr-columns-two::before {
    content: '\e97a';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }

  .e-de-ctnr-columns-three::before {
    content: '\e97b';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }

  .e-de-ctnr-columns-right::before {
    content: '\e977';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }

  .e-de-ctnr-columns-left::before {
    content: '\e978';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }
}
