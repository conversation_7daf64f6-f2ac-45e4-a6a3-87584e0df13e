import { Spreadsheet } from '../base/index';
/**
 * Represents Ribbon for Spreadsheet.
 */
export declare class Ribbon {
    private parent;
    private ribbon;
    private numFormatDDB;
    private fontSizeDdb;
    private fontNameDdb;
    private textAlignDdb;
    private verticalAlignDdb;
    private sortingDdb;
    private datavalidationDdb;
    private bordersDdb;
    private cfDdb;
    private clearDdb;
    private findDdb;
    private findDialog;
    private border;
    private fontNameIndex;
    private numPopupWidth;
    private pasteSplitBtn;
    private colorPicker;
    private mergeSplitBtn;
    private findValue;
    private preTabIdx;
    private addChartDdb;
    private cPickerEle;
    constructor(parent: Spreadsheet);
    getModuleName(): string;
    private ribbonOperation;
    private initialize;
    private getRibbonMenuItems;
    private getRibbonItems;
    private getPasteBtn;
    private getHyperlinkDlg;
    private passwordProtectDlg;
    private getLocaleText;
    private getLocaleProtectText;
    private getLocaleProtectWorkbook;
    private insertDesignChart;
    private removeDesignChart;
    private createRibbon;
    private tabSelecting;
    private beforeRenderHandler;
    private getChartThemeDDB;
    private getNumFormatDDB;
    private getFontSizeDDB;
    private getChartDDB;
    private closeDropdownPopup;
    private createChartDdb;
    private createChartMenu;
    private getAddChartEleDBB;
    private createAddChartMenu;
    private getCFDBB;
    private createCFMenu;
    private menuIconKeyDown;
    private createElement;
    private getBordersDBB;
    private createBorderMenu;
    private chartSelected;
    private addChartEleSelected;
    private cfSelected;
    private borderSelected;
    private getFontNameDDB;
    private getBtn;
    private datavalidationDDB;
    private getTextAlignDDB;
    private getVerticalAlignDDB;
    private getMergeSplitBtn;
    private mergeSelectHandler;
    private unMerge;
    private merge;
    private performMerge;
    private getSortFilterDDB;
    private getFindBtn;
    private findToolDlg;
    private updateCount;
    private closeDialog;
    private getClearDDB;
    private ribbonCreated;
    private alignItemRender;
    private getAlignText;
    private toggleBtnClicked;
    private getCellStyleValue;
    private refreshSelected;
    private expandCollapseHandler;
    private getChartThemeDdbItems;
    private getNumFormatDdbItems;
    private getFontFamilyItems;
    private applyNumFormat;
    private renderCustomFormatDialog;
    private tBarDdbBeforeOpen;
    private numDDBOpen;
    private previewNumFormat;
    private refreshRibbonContent;
    private refreshHomeTabContent;
    private refreshTextAlign;
    private toggleActiveState;
    private refreshToggleBtn;
    private refreshFontNameSelection;
    private refreshNumFormatSelection;
    private fileMenuItemSelect;
    private toolbarClicked;
    private toggleRibbonItems;
    private enableFileMenuItems;
    private hideRibbonTabs;
    private addRibbonTabs;
    private updateToggleText;
    private refreshViewTabContent;
    private updateViewTabContent;
    private updateRibbonItemText;
    private refreshDataTabContent;
    private updateDataTabContent;
    private updateProtectBtn;
    private updateProtectWorkbookBtn;
    private addToolbarItems;
    private enableToolbarItems;
    private createMobileView;
    private renderMobileToolbar;
    private fileMenuBeforeOpen;
    private enableRibbonTabs;
    private fileMenuBeforeClose;
    private hideFileMenuItems;
    private addFileMenuItems;
    private hideToolbarItems;
    private protectSheetHandler;
    private updateMergeItem;
    private onPropertyChanged;
    private addEventListener;
    destroy(): void;
    private destroyComponent;
    private detachPopupElement;
    private removeEventListener;
}
