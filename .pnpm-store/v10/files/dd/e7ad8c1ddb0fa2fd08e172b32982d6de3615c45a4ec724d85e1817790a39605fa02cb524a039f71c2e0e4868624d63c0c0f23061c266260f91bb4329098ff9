/** @hidden */
export declare const DISABLED: string;
/** @hidden */
export declare const WRAPTEXT: string;
/** @hidden */
export declare const locale: string;
/** @hidden */
export declare const dialog: string;
/** @hidden */
export declare const actionEvents: string;
/** @hidden */
export declare const overlay: string;
/** @hidden */
export declare const fontColor: {
    [key: string]: string[];
};
/** @hidden */
export declare const fillColor: {
    [key: string]: string[];
};
/** @hidden */
export declare const keyCodes: {
    [key: string]: number;
};
/**
 * Default locale text
 *
 * @hidden
 */
export declare const defaultLocale: object;
