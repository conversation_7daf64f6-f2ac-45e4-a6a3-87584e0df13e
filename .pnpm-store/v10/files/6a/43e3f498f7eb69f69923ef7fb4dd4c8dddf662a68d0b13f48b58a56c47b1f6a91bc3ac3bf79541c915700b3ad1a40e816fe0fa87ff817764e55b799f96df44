@include export-module('spreadsheet-tailwind-icons') {
  .e-spreadsheet {
    & .e-findtool-dlg {

      & .e-prev-icon {
        &::before {
          content: '\e776';
        }
      }

      & .e-next-icon {
        &::before {
          content: '\e729';
        }
      }

      & .e-option-icon {
        &::before {
          content: '\e770';
        }
      }

      & .e-close {
        &::before {
          content: '\e7e7';
        }
      }
    }

    & .e-ribbon {
      & .e-bold-icon {
        &::before {
          content: '\e737';
        }
      }

      & .e-italic-icon {
        &::before {
          content: '\e75a';
        }
      }

      & .e-underline-icon {
        &::before {
          content: '\e82f';
        }
      }

      & .e-line-through-icon {
        &::before {
          content: '\e758';
        }
      }

      & .e-font-color {
        &::before {
          content: '\e79f';
        }
      }

      & .e-fill-color {
        &::before {
          content: '\e783';
        }
      }

      .e-wrap-icon::before {
        content: '\e7ce';
      }

      & .e-hide-headers {
        &::before {
          content: '\e7ea';
        }
      }

      & .e-hide-gridlines {
        &::before {
          content: '\e816';
        }
      }

      & .e-freeze-pane::before {
        content: '\e7ec';
      }

      & .e-freeze-row::before {
        content: '\e77f';
      }

      & .e-freeze-column::before {
        content: '\e841';
      }

      & .e-insert-function {
        &::before {
          content: '\e7fa';
        }
      }

      & .e-hyperlink-icon {
        &::before {
          content: '\e757';
        }
      }

      & .e-image-icon {
        &::before {
          content: '\e786';
        }
      }

      & .e-merge-icon {
        &::before {
          content: '\e71e';
        }
      }
    }

    & .e-insert-function .e-btn-icon {
      &::before {
        content: '\e7fa';
      }
    }

    & .e-add-icon {
      &::before {
        content: '\e805';
      }
    }

    & .e-sheets-list .e-btn-icon {
      &::before {
        content: '\e799';
      }
    }

    & .e-formula-bar-panel .e-drop-icon {
      &::before {
        content: '\e729';
      }
    }

    & .e-file-menu-icon {
      &::before {
        content: '\e770';
      }
    }

    & .e-tick-icon {
      &::before {
        content: '\e774';
      }
    }
  }

  .e-spreadsheet-find-ddb {
    & .e-search-icon {
      &::before {
        content: '\e754';
      }
    }
  }

  .e-spreadsheet-contextmenu.e-contextmenu-wrapper {
    & .e-delete {
      &::before {
        content: '\e820';
      }
    }
  }

  .e-popup {
    &.e-dropdown-popup .e-selected-icon,
    &.e-numformat-ddb.e-dropdown-popup .e-active-item,
    &.e-menu-popup .e-selected-icon {
      &::before {
        content: '\e774';
      }
    }
  }

  .e-borders-ddb {
    & .e-top-borders {
      &::before {
        content: '\e7e0';
      }
    }

    & .e-left-borders {
      &::before {
        content: '\e806';
      }
    }

    & .e-right-borders {
      &::before {
        content: '\e7ab';
      }
    }

    & .e-bottom-borders {
      &::before {
        content: '\e766';
      }
    }

    & .e-all-borders {
      &::before {
        content: '\e7d1';
      }
    }

    & .e-horizontal-borders {
      &::before {
        content: '\e83b';
      }
    }

    & .e-vertical-borders {
      &::before {
        content: '\e792';
      }
    }

    & .e-outside-borders {
      &::before {
        content: '\e7ad';
      }
    }

    & .e-inside-borders {
      &::before {
        content: '\e78f';
      }
    }

    & .e-no-borders {
      &::before {
        content: '\e827';
      }
    }
  }

  .e-align-ddb {
    & .e-left-icon {
      &::before {
        content: '\e7b8';
      }
    }

    & .e-center-icon {
      &::before {
        content: '\e813';
      }
    }

    & .e-right-icon {
      &::before {
        content: '\e719';
      }
    }

    & .e-bottom-icon {
      &::before {
        content: '\e7a0';
      }
    }

    & .e-middle-icon {
      &::before {
        content: '\e74f';
      }
    }

    & .e-top-icon {
      &::before {
        content: '\e707';
      }
    }
  }

  .e-datavalidation-ddb {
    & .e-datavalidation-icon {
      &::before {
        content: '\e835';
      }
    }
  }

  .e-clear-ddb {
    & .e-clear-icon {
      &::before {
        content: '\e7cc';
      }
    }
  }

  .e-dragfill-ddb {
    & .e-dragfill-icon {
      &::before {
        content: '\e877';
      }
    }
  }

  .e-chart-ddb,
  .e-chart-type-ddb {
    & .e-chart-icon,
    & .e-chart-type-icon {
      &::before {
        content: '\e845';
      }
    }
  }

  .e-chart-menu .e-ul .e-menu-item,
  .e-chart-type-menu .e-ul .e-menu-item {
    padding: 0 16px 0 10px;

    .e-menu-icon {
      font-size: $spreadsheet-chart-icon-font-size;
      margin-right: 10px;
    }

    .e-bar {
      font-size: 42px;
      left: -6px;
      margin-right: -2px;
      position: relative;
    }
  }

  .e-addchart-menu .e-ul .e-menu-item .e-menu-icon {
    font-size: $spreadsheet-chart-icon-font-size;
  }

  .e-chart-menu,
  .e-chart-type-menu {

    & .e-column {
      &::before {
        content: '\e845';
      }
    }

    & .e-bar {
      &::before {
        content: '\e872';
      }
    }

    & .e-area {
      &::before {
        content: '\e84d';
      }
    }

    & .e-pie-doughnut {
      &::before {
        content: '\e850';
      }
    }

    & .e-line {
      &::before {
        content: '\e84a';
      }
    }

    & .e-scatter {
      &::before {
        content: '\e847';
      }
    }

    & .e-column-main {
      & .e-clusteredcolumn {
        &::before {
          content: '\e86a';
        }
      }

      & .e-stackedcolumn {
        &::before {
          content: '\e875';
        }
      }

      & .e-stackedcolumn100 {
        &::before {
          content: '\e86f';
        }
      }
    }

    & .e-bar-main {
      & .e-clusteredbar {
        &::before {
          content: '\e86c';
        }
      }

      & .e-stackedbar {
        &::before {
          content: '\e866';
        }
      }

      & .e-stackedbar100 {
        &::before {
          content: '\e86d';
        }
      }
    }

    & .e-pie-main {
      & .e-pie {
        &::before {
          content: '\e869';
        }
      }

      & .e-doughnut {
        &::before {
          content: '\e868';
        }
      }
    }

    & .e-area-main {
      & .e-area {
        &::before {
          content: '\e873';
        }
      }

      & .e-stackedarea {
        &::before {
          content: '\e871';
        }
      }

      & .e-stackedarea100 {
        &::before {
          content: '\e86b';
        }
      }
    }

    & .e-line-main {
      & .e-line {
        &::before {
          content: '\e86e';
        }
      }

      & .e-line-marker {
        &::before {
          content: '\e8c3';
        }
      }

      & .e-stackedline {
        &::before {
          content: '\e867';
        }
      }

      & .e-stackedline-marker {
        &::before {
          content: '\e8c1';
        }
      }

      & .e-stackedline100 {
        &::before {
          content: '\e870';
        }
      }

      & .e-stackedline100-marker {
        &::before {
          content: '\e8c2';
        }
      }
    }

    & .e-scatter-main {
      & .e-scatter {
        &::before {
          content: '\e874';
        }
      }
    }
  }

  .e-addchart-ddb {
    & .e-addchart-icon {
      &::before {
        content: '\e848';
      }
    }

    & .e-addchart-menu {
      & .e-axes {
        &::before {
          content: '\e84c';
        }
      }

      & .e-axis-title {
        &::before {
          content: '\e84e';
        }
      }

      & .e-chart-title {
        &::before {
          content: '\e84f';
        }
      }

      & .e-data-labels {
        &::before {
          content: '\e851';
        }
      }

      & .e-gridlines {
        &::before {
          content: '\e849';
        }
      }

      & .e-legends {
        &::before {
          content: '\e846';
        }
      }
    }
  }

  .e-addchart-menu {
    & .e-ph-axes {
      &::before {
        content: '\e85a';
      }
    }

    & .e-pv-axes {
      &::before {
        content: '\e856';
      }
    }

    & .e-ph-axistitle {
      &::before {
        content: '\e852';
      }
    }

    & .e-pv-axistitle {
      &::before {
        content: '\e860';
      }
    }

    & .e-ct-none {
      &::before {
        content: '\e864';
      }
    }

    & .e-ct-abovechart {
      &::before {
        content: '\e865';
      }
    }

    & .e-dl-none {
      &::before {
        content: '\e858';
      }
    }

    & .e-dl-center {
      &::before {
        content: '\e857';
      }
    }

    & .e-dl-insideend {
      &::before {
        content: '\e863';
      }
    }

    & .e-dl-insidebase {
      &::before {
        content: '\e862';
      }
    }

    & .e-dl-outsideend {
      &::before {
        content: '\e85f';
      }
    }

    & .e-gl-major-horizontal {
      &::before {
        content: '\e85d';
      }
    }

    & .e-gl-major-vertical {
      &::before {
        content: '\e859';
      }
    }

    & .e-gl-minor-horizontal {
      &::before {
        content: '\e85c';
      }
    }

    & .e-gl-minor-vertical {
      &::before {
        content: '\e855';
      }
    }

    & .e-legends-none {
      &::before {
        content: '\e853';
      }
    }

    & .e-legends-right {
      &::before {
        content: '\e854';
      }
    }

    & .e-legends-left {
      &::before {
        content: '\e861';
      }
    }

    & .e-legends-bottom {
      &::before {
        content: '\e85e';
      }
    }

    & .e-legends-top {
      &::before {
        content: '\e85b';
      }
    }
  }

  .e-switch-row-column-icon {
    &::before {
      content: '\e84b';
    }
  }

  .e-cf-ddb {
    & .e-conditionalformatting-icon {
      &::before {
        content: '\e812';
      }
    }
  }

  .e-cf-menu .e-ul .e-menu-item .e-menu-icon {
    font-size: $cf-menu-icon-font-size;
  }

  .e-cf-menu {
    & .e-hlcellrules {
      &::before {
        content: '\e83d';
      }
    }

    & .e-topbottomrules {
      &::before {
        content: '\e80d';
      }
    }

    & .e-databars {
      &::before {
        content: '\e791';
      }
    }

    & .e-colorscales {
      &::before {
        content: '\e7c6';
      }
    }

    & .e-iconsets {
      &::before {
        content: '\e764';
      }
    }

    & .e-clearrules {
      &::before {
        content: '\e7a2';
      }
    }

    & .e-greaterthan {
      &::before {
        content: '\e79a';
      }
    }

    & .e-lessthan {
      &::before {
        content: '\e7d7';
      }
    }

    & .e-between {
      &::before {
        content: '\e73b';
      }
    }

    & .e-equalto {
      &::before {
        content: '\e81a';
      }
    }

    & .e-textcontains {
      &::before {
        content: '\e75f';
      }
    }

    & .e-adateoccuring {
      &::before {
        content: '\e7bd';
      }
    }

    & .e-top10items {
      &::before {
        content: '\e80d';
      }
    }

    & .e-top10 {
      &::before {
        content: '\e76c';
      }
    }

    & .e-bottom10items {
      &::before {
        content: '\e79c';
      }
    }

    & .e-bottom10 {
      &::before {
        content: '\e777';
      }
    }

    & .e-aboveaverage {
      &::before {
        content: '\e808';
      }
    }

    & .e-belowaverage {
      &::before {
        content: '\e7d6';
      }
    }

    & .e-duplicate {
      &::before {
        content: '\e7ef';
      }
    }
  }

  .e-menu-wrapper.e-file-menu {
    & .e-new {
      &::before {
        content: '\e7dc';
      }
    }

    & .e-open {
      &::before {
        content: '\e760';
      }
    }

    & .e-save {
      &::before {
        content: '\e7ae';
      }
    }

    & .e-xls {
      &::before {
        content: '\e781';
      }
    }

    & .e-xlsx {
      &::before {
        content: '\e7c1';
      }
    }

    & .e-csv {
      &::before {
        content: '\e7ba';
      }
    }

    & .e-pdf {
      &::before {
        content: '\e700';
      }
    }
  }

  .e-spreadsheet,
  .e-spreadsheet-contextmenu {
    & .e-cut-icon {
      &::before {
        content: '\e7fb';
      }
    }

    & .e-copy-icon {
      &::before {
        content: '\e77c';
      }
    }

    & .e-paste-icon {
      &::before {
        content: '\e70b';
      }
    }
  }

  .e-undo-icon {
    &::before {
      content: '\e713';
    }
  }

  .e-redo-icon {
    &::before {
      content: '\e755';
    }
  }

  .e-sort-filter-ddb,
  .e-spreadsheet-contextmenu {
    & .e-sort-icon {
      &::before {
        content: '\e7a3';
      }
    }

    & .e-sort-asc {
      &::before {
        content: '\e7a3';
      }
    }

    & .e-sort-desc {
      &::before {
        content: '\e7b6';
      }
    }

    & .e-sort-custom {
      &::before {
        content: '\e824';
      }
    }
  }

  .e-spreadsheet-contextmenu {
    & .e-hyperlink-icon {
      &::before {
        content: '\e757';
      }
    }

    & .e-edithyperlink-icon {
      &::before {
        content: '\e722';
      }
    }

    & .e-openhyperlink-icon {
      &::before {
        content: '\e797';
      }
    }

    & .e-removehyperlink-icon {
      &::before {
        content: '\e80c';
      }
    }
  }

  .e-spreadsheet {

    & .e-sort-delete {
      &::before {
        content: '\e820';
      }
    }
  }

  .e-spreadsheet,
  .e-sort-filter-ddb,
  .e-spreadsheet-contextmenu {
    & .e-filter-icon {
      &::before {
        content: '\e843';
      }
    }

    & .e-filter-icon.e-filtered {
      &::before {
        content: '\e796';
      }
    }

    & .e-filter-icon.e-sortasc-filter {
      &::before {
        content: '\e7aa';
      }
    }

    & .e-filter-icon.e-sortdesc-filter {
      &::before {
        content: '\e71f';
      }
    }

    & .e-filter-icon.e-filtered.e-sortasc-filter {
      &::before {
        content: '\e81e';
      }
    }

    & .e-filter-icon.e-filtered.e-sortdesc-filter {
      &::before {
        content: '\e74b';
      }
    }

    & .e-filter-apply {
      &::before {
        content: '\e7f7';
      }
    }

    & .e-filter-clear {
      &::before {
        content: '\e72c';
      }
    }

    & .e-filter-reapply {
      &::before {
        content: '\e814';
      }
    }

    & .e-protect-icon {
      &::before {
        content: '\e70a';
      }
    }

    & .e-sort-filter-icon {
      &::before {
        content: '\e731';
      }
    }

    & .e-password-protect-icon {
      &::before {
        content: '\e788';
      }
    }
  }
}
