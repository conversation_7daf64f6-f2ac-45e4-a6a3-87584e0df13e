@include export-module('document-editor-container-bootstrap4-icons') {
  .e-de-ctnr-close::before {
    content: '\e745';
  }

  .e-de-ctnr-linespacing::before {
    content: '\e784';
  }

  .e-de-ctnr-undo::before {
    content: '\e752';
  }

  .e-de-ctnr-find::before {
    content: '\e724';
  }

  .e-de-ctnr-lock::before {
    content: '\e735';
  }

  .e-de-cnt-track::before {
    content: '\e20a';
    font-family: 'e-icons';
  }

  .e-de-selected-spellcheck-item::before {
    content: '\e718';
    font-family: 'e-icons';
    font-size: 10px;
  }

  .e-de-selected-underline-item::before {
    content: '\e718';
    font-family: 'e-icons';
    font-size: 10px;
  }

  .e-de-ctnr-italic::before {
    content: '\e78e';
  }

  .e-de-ctnr-link::before {
    content: '\e72e';
  }

  .e-de-ctnr-download::before {
    content: '\e75d';
  }

  .e-de-selected-item::before {
    content: '\e718';
  }

  .e-de-ctnr-break::before {
    content: '\e749';
  }

  .e-de-ctnr-page-break::before {
    content: '\e708';
  }

  .e-de-ctnr-section-break::before {
    content: '\e75f';
  }

  .e-de-ctnr-upload::before {
    content: '\e769';
  }

  .e-de-ctnr-tableofcontent::before {
    content: '\e753';
  }

  .e-de-ctnr-pagenumber::before {
    content: '\e733';
  }

  .e-de-ctnr-highlight::before {
    content: '\e770';
  }

  .e-de-ctnr-new::before {
    content: '\e759';
  }

  .e-de-ctnr-paste::before {
    content: '\e739';
  }

  .e-de-ctnr-bold::before {
    content: '\e78b';
  }

  .e-de-ctnr-subscript::before {
    content: '\e707';
  }

  .e-de-ctnr-pagesetup::before {
    content: '\e73a';
  }

  .e-de-ctnr-strikethrough::before {
    content: '\e786';
  }

  .e-de-ctnr-image::before {
    content: '\e776';
  }

  .e-de-ctnr-redo::before {
    content: '\e778';
  }

  .e-de-ctnr-bookmark::before {
    content: '\e731';
  }

  .e-de-ctnr-increaseindent::before {
    content: '\e702';
  }

  .e-de-ctnr-header::before {
    content: '\e772';
  }

  .e-de-ctnr-backgroundcolor::before {
    content: '\e754';
  }

  .e-de-ctnr-open::before {
    content: '\e70f';
  }

  .e-de-ctnr-underline::before {
    content: '\e792';
  }

  .e-de-ctnr-superscript::before {
    content: '\e779';
  }

  .e-de-ctnr-alignleft::before {
    content: '\e76f';
  }

  .e-de-ctnr-numbering::before {
    content: '\e72c';
  }

  .e-de-ctnr-aligncenter::before {
    content: '\e732';
  }

  .e-de-ctnr-bullets::before {
    content: '\e72a';
  }

  .e-de-ctnr-borders::before {
    content: '\e93b';
  }

  .e-de-ctnr-decreaseindent::before {
    content: '\e722';
  }

  .e-de-ctnr-showhide::before {
    content: '\e715';
    font-size: 16px;
  }

  .e-de-ctnr-print::before {
    content: '\e743';
  }

  .e-de-ctnr-alignright::before {
    content: '\e746';
  }

  .e-de-ctnr-footer::before {
    content: '\e75a';
  }

  .e-de-ctnr-clearall::before {
    content: '\e703';
  }

  .e-de-ctnr-highlightcolor::before {
    content: '\e770';
  }

  .e-de-ctnr-insertleft::before {
    content: '\e737';
  }

  .e-de-ctnr-insertright::before {
    content: '\e70e';
  }

  .e-de-ctnr-insertabove::before {
    content: '\e783';
  }

  .e-de-ctnr-insertbelow::before {
    content: '\e736';
  }

  .e-de-ctnr-deleterows::before {
    content: '\e738';
  }

  .e-de-ctnr-deletecolumns::before {
    content: '\e719';
  }

  .e-de-ctnr-aligntop::before {
    content: '\e709';
  }

  .e-de-ctnr-alignbottom::before {
    content: '\e726';
  }

  .e-de-ctnr-fontcolor::before {
    content: '\e74b';
  }

  .e-de-ctnr-change-case::before {
    content: '\e88c';
  }

  .e-de-ctnr-strokesize::before {
    content: '\e75b';
  }

  .e-de-ctnr-strokestyle::before {
    content: '\eb4d';
  }

  .e-de-ctnr-cellbg-clr-picker::before {
    content: '\e754';
  }

  .e-de-ctnr-mergecell::before {
    content: '\e7b1';
  }

  .e-de-ctnr-table::before {
    content: '\e7ac';
  }

  .e-de-ctnr-justify::before {
    content: '\e79b';
  }

  .e-de-ctnr-outsideborder::before {
    content: '\e7a1';
  }

  .e-de-ctnr-allborders::before {
    content: '\e79e';
  }

  .e-de-ctnr-insideborders::before {
    content: '\e79d';
  }

  .e-de-ctnr-leftborders::before {
    content: '\e7a7';
  }

  .e-de-ctnr-insideverticalborder::before {
    content: '\e79c';
  }

  .e-de-ctnr-rightborder::before {
    content: '\e79f';
  }

  .e-de-ctnr-topborder::before {
    content: '\e7a3';
  }

  .e-de-ctnr-insidehorizondalborder::before {
    content: '\e7a9';
  }

  .e-de-ctnr-bottomborder::before {
    content: '\e7aa';
  }

  .e-de-ctnr-aligncenter-table::before {
    content: '\e706';
  }

  .e-de-ctnr-bullet-none::before {
    content: '\e7b3';
  }

  .e-de-ctnr-bullet-dot::before {
    content: '\e7a0';
  }

  .e-de-ctnr-bullet-circle::before {
    content: '\e7b0';
  }

  .e-de-ctnr-bullet-square::before {
    content: '\e7a5';
  }

  .e-de-ctnr-bullet-flower::before {
    content: '\e7a4';
  }

  .e-de-ctnr-bullet-arrow::before {
    content: '\e7b6';
  }

  .e-de-ctnr-bullet-tick::before {
    content: '\e7ab';
  }

  .e-de-flip {
    transform: scaleX(-1);
  }

  .e-de-cnt-cmt-add::before {
    content: '\e814';
    font-family: 'e-icons';
  }

  .e-de-printlayout::before {
    content: '\e494';
    font-family: 'e-icons';
  }

  .e-de-weblayout::before {
    content: '\e898';
    font-family: 'e-icons';
  }

  .e-de-textform::before {
    content: '\e198';
    font-family: 'e-icons';
  }

  .e-de-formproperties::before {
    content: '\e199';
    font-family: 'e-icons';
  }

  .e-de-clearform::before {
    content: '\e19a';
    font-family: 'e-icons';
  }

  .e-de-dropdownform::before {
    content: '\e19b';
    font-family: 'e-icons';
  }

  .e-de-formfield::before {
    content: '\e19c';
    font-family: 'e-icons';
  }

  .e-de-checkbox-form::before {
    content: '\e19d';
    font-family: 'e-icons';
  }

  .e-de-arrow-up::before {
    content: '\e834';
    font-family: 'e-icons';
  }

  .e-de-arrow-down::before {
    content: '\e83d';
    font-family: 'e-icons';
  }

  .e-de-update-field::before {
    content: '\e19e';
    font-family: 'e-icons';
  }

  .e-de-footnote::before {
    content: '\e435';
    font-family: 'e-icons';
  }

  .e-de-endnote::before {
    content: '\e436';
    font-family: 'e-icons';
  }

  .e-de-e-paragraph-mark::before{
    content: '\e720';
    font-family: 'e-icons';
  }

  .e-de-e-paragraph-style-mark::before{
    content: '\e720';
    font-family: 'e-icons';
  }

  .e-de-e-character-style-mark::before{
    content: '\e97b';
    font-family: 'e-icons';
  }

  .e-de-e-linked-style-mark::before{
    content: '\e97c';
    font-family: 'e-icons';
  }
  
  .e-de-ctnr-columns::before {
    content: '\e955';
    font-family: 'e-icons';
  }

  .e-de-ctnr-page-size::before {
    content: '\e952';
    font-family: 'e-icons';
  }

  .e-de-ctnr-page-break-column::before {
    content: '\e970';
    font-family: 'e-icons';
  }

  .e-de-ctnr-page-break-text-wrapping::before {
    content: '\e972';
    font-family: 'e-icons';
  }

  .e-de-ctnr-section-break-continuous::before {
    content: '\e971';
    font-family: 'e-icons';
  }
  
  .e-de-ctnr-section-break-even-page::before {
    content: '\e96e';
    font-family: 'e-icons';
  }

  .e-de-ctnr-section-break-odd-page::before {
    content: '\e96f';
    font-family: 'e-icons';
  }

  .e-de-ctnr-columns-one::before {
    content: '\e975';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }

  .e-de-ctnr-columns-two::before {
    content: '\e976';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }

  .e-de-ctnr-columns-three::before {
    content: '\e977';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }

  .e-de-ctnr-columns-right::before {
    content: '\e973';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }

  .e-de-ctnr-columns-left::before {
    content: '\e974';
    font-family: 'e-icons';
    font-size: $de-column-presets-font-size;
  }
}
