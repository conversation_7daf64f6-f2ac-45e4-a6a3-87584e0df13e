{"_from": "@syncfusion/ej2-documenteditor@*", "_id": "@syncfusion/ej2-documenteditor@23.1.43", "_inBundle": false, "_integrity": "sha512-ByMSMc6AscDf72HFxYPPvAIWN7dLTuT6Nok2Ynle1tIQRa194BiaSYWaLfmH/MsWBAXhZvbNM9oAbbIZBy7m+A==", "_location": "/@syncfusion/ej2-documenteditor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-documenteditor@*", "name": "@syncfusion/ej2-documenteditor", "escapedName": "@syncfusion%2fej2-documenteditor", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-documenteditor", "/@syncfusion/ej2-react-documenteditor", "/@syncfusion/ej2-vue-documenteditor"], "_resolved": "https://nexus.syncfusion.com/repository/ej2-hotfix-new/@syncfusion/ej2-documenteditor/-/ej2-documenteditor-23.1.43.tgz", "_shasum": "801812ef3ad386257c2ebe2b0fd03c0cd90c706e", "_spec": "@syncfusion/ej2-documenteditor@*", "_where": "/jenkins/workspace/elease-automation_release_23.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-javascript-ui-controls/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~23.1.41", "@syncfusion/ej2-buttons": "~23.1.43", "@syncfusion/ej2-calendars": "~23.1.41", "@syncfusion/ej2-compression": "~23.1.36", "@syncfusion/ej2-dropdowns": "~23.1.44", "@syncfusion/ej2-file-utils": "~23.1.36", "@syncfusion/ej2-inputs": "~23.1.43", "@syncfusion/ej2-navigations": "~23.1.44", "@syncfusion/ej2-office-chart": "~23.1.36", "@syncfusion/ej2-popups": "~23.1.44", "@syncfusion/ej2-splitbuttons": "~23.1.43"}, "deprecated": false, "description": "Feature-rich document editor control with built-in support for context menu, options pane and dialogs.", "devDependencies": {}, "es2015": "./dist/es6/ej2-documenteditor.es5.js", "homepage": "https://www.syncfusion.com/javascript-ui-controls", "keywords": ["ej2", "web-components", "syncfusion", "documenteditor", "document-editor", "word-editor"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-documenteditor.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-documenteditor", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-javascript-ui-controls.git"}, "typings": "index.d.ts", "version": "23.1.44", "sideEffects": false}