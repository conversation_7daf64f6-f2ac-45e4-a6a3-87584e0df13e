import { WCharacterFormat } from '..';
import { DocumentHelper, TextSizeInfo } from '../viewer';
/**
 *  Class which performs optimized text measuring logic to find font height.
 */
export declare class Optimized {
    private documentHelper;
    /**
     * Font height collection cache object
     */
    private optimizedHeightCollection;
    private getModuleName;
    /**
     * Constructor to initialize Optimized module.
     *
     * @param {DocumentHelper} documentHelper - the document helper object.
     */
    constructor(documentHelper: DocumentHelper);
    /**
     * Construct key based on the character format.
     *
     * @param {WCharacterFormat} characterFormat - the character format to construct key.
     * @returns {string} - the constructed key.
     */
    private getkeyFromCharFormat;
    /**
     * Method to retrieve font information with optimized text measuring logic.
     *
     * @param {WCharacterFormat} characterFormat -character format to apply.
     * @returns {string} - returns font size information.
     */
    private getFontInfo;
    /**
     * @private
     * @param {WCharacterFormat} characterFormat - character format to apply.
     * @returns {TextSizeInfo} returns text size information.
     */
    getHeightInternal(characterFormat: WCharacterFormat): TextSizeInfo;
    /**
     * @private
     * @returns {void}
     */
    destroy(): void;
}
