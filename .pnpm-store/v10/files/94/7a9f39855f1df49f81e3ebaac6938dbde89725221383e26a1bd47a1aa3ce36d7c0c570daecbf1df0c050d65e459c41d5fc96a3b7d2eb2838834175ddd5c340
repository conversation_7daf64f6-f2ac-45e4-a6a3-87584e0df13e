import { FormFieldsBase } from './index';
import { PdfViewer, PdfViewerBase } from "../index";
import { Rect } from '@syncfusion/ej2-drawings';
/**
 * SignatureBase
 *
 * @hidden
 */
export declare class SignatureBase {
    m_formFields: FormFieldsBase;
    private pdfViewer;
    private pdfViewerBase;
    constructor(pdfViewer: PdfViewer, pdfViewerBase: PdfViewerBase);
    /**
     * @private
     * @param jsonObject
     * @param loadedDocument
     */
    saveSignatureData(jsonObject: {
        [key: string]: string;
    }, loadedDocument: any): void;
    /**
     * getSignatureBounds
     */
    getSignatureBounds(bounds: Rect, pageHeight: number, pageWidth: number, rotateAngle: number): any;
    /**
     * @private
     * @param jsonObject
     * @param loadedDocument
     */
    saveSignatureAsAnnotatation(jsonObject: {
        [key: string]: string;
    }, loadedDocument: any): void;
    private convertPointToPixel;
    private convertPixelToPoint;
    private getRotateAngle;
}
