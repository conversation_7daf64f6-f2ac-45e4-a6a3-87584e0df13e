@import 'ej2-base/styles/definition/fluent-dark.scss';
@import 'ej2-buttons/styles/button/fluent-dark-definition.scss';
@import 'ej2-buttons/styles/check-box/fluent-dark-definition.scss';
@import 'ej2-buttons/styles/radio-button/fluent-dark-definition.scss';
@import 'ej2-buttons/styles/switch/fluent-dark-definition.scss';
@import 'ej2-navigations/styles/toolbar/fluent-dark-definition.scss';
@import 'ej2-navigations/styles/tab/fluent-dark-definition.scss';
@import 'ej2-navigations/styles/context-menu/fluent-dark-definition.scss';
@import 'ej2-navigations/styles/menu/fluent-dark-definition.scss';
@import 'ej2-navigations/styles/treeview/fluent-dark-definition.scss';
@import 'ej2-grids/styles/excel-filter/fluent-dark-definition.scss';
@import 'ej2-calendars/styles/datepicker/fluent-dark-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/fluent-dark-definition.scss';
@import 'ej2-inputs/styles/color-picker/fluent-dark-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/fluent-dark-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/fluent-dark-definition.scss';
@import 'ej2-dropdowns/styles/list-box/fluent-dark-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/fluent-dark-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/fluent-dark-definition.scss';
@import 'fluent-dark-definition.scss';
@import 'icons/fluent-dark.scss';
@import 'all.scss';
