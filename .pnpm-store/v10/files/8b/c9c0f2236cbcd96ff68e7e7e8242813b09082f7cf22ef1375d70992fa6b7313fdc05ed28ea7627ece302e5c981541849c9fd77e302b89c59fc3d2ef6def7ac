$de-ctnr-height: calc(100%) !default;
$de-ctnr-tlbr-height: 85px !default;
$de-ctnr-tlbt-height-big: 90px !default;
$de-tlbr-margin-first: 8px !default;
$de-tlbr-margin-first-big: 8px !default;
$de-tlbr-margin-right: 4px !default;
$de-tlbr-margin-right-big: 8px !default;
$de-ctrnr-tblr-separator-margin: 0 8px !default;
$de-ctrnr-tblr-separator-margin-big: 0 8px !default;
$de-ctnr-margin-bottom: 8px !default;
$de-ctnr-margin-bottom-big: 8px !default;
$de-group-btn-hgt: 32px !default;
$de-group-btn-hgt-big: 40px !default;
$de-status-btn-hgt-big: 38px !default;
$de-ctnr-tlbr-width: calc(100% - 78px) !default;
$de-spellcheck-tlbr-width: 106px !default;
$de-spellcheck-tlbr-width-big: 145px !default;
$de-spellcheck-tlbr-height: 34px !default;
$de-spellcheck-tlbr-margin-left: 7.5px !default;
$de-zoom-tlbr-margin-left: calc(100% - 395px) !default;
$de-zoom-tlbr-margin-left-big: calc(100% - 430px) !default;
$e-de-ctnr-break-listview-margin-right: 12px !default;
$e-de-ctnr-break-listview-margin-right-big: 16px !default;
$e-de-ctnr-break-listview-margin-left: 12px !default;
$e-de-ctnr-break-listview-margin-left-big: 16px !default;
$de-tlbr-btn-height: calc(100% - 10px) !default;

// Subtract toolbar & statusbar height
$de-tool-ctnr-editor-height: calc(100% - 117px) !default;
$de-tool-ctnr-editor-height-big: calc(100% - 128px) !default;

// Subtract statusbar height
$de-ctnr-editor-height: calc(100% - 42px) !default;
$de-ctnr-editor-height-big: calc(100% - 46px) !default;
$de-ctrnr-tblr-item-padding: 0 !default;
$de-ctrnr-tblr-item-margin: 0 6.5px !default;
$de-ctrnr-tblr-item-margin-big: 0 6px !default;
$de-prop-pane-margin: 16px;
$de-panel-header-size: $text-xs !default;
$de-table-prop-border-margin: 16px;
$de-toc-template-content1: 80px !default;
$de-toc-template-content2: 66px !default;
$de-toc-template-content3: 59px !default;
$de-img-span-top: 8px !default;
$de-img-span-left: 12px !default;
$de-toggle-btn-outline: none !default;
$de-toggle-btn-border: none !default;
$de-page-no-width-spellout: calc(100% - 169px) !default;
$de-page-no-width-spellout-big: calc(100% - 180px) !default;
$de-ctnr-status-bar-hgt: 36px !default;
$de-border-size-button-height-bg: 39px !default;
$de-border-size-button-width-bg: 100px !default;
$de-border-size-button-height: 32px !default;
$de-border-size-button-width: 104px !default;
$de-list-hover-bg: rgba($primary) !default;
$de-pane-margin-right: 12px;
$de-pane-margin-right-big: 16px;
$de-left-panel-width: 151px;
$de-left-panel-width-big: 169px;
$de-right-panel-width: 73px;
$de-text-pane-width: 256px;
$de-text-pane-width-big: 306px;
$de-btn-icon-margin: -2px !default;
$de-left-btn-width: 36px;
$de-left-btn-width-big: 44px;
$de-right-btn-width: 36px;
$de-right-btn-width-big: 43px;
$de-border-btn-width-big: 44px;
$de-right-font-size-width-big: $text-8xl;
$de-img-span-top-big: 12px;
$de-bullet-icon-font-size: $text-xs;
$de-bullet-icon-line-height: 0;
$de-list-button-margin-right: 12px;
$de-list-button-margin-right-bigger: 16px;
$de-text-pane-style-width: auto;
$de-btn-font-size: $text-base;
$de-btn-font-size-bigger: $text-lg;
$de-ctnr-padding: 16px;
$de-prop-style-padding: 12px;
$de-prop-div-padding-top: 12px;
$de-prop-div-padding-bottom: 12px;
$de-layout-btn-font-size: $text-sm;
$de-layout-btn-bgr-font-size: $text-base;
$de-pagenumber-text-border-radius: 2px;
$de-pagenumber-text-margin-top: -3px;
$e-de-pagenumber-input-margin-left: -2px;
$e-de-ctnr-pg-no-spellout-padd-top: 7px;
$e-de-ctnr-pg-no-spellout-padd-top-big: 10px;
$e-de-status-bar-padding-top: 1px;
$e-de-status-bar-big-padding-top: 1px;
$e-de-review-pane-width: 380px;
$e-de-ctnr-linespacing-tick-icon: 8px;
$de-toolbar-font-size: $text-xs;
$de-toolbar-padding-bottom: 10px;
$de-toolbar-padding-bottom-bg: 10px;
$de-header-font-weight: $font-weight-medium;
$de-prop-sub-label-mrgn-btm: 4px;
$de-hdr-ftr-frst-div-mrgn-btm: 12px;
$de-hdr-ftr-top-div-mrgn-btm: 12px;
$de-ctnr-pg-no-spellout-fnt-size: $text-sm;
$de-ctnr-pg-no-spellout-fnt-size-bg: $text-base;
$de-status-br-lbl-fnt-weight: $font-weight-medium;
$de-status-br-left: 0 !default;
$de-status-br-top: 6px !default;
$de-status-br-top-big: 8px !default;
$e-de-btn-font-size: $text-sm;
$e-de-btn-font-icon-size: 16px;
$e-de-btn-font-size-big: $text-base;
$e-de-btn-font-icon-size-big: 18px;
$e-de-bzr-btn-font-size: $text-base;
$e-de-bzr-btn-font-size-big: $text-lg;
$e-de-bzr-button-hover-border-radius: 4px;
$e-de-bzr-button-hover-border: none;
$de-clr-pckr-width: 40px;
$de-page-number-margin-top: 3px;
$de-page-number-margin-top-big: 4px;
$de-hlt-clr-height: 20px;
$de-hlt-clr-height-bigger: 24px;

// Color variable
$de-pageweb-toggle-outline: rgba($white) 0 solid !default;
$de-title-bar-bg-clr: rgba($primary) !default;
$de-title-bar-font-clr: rgba($primary-text-color) !default;
$de-ctnr-bg-clr: $content-bg-color-alt2 !default;
$de-ctnr-prop-bg-clr: $content-bg-color-alt1 !default;
$de-prpty-btn-bg-clr: $content-bg-color-alt2 !default;
$de-prop-btn-icon-clr: rgba($icon-color);
$de-toolbar-icon-clr: rgba($icon-color);
$de-clr-picker-border-clr: rgba($border);
$de-panel-border: rgba($border-light) !default;
$de-pane-separator: rgba($border-light) !default;
$de-list-thmbnail-border-clr: rgba($border-light);
$e-de-statusbar-separator-color: rgba($border-light);
$de-panel-header-color: rgba($on-secondary-container) !default;
$de-panel-sub-label-color: rgba($content-text-color) !default;
$de-header-line-color: 1px solid rgba($border-light) !default;
$de-toggle-bg-color: rgba($content-bg-color-selected) !default;
$de-font-color: rgba($on-secondary-container) !default;
$de-prop-btn-border-color: rgba($border-light) !default;
$de-prop-btn-bg-color: $content-bg-color-alt3 !default;
$de-toggle-btn-color: rgba($icon-color) !default;
$de-pageweb-toggle-color: $content-bg-color-alt3 !default;
$e-de-pagenumber-bg: $content-bg-color-alt4;
$de-toggle-border-color: $secondary-bg-color !default;
$de-toggle-hover-color: $secondary-bg-color !default;
$de-toggle-border-hover-color: $secondary-bg-color !default;
$de-toggle-disabled-color: $secondary-bg-color !default;
$de-background-color: $content-bg-color-alt3 !default;
$de-toolbar-background-color: $content-bg-color-alt2 !default;
$de-pane-background-color: $content-bg-color-alt1 !default;

//Blazor
$e-de-bzr-button-border-color: rgba($white) !default;
$e-de-bzr-button-active-font-color: rgba($content-text-color) !default;
$de-white-color: rgba($white) !default;
$de-black-color: rgba($black) !default;
$e-de-bzr-button-bg-color: $content-bg-color-alt2 !default;
$e-de-bzr-button-hover-bg-color: $secondary-bg-color-hover !default;
$e-de-bzr-button-font-color: rgba($content-text-color) !default;
$e-de-bzr-button-hover-font-color: rgba($content-text-color-selected) !default;
$e-de-bzr-button-active-bg-color:  rgba($primary-light) !default;
$e-de-bzr-button-active-border-color: rgba($primary-light) !default;
$de-hover-bg: $content-bg-color-alt2 !default;
