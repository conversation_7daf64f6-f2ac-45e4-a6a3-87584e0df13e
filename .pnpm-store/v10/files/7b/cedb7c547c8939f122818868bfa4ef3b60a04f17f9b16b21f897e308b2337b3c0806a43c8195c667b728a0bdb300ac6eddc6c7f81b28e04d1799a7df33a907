import { DocumentHelper } from '../viewer';
/**
 * Exports the document to Word format.
 */
export declare class WordExport {
    private getModuleName;
    private customXMLItemsPath;
    private customXMLItemsPropspath;
    private itemPropsPath;
    private documentPath;
    private stylePath;
    private chartPath;
    private numberingPath;
    private settingsPath;
    private headerPath;
    private footerPath;
    private imagePath;
    private footnotesPath;
    private endnotesPath;
    private appPath;
    private corePath;
    private contentTypesPath;
    private defaultEmbeddingPath;
    private commentsPath;
    private commentsExtendedPath;
    private themePath;
    private generalRelationPath;
    private wordRelationPath;
    private customXMLRelPath;
    private excelRelationPath;
    private footnotesRelationPath;
    private endnotesRelationPath;
    private headerRelationPath;
    private footerRelationPath;
    private xmlContentType;
    private fontContentType;
    private documentContentType;
    private settingsContentType;
    private commentsContentType;
    private commentsExContentType;
    private endnoteContentType;
    private footerContentType;
    private footnoteContentType;
    private headerContentType;
    private numberingContentType;
    private stylesContentType;
    private webSettingsContentType;
    private appContentType;
    private coreContentType;
    private customContentType;
    private customXmlContentType;
    private relationContentType;
    private chartsContentType;
    private themeContentType;
    private tableStyleContentType;
    private chartColorStyleContentType;
    private commentsRelType;
    private commentsExRelType;
    private settingsRelType;
    private endnoteRelType;
    private footerRelType;
    private footnoteRelType;
    private headerRelType;
    private documentRelType;
    private numberingRelType;
    private stylesRelType;
    private chartRelType;
    private ThemeRelType;
    private fontRelType;
    private tableStyleRelType;
    private coreRelType;
    private appRelType;
    private customRelType;
    private imageRelType;
    private hyperlinkRelType;
    private controlRelType;
    private packageRelType;
    private customXmlRelType;
    private customUIRelType;
    private attachedTemplateRelType;
    private chartColorStyleRelType;
    private wNamespace;
    private wpNamespace;
    private pictureNamespace;
    private aNamespace;
    private a14Namespace;
    private svgNamespace;
    private rNamespace;
    private rpNamespace;
    private vNamespace;
    private oNamespace;
    private xmlNamespace;
    private w10Namespace;
    private cpNamespace;
    private dcNamespace;
    private docPropsNamespace;
    private veNamespace;
    private mNamespace;
    private wneNamespace;
    private customPropsNamespace;
    private vtNamespace;
    private chartNamespace;
    private slNamespace;
    private dtNamespace;
    private wmlNamespace;
    private w14Namespace;
    private wpCanvasNamespace;
    private wpDrawingNamespace;
    private wpGroupNamespace;
    private wpInkNamespace;
    private wpShapeNamespace;
    private w15Namespace;
    private diagramNamespace;
    private eNamespace;
    private pNamespace;
    private certNamespace;
    private cxNamespace;
    private c15Namespace;
    private c7Namespace;
    private csNamespace;
    private spreadSheetNamespace;
    private spreadSheet9;
    private cRelationshipsTag;
    private cRelationshipTag;
    private cIdTag;
    private cTypeTag;
    private cTargetTag;
    private cUserShapesTag;
    private cExternalData;
    private twipsInOnePoint;
    private twentiethOfPoint;
    private borderMultiplier;
    private percentageFactor;
    private emusPerPoint;
    private cConditionalTableStyleTag;
    private cTableFormatTag;
    private cTowFormatTag;
    private cCellFormatTag;
    private cParagraphFormatTag;
    private cCharacterFormatTag;
    private packageType;
    private relsPartPath;
    private documentRelsPartPath;
    private webSettingsPath;
    private wordMLDocumentPath;
    private wordMLStylePath;
    private wordMLNumberingPath;
    private wordMLSettingsPath;
    private wordMLHeaderPath;
    private wordMLFooterPath;
    private wordMLCommentsPath;
    private wordMLImagePath;
    private wordMLFootnotesPath;
    private wordMLEndnotesPath;
    private wordMLAppPath;
    private wordMLCorePath;
    private wordMLCustomPath;
    private wordMLFontTablePath;
    private wordMLChartsPath;
    private wordMLDefaultEmbeddingPath;
    private wordMLEmbeddingPath;
    private wordMLDrawingPath;
    private wordMLThemePath;
    private wordMLFontsPath;
    private wordMLDiagramPath;
    private wordMLControlPath;
    private wordMLVbaProject;
    private wordMLVbaData;
    private wordMLVbaProjectPath;
    private wordMLVbaDataPath;
    private wordMLWebSettingsPath;
    private wordMLCustomItemProp1Path;
    private wordMLFootnoteRelPath;
    private wordMLEndnoteRelPath;
    private wordMLSettingsRelPath;
    private wordMLNumberingRelPath;
    private wordMLFontTableRelPath;
    private wordMLCustomXmlPropsRelType;
    private wordMLControlRelType;
    private wordMLDiagramContentType;
    private dsNamespace;
    private excelFiles;
    private section;
    private lastSection;
    private blockOwner;
    private paragraph;
    private table;
    private row;
    private headerFooter;
    private endNoteFootnote;
    private document;
    private mSections;
    private mLists;
    private mAbstractLists;
    private mStyles;
    private mThemes;
    private defCharacterFormat;
    private themeFontLang;
    private defParagraphFormat;
    private defaultTabWidthValue;
    private dontUseHtmlParagraphAutoSpacing;
    private allowSpaceOfSameStyleInTable;
    private mRelationShipID;
    private cRelationShipId;
    private eRelationShipId;
    private efRelationShipId;
    private mDocPrID;
    private chartCount;
    private seriesCount;
    private chartStringCount;
    private chart;
    private mDifferentFirstPage;
    private mHeaderFooterColl;
    private mFootEndnotesColl;
    private mVerticalMerge;
    private mGridSpans;
    private mDocumentImages;
    private mSvgImages;
    private mCustomXML;
    private mImages;
    private mDocumentCharts;
    private mExternalLinkImages;
    private mHeaderFooterImages;
    private mHeaderFooterSvgImages;
    private mArchive;
    private mArchiveExcel;
    private mBookmarks;
    private formatting;
    private enforcement;
    private hashValue;
    private saltValue;
    private protectionType;
    private fileName;
    private spanCellFormat;
    private mComments;
    private revisions;
    private customXMLProps;
    private paraID;
    private commentParaID;
    private commentParaIDInfo;
    private replyCommentIdCollection;
    private imageRelationIds;
    private svgImageRelationIds;
    private isInsideComment;
    private commentId;
    private currentCommentId;
    private jsonObject;
    private trackChangesId;
    private prevRevisionIds;
    private isRevisionContinuous;
    private formFieldShading;
    private trackChanges;
    private compatibilityMode;
    private isBookmarkAtEnd;
    private isBookmarkAtRowEnd;
    private keywordIndex;
    private isHeaderFooter;
    private isSerializeFootEndNote;
    private containerWidth;
    private readonly bookmarks;
    private readonly documentImages;
    private readonly svgImages;
    private readonly externalImages;
    private readonly headerFooterImages;
    private readonly headerFooterSvgImages;
    private readonly documentCharts;
    private readonly headersFooters;
    private readonly endnotesFootnotes;
    /**
     * @private
     * @param {DocumentHelper} documentHelper - Document helper
     * @param {string} fileName - file name
     * @returns {void}
     */
    save(documentHelper: DocumentHelper, fileName: string): void;
    private saveInternal;
    /**
     * @private
     * @param {DocumentHelper} documentHelper - Document helper
     * @returns {Promise<Blob>} - Return Promise
     */
    saveAsBlob(documentHelper: DocumentHelper): Promise<Blob>;
    private serializeExcelFiles;
    /**
     * @private
     * @returns {void}
     */
    saveExcel(): void;
    /**
     * @private
     * @returns {void}
     */
    destroy(): void;
    private serialize;
    private setDocument;
    private clearDocument;
    private serializeDocument;
    private writeCommonAttributeStrings;
    private writeDup;
    private writeCustom;
    private serializeDocumentBody;
    private serializeSection;
    private serializeComments;
    private serializeThemes;
    private themeFont;
    private themeType;
    private serializeCommentCommonAttribute;
    private serializeCommentInternal;
    private retrieveCommentText;
    private serializeCommentsExtended;
    private serializeCommentsExInternal;
    private serializeSectionProperties;
    private serializeFootNotesPr;
    private getFootNoteNumberFormat;
    private getFootNoteNumberRestart;
    private getPageNumberFormat;
    private serializeEndNotesPr;
    private serializeColumns;
    private serializePageSetup;
    private serializePageSize;
    private serializePageMargins;
    private serializePageNumberType;
    private serializeSectionType;
    private serializeHFReference;
    private addHeaderFooter;
    private serializeBodyItems;
    private serializeContentControl;
    private serializeContentProperties;
    private toUnicode;
    private serializeContentControlList;
    private serializeContentParagraph;
    private serializeContentControlDate;
    private serializeBodyItem;
    private serializeParagraph;
    private serializeRevisionStart;
    private serializeTrackChanges;
    private retrieveRevision;
    private serializeParagraphItems;
    private serializeEFReference;
    private addFootnotesEndnotes;
    private serializeEndnotesFootnote;
    private serializeInlineEndnotes;
    private serializeInlineFootnotes;
    private writeEFCommonAttributes;
    private serializeFootnotes;
    private serializeEndnotes;
    private serializeRevisionEnd;
    private serializeComment;
    private serializeCommentItems;
    private serializeBiDirectionalOverride;
    private serializeEditRange;
    private serializeBookMark;
    private getBookmarkId;
    private serializePicture;
    private serializeShape;
    private serializeDrawing;
    private serializeWrappingPictureAndShape;
    private serializeInlinePictureAndShape;
    private serializePictureAndShapeDistance;
    private writeDefaultDistAttribute;
    private serializeInlineCharts;
    private serializeDrawingGraphicsChart;
    private getBase64ImageString;
    private getNextChartName;
    private serializeChart;
    private serializeChartStructure;
    private serializeChartXML;
    private serializeChartColors;
    private serializeChartColor;
    private serializeChartExcelData;
    private serializeWorkBook;
    private serializeExcelStyles;
    private serializeExcelData;
    private serializeSharedString;
    private serializeExcelSheet;
    private serializeExcelContentTypes;
    private serializeExcelRelation;
    private serializeExcelGeneralRelations;
    private getNextExcelRelationShipID;
    private getNextChartRelationShipID;
    private serializeChartData;
    private serializeChartPlotArea;
    private serializeChartLegend;
    private serializeChartErrorBar;
    private errorBarValueType;
    private serializeCategoryAxis;
    private serializeValueAxis;
    private serializeAxis;
    private parseChartTrendLines;
    private chartTrendLineType;
    private parseChartDataLabels;
    private serializeShapeProperties;
    private serializeDefaultShapeProperties;
    private serializeDefaultLineProperties;
    private serializeTextProperties;
    private serializeChartTitleFont;
    private serializeChartSolidFill;
    private serializeFont;
    private parseChartSeriesColor;
    private parseChartDataPoint;
    private serializeChartCategory;
    private serializeChartValue;
    private serializeChartYValue;
    private chartType;
    private chartGrouping;
    private chartLegendPosition;
    private updatechartId;
    private addChartRelation;
    /**
     * @private
     */
    startsWith(sourceString: string, startString: string): boolean;
    private serializeShapeDrawingGraphics;
    private getTextVerticalAlignmentProperty;
    private serializeShapeWrapStyle;
    private serializeDrawingGraphics;
    private serializeBlipExtensions;
    private updateShapeId;
    private addImageRelation;
    private updateHFImageRels;
    private serializeTable;
    private getMergeCellFormat;
    private serializeTableCell;
    private serializeTableGrid;
    private serializeTableRows;
    private serializeRow;
    private serializeRowFormat;
    private serializeCells;
    private serializeCell;
    private createCellForMerge;
    private serializeCellFormat;
    private serializeCellWidth;
    private serializeCellMerge;
    private createMerge;
    private serializeColumnSpan;
    private checkMergeCell;
    private serializeGridSpan;
    private serializeTableCellDirection;
    private serializeCellVerticalAlign;
    private serializeGridColumns;
    private serializeTableFormat;
    private serializeTablePositioning;
    private serializeTableMargins;
    private serializeRowMargins;
    private serializeCellMargins;
    private serializeMargins;
    private serializeShading;
    private getTextureStyle;
    private serializeParagraphBorders;
    private serializeTableBorders;
    private serializeBorders;
    private serializeTblLayout;
    private serializeBorder;
    private getBorderStyle;
    private serializeTableIndentation;
    private serializeCellSpacing;
    private serializeTableWidth;
    private serializeTableAlignment;
    private serializeFieldCharacter;
    private serializeTextRange;
    private retrieveDeleteRevision;
    private serializeParagraphFormat;
    private getOutlineLevelValue;
    private serializeTabs;
    private serializeTab;
    private getTextWrappingType;
    private getTextWrappingStyle;
    private getDateStorageFormat;
    private getDateCalendarType;
    private getContentControlAppearance;
    private getTextFormFieldFormat;
    private getTextFormFieldType;
    private getTabLeader;
    private getTabJustification;
    private getTableVerticalAlignment;
    private getTableHorizontalAlignment;
    private getTableVerticalRelationEnumValue;
    private getTableVerticalRelation;
    private getTableHorizontalRelation;
    private getVerticalOrigin;
    private getHorizontalOrigin;
    private getShapeVerticalAlignment;
    private getShapeHorizontalAlignment;
    private getBiDirectionalOverride;
    private getBreakClearType;
    private serializeListFormat;
    private serializeParagraphAlignment;
    private serializeParagraphSpacing;
    private serializeIndentation;
    private serializeCustomXMLMapping;
    private customXMLRelation;
    private createXMLItem;
    private createXMLItemProps;
    private serializeStyles;
    private serializeDefaultStyles;
    private serializeDocumentStyles;
    private serializeCharacterFormat;
    private getColor;
    private getUnderlineStyle;
    private getHighlightColor;
    private serializeBoolProperty;
    private serializeNumberings;
    private serializeAbstractListStyles;
    private serializeListInstances;
    private generateHex;
    private roundToTwoDecimal;
    private serializeListLevel;
    private serializeLevelOverrides;
    private getLevelPattern;
    private serializeLevelText;
    private serializeLevelFollow;
    private serializeThemeFontLang;
    private serializeDocumentProtectionSettings;
    private serializeSettings;
    private serializeCoreProperties;
    private serializeAppProperties;
    private serializeFontTable;
    private serializeSettingsRelation;
    private getCompatibilityModeEnumValue;
    private serializeHeaderFooters;
    private serializeHeaderFooter;
    private serializeHeader;
    private serializeHFRelations;
    private writeHFCommonAttributes;
    private serializeFooter;
    private serializeDocumentRelations;
    private serializeChartDocumentRelations;
    private serializeChartRelations;
    private serializeImagesRelations;
    private serializeSvgImageRelation;
    /**
     * @private
     */
    encodedString(input: string): Uint8Array;
    private serializeExternalLinkImages;
    private serializeHeaderFooterRelations;
    private serializeHFRelation;
    private serializeRelationShip;
    private getNextRelationShipID;
    private getEFNextRelationShipID;
    private serializeGeneralRelations;
    private serializeContentTypes;
    private serializeHFContentTypes;
    private serializeHeaderFootersContentType;
    private SerializeEFContentTypes;
    private serializeEFContentType;
    private serializeOverrideContentType;
    private serializeDefaultContentType;
    private resetRelationShipID;
    private resetExcelRelationShipId;
    private resetChartRelationShipId;
    private close;
}
