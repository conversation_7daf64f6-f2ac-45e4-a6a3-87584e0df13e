@import 'ej2-base/styles/definition/bootstrap4.scss';
@import '../spreadsheet-ribbon/bootstrap4-definition.scss';
@import 'ej2-buttons/styles/button/bootstrap4-definition.scss';
@import 'ej2-buttons/styles/check-box/bootstrap4-definition.scss';
@import 'ej2-buttons/styles/radio-button/bootstrap4-definition.scss';
@import 'ej2-buttons/styles/switch/bootstrap4-definition.scss';
@import 'ej2-navigations/styles/toolbar/bootstrap4-definition.scss';
@import 'ej2-navigations/styles/tab/bootstrap4-definition.scss';
@import 'ej2-navigations/styles/context-menu/bootstrap4-definition.scss';
@import 'ej2-navigations/styles/menu/bootstrap4-definition.scss';
@import 'ej2-navigations/styles/treeview/bootstrap4-definition.scss';
@import 'ej2-grids/styles/excel-filter/bootstrap4-definition.scss';
@import 'ej2-calendars/styles/datepicker/bootstrap4-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/bootstrap4-definition.scss';
@import 'ej2-inputs/styles/color-picker/bootstrap4-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/bootstrap4-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/bootstrap4-definition.scss';
@import 'ej2-dropdowns/styles/list-box/bootstrap4-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/bootstrap4-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/bootstrap4-definition.scss';
@import 'bootstrap4-definition.scss';
@import 'icons/bootstrap4.scss';
@import 'all.scss';
