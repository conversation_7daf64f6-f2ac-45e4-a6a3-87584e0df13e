@import 'ej2-base/styles/definition/material.scss';
@import '../spreadsheet-ribbon/material-definition.scss';
@import 'ej2-buttons/styles/button/material-definition.scss';
@import 'ej2-buttons/styles/check-box/material-definition.scss';
@import 'ej2-buttons/styles/radio-button/material-definition.scss';
@import 'ej2-buttons/styles/switch/material-definition.scss';
@import 'ej2-navigations/styles/toolbar/material-definition.scss';
@import 'ej2-navigations/styles/tab/material-definition.scss';
@import 'ej2-navigations/styles/context-menu/material-definition.scss';
@import 'ej2-navigations/styles/menu/material-definition.scss';
@import 'ej2-navigations/styles/treeview/material-definition.scss';
@import 'ej2-grids/styles/excel-filter/material-definition.scss';
@import 'ej2-calendars/styles/datepicker/material-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/material-definition.scss';
@import 'ej2-inputs/styles/color-picker/material-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/material-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/material-definition.scss';
@import 'ej2-dropdowns/styles/list-box/material-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/material-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/material-definition.scss';
@import 'material-definition.scss';
@import 'icons/material.scss';
@import 'all.scss';
