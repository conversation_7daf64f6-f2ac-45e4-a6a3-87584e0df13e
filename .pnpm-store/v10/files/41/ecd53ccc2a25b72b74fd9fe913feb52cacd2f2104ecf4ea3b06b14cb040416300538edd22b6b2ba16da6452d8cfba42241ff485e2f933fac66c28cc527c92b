import { Spreadsheet } from '../index';
/**
 * Represents Data Validation support for Spreadsheet.
 */
export declare class DataValidation {
    private parent;
    private data;
    private listObj;
    private dataList;
    private typeData;
    private operatorData;
    /**
     * Constructor for the Spreadsheet Data Validation module.
     *
     * @param {Spreadsheet} parent - Constructor for the Spreadsheet Data Validation module.
     */
    constructor(parent: Spreadsheet);
    /**
     * To destroy the Data Validation module.
     *
     * @returns {void}
     */
    protected destroy(): void;
    private addEventListener;
    private removeEventListener;
    private removeValidationHandler;
    private keyUpHandler;
    private listOpen;
    private invalidDataHandler;
    private listHandler;
    private setDropDownListIndex;
    private updateDataSource;
    private listValueChange;
    private getRange;
    private initiateDataValidationHandler;
    private dataValidationContent;
    private validateRange;
    private moreValidationDlg;
    private extendValidationDlg;
    private userInput;
    private dlgClickHandler;
    private formattedValue;
    private formattedType;
    private isDialogValidator;
    private getDateAsNumber;
    private isValidationHandler;
    private checkDataValidation;
    private formatValidation;
    private InvalidElementHandler;
    private validationErrorHandler;
    private errorDlgHandler;
    /**
     * Gets the module name.
     *
     * @returns {string} - Gets the module name.
     */
    protected getModuleName(): string;
}
