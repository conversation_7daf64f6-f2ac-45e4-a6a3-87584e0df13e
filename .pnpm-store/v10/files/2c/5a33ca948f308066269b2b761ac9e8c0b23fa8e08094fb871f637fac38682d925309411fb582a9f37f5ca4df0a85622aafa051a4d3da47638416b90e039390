@import 'ej2-base/styles/definition/fluent-dark.scss';
@import 'ej2-navigations/styles/pager/fluent-dark-definition.scss';
@import '../excel-filter/fluent-dark-definition.scss';
@import 'ej2-inputs/styles/input/fluent-dark-definition.scss';
@import 'ej2-inputs/styles/numerictextbox/fluent-dark-definition.scss';
@import 'ej2-buttons/styles/button/fluent-dark-definition.scss';
@import 'ej2-buttons/styles/radio-button/fluent-dark-definition.scss';
@import 'ej2-buttons/styles/check-box/fluent-dark-definition.scss';
@import 'ej2-dropdowns/styles/auto-complete/fluent-dark-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/fluent-dark-definition.scss';
@import 'ej2-popups/styles/dialog/fluent-dark-definition.scss';
@import 'ej2-popups/styles/spinner/fluent-dark-definition.scss';
@import 'ej2-popups/styles/tooltip/fluent-dark-definition.scss';
@import 'ej2-calendars/styles/datepicker/fluent-dark-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/fluent-dark-definition.scss';
@import 'ej2-navigations/styles/toolbar/fluent-dark-definition.scss';
@import 'ej2-navigations/styles/context-menu/fluent-dark-definition.scss';
@import 'ej2-notifications/styles/skeleton/fluent-dark-definition.scss';
@import 'fluent-dark-definition.scss';
@import 'icons/fluent-dark.scss';
@import 'all.scss';
