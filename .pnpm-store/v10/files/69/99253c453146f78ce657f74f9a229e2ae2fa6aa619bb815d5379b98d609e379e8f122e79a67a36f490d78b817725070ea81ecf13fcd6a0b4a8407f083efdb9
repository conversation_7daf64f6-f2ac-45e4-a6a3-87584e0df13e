import { ModuleDeclaration } from '@syncfusion/ej2-base';
import { Workbook } from '../base/index';
/**
 * To get Workbook required modules.
 *
 * @hidden
 * @param {Workbook} context - Specifies the context.
 * @param {ModuleDeclaration[]} modules - Specifies the modules.
 * @returns {ModuleDeclaration[]} - To get Workbook required modules.
 */
export declare function getWorkbookRequiredModules(context: Workbook, modules?: ModuleDeclaration[]): ModuleDeclaration[];
