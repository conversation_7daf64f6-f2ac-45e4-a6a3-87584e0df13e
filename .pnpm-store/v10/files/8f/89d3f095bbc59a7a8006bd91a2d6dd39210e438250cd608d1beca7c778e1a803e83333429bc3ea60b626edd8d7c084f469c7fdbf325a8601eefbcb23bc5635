/**
 * @hidden
 */
var BookmarkBase = /** @class */ (function () {
    function BookmarkBase() {
        this.HasChild = false;
    }
    return BookmarkBase;
}());
export { BookmarkBase };
/**
 * @hidden
 */
var BookmarkDestination = /** @class */ (function () {
    function BookmarkDestination() {
        // constructor
    }
    return BookmarkDestination;
}());
export { BookmarkDestination };
/**
 * @hidden
 */
var BookmarkStyles = /** @class */ (function () {
    function BookmarkStyles() {
        // constructor
    }
    return BookmarkStyles;
}());
export { BookmarkStyles };
