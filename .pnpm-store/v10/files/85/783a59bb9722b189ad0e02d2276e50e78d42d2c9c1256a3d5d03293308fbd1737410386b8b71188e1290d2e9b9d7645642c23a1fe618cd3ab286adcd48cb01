

@import 'ej2-buttons/styles/button/material3-dark-definition.scss';
@import 'ej2-buttons/styles/check-box/material3-dark-definition.scss';
@import 'ej2-buttons/styles/radio-button/material3-dark-definition.scss';
@import 'ej2-buttons/styles/switch/material3-dark-definition.scss';
@import 'ej2-navigations/styles/toolbar/material3-dark-definition.scss';
@import 'ej2-navigations/styles/tab/material3-dark-definition.scss';
@import 'ej2-navigations/styles/context-menu/material3-dark-definition.scss';
@import 'ej2-navigations/styles/menu/material3-dark-definition.scss';
@import 'ej2-navigations/styles/treeview/material3-dark-definition.scss';
@import 'ej2-grids/styles/excel-filter/material3-dark-definition.scss';
@import 'ej2-calendars/styles/datepicker/material3-dark-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/material3-dark-definition.scss';
@import 'ej2-inputs/styles/color-picker/material3-dark-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/material3-dark-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/material3-dark-definition.scss';
@import 'ej2-dropdowns/styles/list-box/material3-dark-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/material3-dark-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/material3-dark-definition.scss';
@import 'material3-dark-definition.scss';
@import 'icons/material3-dark.scss';
@import 'all.scss';
