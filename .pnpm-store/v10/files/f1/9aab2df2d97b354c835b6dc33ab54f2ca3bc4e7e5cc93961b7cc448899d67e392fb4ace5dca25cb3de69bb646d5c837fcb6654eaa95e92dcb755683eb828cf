import { createElement, L10n, isNullOrUndefined } from '@syncfusion/ej2-base';
import { DropDownButton } from '@syncfusion/ej2-splitbuttons';
import { Button } from '@syncfusion/ej2-buttons';
/**
 * Represents document editor status bar.
 *
 * @private
 */
var StatusBar = /** @class */ (function () {
    function StatusBar(parentElement, docEditor) {
        this.startPage = 1;
        this.statusBarDiv = parentElement;
        this.container = docEditor;
        this.initializeStatusBar();
        this.wireEvents();
    }
    Object.defineProperty(StatusBar.prototype, "documentEditor", {
        get: function () {
            return this.container ? this.container.documentEditor : undefined;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(StatusBar.prototype, "editorPageCount", {
        get: function () {
            return this.documentEditor ? this.documentEditor.pageCount : 1;
        },
        enumerable: true,
        configurable: true
    });
    StatusBar.prototype.initializeStatusBar = function () {
        var _this = this;
        var isRtl = this.container.enableRtl;
        this.documentEditor.enableSpellCheck = (this.container.enableSpellCheck) ? true : false;
        this.localObj = new L10n('documenteditorcontainer', this.container.defaultLocale, this.container.locale);
        var styles = isRtl ? 'padding-right:16px' : 'padding-left:16px';
        var div = createElement('div', { className: (this.container.enableSpellCheck) ? 'e-de-ctnr-pg-no' : 'e-de-ctnr-pg-no-spellout', styles: styles });
        this.statusBarDiv.appendChild(div);
        var label = createElement('span');
        label.textContent = this.localObj.getConstant('Page') + ' ';
        div.appendChild(label);
        this.pageNumberInput = createElement('input', { styles: 'text-transform:capitalize;white-space:pre;overflow:hidden;user-select:none;cursor:text', attrs: { type: 'text', 'aria-label': this.localObj.getConstant('Current Page Number') }, className: 'e-de-pagenumber-input' });
        this.editablePageNumber = createElement('div', { styles: 'display: inline-flex', className: 'e-input e-de-pagenumber-text' });
        this.editablePageNumber.appendChild(this.pageNumberInput);
        var pageNumberOfLabelStyle = "";
        if (isRtl) {
            label.style.marginLeft = '6px';
            this.editablePageNumber.style.marginLeft = '6px';
            pageNumberOfLabelStyle = 'padding-left:5px';
        }
        else {
            label.style.marginRight = '6px';
            this.editablePageNumber.style.marginRight = '6px';
            pageNumberOfLabelStyle = 'padding-right:5px';
        }
        this.updatePageNumber();
        div.appendChild(this.editablePageNumber);
        this.editablePageNumber.setAttribute('title', this.localObj.getConstant('Current Page Number'));
        var label1 = createElement('span', { styles: pageNumberOfLabelStyle });
        label1.textContent = ' ' + this.localObj.getConstant('of') + ' ';
        div.appendChild(label1);
        this.pageCount = createElement('span');
        div.appendChild(this.pageCount);
        this.updatePageCount();
        if (this.documentEditor.enableSpellCheck) {
            var verticalLine = createElement('div', { className: 'e-de-statusbar-separator' });
            this.statusBarDiv.appendChild(verticalLine);
            var spellCheckBtn = this.addSpellCheckElement();
            this.spellCheckButton.appendTo(spellCheckBtn);
        }
        this.pageButton = this.createButtonTemplate((this.container.enableSpellCheck) ? 'e-de-statusbar-pageweb e-btn-pageweb-spellcheck' : 'e-de-statusbar-pageweb', 'e-de-printlayout e-icons', 'Print layout', this.statusBarDiv, this.pageButton, (this.documentEditor.layoutType === 'Pages') ? true : false);
        this.webButton = this.createButtonTemplate('e-de-statusbar-pageweb', 'e-de-weblayout e-icons', 'Web layout', this.statusBarDiv, this.webButton, (this.documentEditor.layoutType === 'Continuous') ? true : false);
        this.pageButton.addEventListener('click', function () {
            _this.documentEditor.layoutType = 'Pages';
            _this.addRemoveClass(_this.pageButton, _this.webButton);
            _this.documentEditor.focusIn();
        });
        this.webButton.addEventListener('click', function () {
            _this.documentEditor.layoutType = 'Continuous';
            _this.addRemoveClass(_this.webButton, _this.pageButton);
            _this.documentEditor.focusIn();
        });
        var zoomBtn = createElement('button', {
            className: 'e-de-statusbar-zoom', attrs: { type: 'button' }
        });
        this.statusBarDiv.appendChild(zoomBtn);
        zoomBtn.setAttribute('title', this.localObj.getConstant('ZoomLevelTooltip'));
        var items = [
            {
                text: '200%'
            },
            {
                text: '175%'
            },
            {
                text: '150%'
            },
            {
                text: '125%'
            },
            {
                text: '100%'
            },
            {
                text: '75%'
            },
            {
                text: '50%'
            },
            {
                text: '25%'
            },
            {
                separator: true
            },
            {
                text: this.localObj.getConstant('Fit one page')
            },
            {
                text: this.localObj.getConstant('Fit page width')
            }
        ];
        this.zoom = new DropDownButton({ content: '100%', items: items, enableRtl: this.container.enableRtl, select: this.onZoom.bind(this) });
        this.zoom.isStringTemplate = true;
        this.zoom.appendTo(zoomBtn);
    };
    StatusBar.prototype.addSpellCheckElement = function () {
        var _this = this;
        var spellCheckBtn = createElement('button', {
            className: 'e-de-statusbar-spellcheck'
        });
        this.statusBarDiv.appendChild(spellCheckBtn);
        spellCheckBtn.setAttribute('title', 'Spell Checker options');
        var spellCheckItems = [
            {
                text: this.localObj.getConstant('Spell Check')
            },
            {
                text: this.localObj.getConstant('Underline errors')
            }
        ];
        this.spellCheckButton = new DropDownButton({
            content: this.localObj.getConstant('Spelling'), items: spellCheckItems, enableRtl: this.container.enableRtl, select: this.onSpellCheck.bind(this),
            beforeItemRender: function (args) {
                args.element.innerHTML = '<span></span>' + args.item.text;
                if (isNullOrUndefined(_this.currentLanguage)) {
                    _this.currentLanguage = _this.documentEditor.spellChecker.languageID;
                }
                if (isNullOrUndefined(_this.allowSuggestion)) {
                    _this.allowSuggestion = _this.documentEditor.spellChecker.allowSpellCheckAndSuggestion;
                }
                var span = args.element.children[0];
                if (args.item.text === _this.localObj.getConstant('Spell Check') && _this.documentEditor.enableSpellCheck &&
                    _this.documentEditor.spellChecker.enableSpellCheck) {
                    span.style.marginRight = '10px';
                    span.setAttribute('class', 'e-de-selected-spellcheck-item');
                }
                else if (args.item.text === _this.localObj.getConstant('Underline errors') && _this.documentEditor.enableSpellCheck &&
                    _this.documentEditor.spellChecker.enableSpellCheck && !_this.documentEditor.spellChecker.removeUnderline) {
                    span.style.marginRight = '10px';
                    span.setAttribute('class', 'e-de-selected-underline-item');
                }
                else {
                    span.style.marginRight = '25px';
                    args.element.children[0].classList.remove('e-de-selected-spellcheck-item');
                    args.element.children[0].classList.remove('e-de-selected-underline-item');
                }
            }
        });
        return spellCheckBtn;
    };
    StatusBar.prototype.onZoom = function (args) {
        this.setZoomValue(args.item.text);
        this.updateZoomContent();
    };
    StatusBar.prototype.onSpellCheck = function (args) {
        this.setSpellCheckValue(args.item.text);
    };
    StatusBar.prototype.updateZoomContent = function () {
        this.zoom.content = Math.round(this.documentEditor.zoomFactor * 100) + '%';
    };
    StatusBar.prototype.setSpellCheckValue = function (text) {
        this.spellCheckButton.content = this.localObj.getConstant('Spelling');
        if (text.match(this.localObj.getConstant('Spell Check'))) {
            this.documentEditor.spellChecker.enableSpellCheck = (this.documentEditor.spellChecker.enableSpellCheck) ? false : true;
            this.documentEditor.documentHelper.triggerSpellCheck = false;
            this.documentEditor.documentHelper.triggerElementsOnLoading = false;
        }
        else if (text.match(this.localObj.getConstant('Underline errors'))) {
            if (this.documentEditor.enableSpellCheck && this.documentEditor.spellChecker.enableSpellCheck) {
                this.documentEditor.spellChecker.removeUnderline = (this.documentEditor.spellChecker.removeUnderline) ? false : true;
            }
        }
    };
    StatusBar.prototype.setZoomValue = function (text) {
        if (text.match(this.localObj.getConstant('Fit one page'))) {
            this.documentEditor.fitPage('FitOnePage');
        }
        else if (text.match(this.localObj.getConstant('Fit page width'))) {
            this.documentEditor.fitPage('FitPageWidth');
        }
        else {
            this.documentEditor.zoomFactor = parseInt(text, 10) / 100;
        }
    };
    /**
     * Updates page count.
     *
     * @returns {void}
     */
    StatusBar.prototype.updatePageCount = function () {
        this.pageCount.textContent = this.editorPageCount.toString();
    };
    /**
     * Updates page number.
     *
     * @returns {void}
     */
    StatusBar.prototype.updatePageNumber = function () {
        this.pageNumberInput.value = this.startPage.toString();
        this.updatePageNumberWidth();
    };
    StatusBar.prototype.updatePageNumberOnViewChange = function (args) {
        if (this.documentEditor.selection
            && this.documentEditor.selection.startPage >= args.startPage && this.documentEditor.selection.startPage <= args.endPage) {
            this.startPage = this.documentEditor.selection.startPage;
        }
        else {
            this.startPage = args.startPage;
        }
        this.updatePageNumber();
        this.updatePageCount();
    };
    StatusBar.prototype.wireEvents = function () {
        var _this = this;
        this.pageNumberInput.addEventListener('keydown', function (e) {
            if (e.which === 13) {
                e.preventDefault();
                var pageNumber = parseInt(_this.pageNumberInput.value, 10);
                if (pageNumber > _this.editorPageCount) {
                    _this.updatePageNumber();
                }
                else {
                    if (_this.documentEditor.selection) {
                        _this.documentEditor.selection.goToPage(parseInt(_this.pageNumberInput.value, 10));
                        _this.documentEditor.focusIn();
                    }
                    else {
                        _this.documentEditor.scrollToPage(parseInt(_this.pageNumberInput.value, 10));
                    }
                }
                _this.pageNumberInput.contentEditable = 'false';
                if (_this.pageNumberInput.value === '') {
                    _this.updatePageNumber();
                }
            }
            if (e.which > 64) {
                e.preventDefault();
            }
        });
        this.pageNumberInput.addEventListener('keyup', function () {
            _this.updatePageNumberWidth();
        });
        this.pageNumberInput.addEventListener('blur', function () {
            if (_this.pageNumberInput.value === '' || parseInt(_this.pageNumberInput.value, 10) > _this.editorPageCount) {
                _this.updatePageNumber();
            }
            _this.pageNumberInput.contentEditable = 'false';
        });
        this.pageNumberInput.addEventListener('focus', function () {
            _this.pageNumberInput.select();
        });
    };
    StatusBar.prototype.updatePageNumberWidth = function () {
        if (this.pageNumberInput) {
            this.pageNumberInput.style.width = this.pageNumberInput.value.length >= 3 ? '30px' : '22px';
        }
    };
    /**
     * @private
     * @returns {void}
     */
    StatusBar.prototype.toggleWebLayout = function () {
        this.addRemoveClass(this.pageButton, this.webButton);
    };
    /**
     * @private
     * @returns {void}
     */
    StatusBar.prototype.togglePageLayout = function () {
        this.addRemoveClass(this.webButton, this.pageButton);
    };
    StatusBar.prototype.addRemoveClass = function (addToElement, removeFromElement) {
        addToElement.classList.add('e-btn-toggle');
        if (removeFromElement.classList.contains('e-btn-toggle')) {
            removeFromElement.classList.remove('e-btn-toggle');
        }
    };
    /* eslint-disable-next-line max-len */
    StatusBar.prototype.createButtonTemplate = function (className, iconcss, toolTipText, div, appendDiv, toggle) {
        appendDiv = createElement('Button', { className: className, attrs: { type: 'button' } });
        div.appendChild(appendDiv);
        var btn = new Button({
            cssClass: className, iconCss: iconcss, enableRtl: this.container.enableRtl
        });
        if (toggle === true) {
            appendDiv.classList.add('e-btn-toggle');
        }
        btn.appendTo(appendDiv);
        appendDiv.setAttribute('title', this.localObj.getConstant(toolTipText));
        if (toolTipText === 'Web layout') {
            this.webBtn = btn;
        }
        else {
            this.pageBtn = btn;
        }
        return appendDiv;
    };
    /**
     * @private
     * @returns {void}
     */
    StatusBar.prototype.destroy = function () {
        if (this.zoom) {
            this.zoom.destroy();
            this.zoom = undefined;
        }
        if (this.spellCheckButton) {
            this.spellCheckButton.destroy();
            this.spellCheckButton = undefined;
        }
        if (this.pageBtn) {
            this.pageBtn.destroy();
            this.pageBtn = undefined;
        }
        if (this.webBtn) {
            this.webBtn.destroy();
            this.webBtn = undefined;
        }
        this.pageButton = undefined;
        this.webButton = undefined;
        this.pageNumberInput = undefined;
        this.statusBarDiv = undefined;
        this.pageCount = undefined;
        this.editablePageNumber = undefined;
        this.localObj = undefined;
        this.container = undefined;
    };
    return StatusBar;
}());
export { StatusBar };
