@include export-module('spreadsheet-material-icons') {
  .e-spreadsheet {
    & .e-findtool-dlg {

      & .e-prev-icon {
        &::before {
          content: '\e910';
        }
      }

      & .e-next-icon {
        &::before {
          content: '\e916';
        }
      }

      & .e-option-icon {
        &::before {
          content: '\e984';
        }
      }

      & .e-close {
        &::before {
          content: '\ec0f';
        }
      }
    }

    & .e-ribbon {
      & .e-bold-icon {
        &::before {
          content: '\e339';
        }
      }

      & .e-italic-icon {
        &::before {
          content: '\e35a';
        }
      }

      & .e-underline-icon {
        &::before {
          content: '\e343';
        }
      }

      & .e-line-through-icon {
        &::before {
          content: '\e332';
        }
      }

      & .e-font-color {
        &::before {
          content: '\e34c';
        }
      }

      & .e-fill-color {
        &::before {
          content: '\e35c';
        }
      }

      .e-wrap-icon::before {
        content: '\e824';
      }

      & .e-hide-headers {
        &::before {
          content: '\e718';
        }
      }

      & .e-hide-gridlines {
        &::before {
          content: '\e719';
        }
      }

      & .e-freeze-pane::before {
        content: '\e88d';
      }

      & .e-freeze-row::before {
        content: '\e88f';
      }

      & .e-freeze-column::before {
        content: '\e88e';
      }

      & .e-insert-function {
        &::before {
          content: '\e717';
        }
      }

      & .e-hyperlink-icon {
        &::before {
          content: '\e290';
        }
      }

      & .e-image-icon {
        &::before {
          content: '\e335';
        }
      }

      & .e-merge-icon {
        &::before {
          content: '\eb93';
        }
      }
    }

    & .e-insert-function .e-btn-icon {
      &::before {
        content: '\e717';
      }
    }

    & .e-add-icon {
      &::before {
        content: '\e823';
      }
    }

    & .e-sheets-list .e-btn-icon {
      &::before {
        content: '\e99a';
      }
    }

    & .e-formula-bar-panel .e-drop-icon {
      &::before {
        content: '\e94d';
      }
    }

    & .e-file-menu-icon {
      &::before {
        content: '\e984';
      }
    }

    & .e-tick-icon {
      &::before {
        content: '\e935';
      }
    }
  }

  .e-spreadsheet-find-ddb {
    & .e-search-icon {
      &::before {
        content: '\e993';
      }
    }
  }

  .e-spreadsheet-contextmenu.e-contextmenu-wrapper {
    & .e-delete {
      &::before {
        content: '\e94a';
      }
    }
  }

  .e-popup {
    &.e-dropdown-popup .e-selected-icon,
    &.e-numformat-ddb.e-dropdown-popup .e-active-item,
    &.e-menu-popup .e-selected-icon {
      &::before {
        content: '\e935';
      }
    }
  }

  .e-borders-ddb {
    & .e-top-borders {
      &::before {
        content: '\e281';
      }
    }

    & .e-left-borders {
      &::before {
        content: '\e291';
      }
    }

    & .e-right-borders {
      &::before {
        content: '\e288';
      }
    }

    & .e-bottom-borders {
      &::before {
        content: '\e298';
      }
    }

    & .e-all-borders {
      &::before {
        content: '\eb95';
      }
    }

    & .e-horizontal-borders {
      &::before {
        content: '\e276';
      }
    }

    & .e-vertical-borders {
      &::before {
        content: '\e287';
      }
    }

    & .e-outside-borders {
      &::before {
        content: '\eb66';
      }
    }

    & .e-inside-borders {
      &::before {
        content: '\eb88';
      }
    }

    & .e-no-borders {
      &::before {
        content: '\e195';
      }
    }
  }

  .e-align-ddb {
    & .e-left-icon {
      &::before {
        content: '\e33a';
      }
    }

    & .e-center-icon {
      &::before {
        content: '\e35e';
      }
    }

    & .e-right-icon {
      &::before {
        content: '\e34d';
      }
    }

    & .e-bottom-icon {
      &::before {
        content: '\eb3d';
      }
    }

    & .e-middle-icon {
      &::before {
        content: '\eb4a';
      }
    }

    & .e-top-icon {
      &::before {
        content: '\eb49';
      }
    }
  }

  .e-datavalidation-ddb {
    & .e-datavalidation-icon {
      &::before {
        content: '\e196';
      }
    }
  }

  .e-clear-ddb {
    & .e-clear-icon {
      &::before {
        content: '\e340';
      }
    }
  }

  .e-dragfill-ddb {
    & .e-dragfill-icon {
      &::before {
        content: '\e92a';
      }
    }
  }

  .e-chart-ddb,
  .e-chart-type-ddb {
    & .e-chart-icon,
    & .e-chart-type-icon {
      &::before {
        content: '\e440';
      }
    }
  }

  .e-chart-menu .e-ul .e-menu-item,
  .e-chart-type-menu .e-ul .e-menu-item {
    padding: 0 16px 0 10px;

    .e-menu-icon {
      font-size: $spreadsheet-chart-icon-font-size;
      margin-right: 10px;
    }

    .e-bar {
      font-size: 42px;
      left: -6px;
      position: relative;
    }
  }

  .e-addchart-menu .e-ul .e-menu-item .e-menu-icon {
    font-size: $spreadsheet-chart-icon-font-size;
  }

  .e-chart-menu,
  .e-chart-type-menu {
    & .e-column {
      &::before {
        content: '\e440';
      }
    }

    & .e-bar {
      &::before {
        content: '\e877';
      }
    }

    & .e-area {
      &::before {
        content: '\e455';
      }
    }

    & .e-pie-doughnut {
      &::before {
        content: '\e458';
      }
    }

    & .e-line {
      &::before {
        content: '\e449';
      }
    }

    & .e-scatter {
      &::before {
        content: '\e443';
      }
    }

    & .e-column-main {
      & .e-clusteredcolumn {
        &::before {
          content: '\e847';
        }
      }

      & .e-stackedcolumn {
        &::before {
          content: '\e888';
        }
      }

      & .e-stackedcolumn100 {
        &::before {
          content: '\e868';
        }
      }
    }

    & .e-bar-main {
      & .e-clusteredbar {
        &::before {
          content: '\e855';
        }
      }

      & .e-stackedbar {
        &::before {
          content: '\e889';
        }
      }

      & .e-stackedbar100 {
        &::before {
          content: '\e863';
        }
      }
    }

    & .e-pie-main {
      & .e-pie {
        &::before {
          content: '\e141';
        }
      }

      & .e-doughnut {
        &::before {
          content: '\e112';
        }
      }
    }

    & .e-area-main {
      & .e-area {
        &::before {
          content: '\e883';
        }
      }

      & .e-stackedarea {
        &::before {
          content: '\e875';
        }
      }

      & .e-stackedarea100 {
        &::before {
          content: '\e900';
        }
      }
    }

    & .e-line-main {
      & .e-line {
        &::before {
          content: '\e867';
        }
      }

      & .e-line-marker {
        &::before {
          content: '\e981';
        }
      }

      & .e-stackedline {
        &::before {
          content: '\e841';
        }
      }

      & .e-stackedline-marker {
        &::before {
          content: '\e97f';
        }
      }

      & .e-stackedline100 {
        &::before {
          content: '\e871';
        }
      }

      & .e-stackedline100-marker {
        &::before {
          content: '\e980';
        }
      }
    }

    & .e-scatter-main {
      & .e-scatter {
        &::before {
          content: '\e887';
        }
      }
    }
  }

  .e-addchart-ddb {
    & .e-addchart-icon {
      &::before {
        content: '\e446';
      }
    }

    & .e-addchart-menu {
      & .e-axes {
        &::before {
          content: '\e453';
        }
      }

      & .e-axis-title {
        &::before {
          content: '\e456';
        }
      }

      & .e-chart-title {
        &::before {
          content: '\e457';
        }
      }

      & .e-data-labels {
        &::before {
          content: '\e461';
        }
      }

      & .e-gridlines {
        &::before {
          content: '\e447';
        }
      }

      & .e-legends {
        &::before {
          content: '\e442';
        }
      }
    }
  }

  .e-addchart-menu {
    & .e-ph-axes {
      &::before {
        content: '\e124';
      }
    }

    & .e-pv-axes {
      &::before {
        content: '\e492';
      }
    }

    & .e-ph-axistitle {
      &::before {
        content: '\e486';
      }
    }

    & .e-pv-axistitle {
      &::before {
        content: '\e143';
      }
    }

    & .e-ct-none {
      &::before {
        content: '\e136';
      }
    }

    & .e-ct-abovechart {
      &::before {
        content: '\e121';
      }
    }

    & .e-dl-none {
      &::before {
        content: '\e495';
      }
    }

    & .e-dl-center {
      &::before {
        content: '\e493';
      }
    }

    & .e-dl-insideend {
      &::before {
        content: '\e135';
      }
    }

    & .e-dl-insidebase {
      &::before {
        content: '\e893';
      }
    }

    & .e-dl-outsideend {
      &::before {
        content: '\e905';
      }
    }

    & .e-gl-major-horizontal {
      &::before {
        content: '\e131';
      }
    }

    & .e-gl-major-vertical {
      &::before {
        content: '\e496';
      }
    }

    & .e-gl-minor-horizontal {
      &::before {
        content: '\e145';
      }
    }

    & .e-gl-minor-vertical {
      &::before {
        content: '\e491';
      }
    }

    & .e-legends-none {
      &::before {
        content: '\e488';
      }
    }

    & .e-legends-right {
      &::before {
        content: '\e489';
      }
    }

    & .e-legends-left {
      &::before {
        content: '\e892';
      }
    }

    & .e-legends-bottom {
      &::before {
        content: '\e132';
      }
    }

    & .e-legends-top {
      &::before {
        content: '\e126';
      }
    }
  }

  .e-switch-row-column-icon {
    &::before {
      content: '\e450';
    }
  }

  .e-cf-ddb {
    & .e-conditionalformatting-icon {
      &::before {
        content: '\e401';
      }
    }
  }

  .e-cf-menu .e-ul .e-menu-item .e-menu-icon {
    font-size: $cf-menu-icon-font-size;
  }

  .e-cf-menu {
    & .e-hlcellrules {
      &::before {
        content: '\e402';
      }
    }

    & .e-topbottomrules {
      &::before {
        content: '\e403';
      }
    }

    & .e-databars {
      &::before {
        content: '\e404';
      }
    }

    & .e-colorscales {
      &::before {
        content: '\e405';
      }
    }

    & .e-iconsets {
      &::before {
        content: '\e406';
      }
    }

    & .e-clearrules {
      &::before {
        content: '\e407';
      }
    }

    & .e-greaterthan {
      &::before {
        content: '\e409';
      }
    }

    & .e-lessthan {
      &::before {
        content: '\e410';
      }
    }

    & .e-between {
      &::before {
        content: '\e411';
      }
    }

    & .e-equalto {
      &::before {
        content: '\e412';
      }
    }

    & .e-textcontains {
      &::before {
        content: '\e413';
      }
    }

    & .e-adateoccuring {
      &::before {
        content: '\e414';
      }
    }

    & .e-top10items {
      &::before {
        content: '\e403';
      }
    }

    & .e-top10 {
      &::before {
        content: '\e415';
      }
    }

    & .e-bottom10items {
      &::before {
        content: '\e416';
      }
    }

    & .e-bottom10 {
      &::before {
        content: '\e417';
      }
    }

    & .e-aboveaverage {
      &::before {
        content: '\e418';
      }
    }

    & .e-belowaverage {
      &::before {
        content: '\e419';
      }
    }

    & .e-duplicate {
      &::before {
        content: '\e420';
      }
    }
  }

  .e-menu-wrapper.e-file-menu {
    & .e-new {
      &::before {
        content: '\e712';
      }
    }

    & .e-open {
      &::before {
        content: '\e65f';
      }
    }

    & .e-save {
      &::before {
        content: '\e704';
      }
    }

    & .e-xls {
      &::before {
        content: '\e726';
      }
    }

    & .e-xlsx {
      &::before {
        content: '\e700';
      }
    }

    & .e-csv {
      &::before {
        content: '\e727';
      }
    }

    & .e-pdf {
      &::before {
        content: '\e88b';
      }
    }
  }

  .e-spreadsheet,
  .e-spreadsheet-contextmenu {
    & .e-cut-icon {
      &::before {
        content: '\e33b';
      }
    }

    & .e-copy-icon {
      &::before {
        content: '\e33d';
      }
    }

    & .e-paste-icon {
      &::before {
        content: '\e355';
      }
    }
  }

  .e-undo-icon {
    &::before {
      content: '\e341';
    }
  }

  .e-redo-icon {
    &::before {
      content: '\e354';
    }
  }

  .e-sort-filter-ddb,
  .e-spreadsheet-contextmenu {
    & .e-sort-icon {
      &::before {
        content: '\e734';
      }
    }

    & .e-sort-asc {
      &::before {
        content: '\e734';
      }
    }

    & .e-sort-desc {
      &::before {
        content: '\e733';
      }
    }

    & .e-sort-custom {
      &::before {
        content: '\e732';
      }
    }
  }

  .e-spreadsheet-contextmenu {
    & .e-hyperlink-icon {
      &::before {
        content: '\e290';
      }
    }

    & .e-edithyperlink-icon {
      &::before {
        content: '\e289';
      }
    }

    & .e-openhyperlink-icon {
      &::before {
        content: '\e278';
      }
    }

    & .e-removehyperlink-icon {
      &::before {
        content: '\e286';
      }
    }
  }

  .e-spreadsheet {
    & .e-sort-delete {
      &::before {
        content: '\e94a';
      }
    }
  }

  .e-spreadsheet,
  .e-sort-filter-ddb,
  .e-spreadsheet-contextmenu {
    & .e-filter-icon {
      &::before {
        content: '\e73a';
      }
    }

    & .e-filter-icon.e-filtered {
      &::before {
        content: '\e73f';
      }
    }

    & .e-filter-icon.e-sortasc-filter {
      &::before {
        content: '\e73d';
      }
    }

    & .e-filter-icon.e-sortdesc-filter {
      &::before {
        content: '\e73c';
      }
    }

    & .e-filter-icon.e-filtered.e-sortasc-filter {
      &::before {
        content: '\e73e';
      }
    }

    & .e-filter-icon.e-filtered.e-sortdesc-filter {
      &::before {
        content: '\e73b';
      }
    }

    & .e-filter-apply {
      &::before {
        content: '\e7ee';
      }
    }

    & .e-filter-clear {
      &::before {
        content: '\e738';
      }
    }

    & .e-filter-reapply {
      &::before {
        content: '\e74a';
      }
    }

    & .e-protect-icon {
      &::before {
        content: '\e197';
      }
    }

    & .e-sort-filter-icon {
      &::before {
        content: '\e736';
      }
    }

    & .e-password-protect-icon {
      &::before {
        content: '\e88a';
      }
    }
  }
}
