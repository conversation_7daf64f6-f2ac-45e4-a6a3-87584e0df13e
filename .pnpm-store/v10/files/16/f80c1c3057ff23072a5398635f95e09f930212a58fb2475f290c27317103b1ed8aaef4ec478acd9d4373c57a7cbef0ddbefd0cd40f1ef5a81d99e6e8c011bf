import { Spreadsheet } from '../index';
import { IOffset } from '../common/index';
/**
 * The `Scroll` module is used to handle scrolling behavior.
 *
 * @hidden
 */
export declare class Scroll {
    private parent;
    /** @hidden */
    offset: {
        left: IOffset;
        top: IOffset;
    };
    private topIndex;
    private leftIndex;
    private clientX;
    /** @hidden */
    isKeyScroll: boolean;
    private initScrollValue;
    /** @hidden */
    prevScroll: {
        scrollLeft: number;
        scrollTop: number;
    };
    /**
     * Constructor for the Spreadsheet scroll module.
     *
     * @param {Spreadsheet} parent - Constructor for the Spreadsheet scroll module.
     * @private
     */
    constructor(parent: Spreadsheet);
    private onContentScroll;
    private updateScrollValue;
    private updateNonVirtualRows;
    private updateNonVirtualCols;
    private updateTopLeftCell;
    private getRowOffset;
    private getColOffset;
    private contentLoaded;
    private updateNonVirualScrollWidth;
    private onHeaderWheel;
    private onContentWheel;
    private scrollHandler;
    private updateScroll;
    private setScrollEvent;
    private initProps;
    /**
     * @hidden
     *
     * @returns {void} - To Set padding
     */
    setPadding(isRtlChange?: boolean): void;
    private setClientX;
    private getPointX;
    private onTouchScroll;
    private pointerUpHandler;
    private addEventListener;
    private destroy;
    private removeEventListener;
}
