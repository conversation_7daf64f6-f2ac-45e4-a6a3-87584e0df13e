@import 'ej2-base/styles/definition/fabric.scss';
@import '../spreadsheet-ribbon/fabric-definition.scss';
@import 'ej2-buttons/styles/button/fabric-definition.scss';
@import 'ej2-buttons/styles/check-box/fabric-definition.scss';
@import 'ej2-buttons/styles/radio-button/fabric-definition.scss';
@import 'ej2-buttons/styles/switch/fabric-definition.scss';
@import 'ej2-navigations/styles/toolbar/fabric-definition.scss';
@import 'ej2-navigations/styles/tab/fabric-definition.scss';
@import 'ej2-navigations/styles/context-menu/fabric-definition.scss';
@import 'ej2-navigations/styles/menu/fabric-definition.scss';
@import 'ej2-navigations/styles/treeview/fabric-definition.scss';
@import 'ej2-grids/styles/excel-filter/fabric-definition.scss';
@import 'ej2-calendars/styles/datepicker/fabric-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/fabric-definition.scss';
@import 'ej2-inputs/styles/color-picker/fabric-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/fabric-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/fabric-definition.scss';
@import 'ej2-dropdowns/styles/list-box/fabric-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/fabric-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/fabric-definition.scss';
@import 'fabric-definition.scss';
@import 'icons/fabric.scss';
@import 'all.scss';
