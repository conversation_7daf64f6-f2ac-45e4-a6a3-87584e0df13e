/**
 * @hidden
 */
export declare enum CommonErrors {
    na = 0,
    value = 1,
    ref = 2,
    divzero = 3,
    num = 4,
    name = 5,
    null = 6
}
/**
 * @hidden
 */
export declare enum FormulasErrorsStrings {
    operators_cannot_start_with_expression = 0,
    reservedWord_And = 1,
    reservedWord_Xor = 2,
    reservedWord_If = 3,
    number_contains_2_decimal_points = 4,
    reservedWord_Else = 5,
    reservedWord_Not = 6,
    invalid_char_in_number = 7,
    invalid_characters_following_with_operator = 6,
    mismatched_parentheses = 8,
    unknown_formula_name = 9,
    requires_a_single_argument = 10,
    requires_3_args = 11,
    invalid_Math_argument = 12,
    requires_2_args = 13,
    bad_index = 14,
    too_complex = 15,
    circular_reference = 16,
    missing_formula = 17,
    improper_formula = 18,
    invalid_expression = 19,
    cell_empty = 20,
    bad_formula = 21,
    empty_expression = 22,
    virtual_mode_required = 23,
    mismatched_tics = 24,
    wrong_number_arguments = 25,
    invalid_arguments = 26,
    iterations_do_not_converge = 27,
    calculation_overflow = 29,
    already_registered = 28,
    missing_sheet = 30,
    cannot_parse = 31,
    expression_cannot_end_with_an_operator = 32,
    spill = 33,
    div = 34
}
/**
 * @hidden
 */
export declare enum ExcelFileFormats {
    xlsx = "xlsx",
    xlsm = "xlsm",
    xlsb = "xlsb",
    xltx = "xltx",
    xltm = "xltm",
    xls = "xls",
    xml = "xml",
    xlam = "xlam",
    xla = "xla",
    xlw = "xlw",
    xlr = "xlr",
    prn = "prn",
    txt = "txt",
    csv = "csv",
    dif = "dif",
    slk = "slk"
}
