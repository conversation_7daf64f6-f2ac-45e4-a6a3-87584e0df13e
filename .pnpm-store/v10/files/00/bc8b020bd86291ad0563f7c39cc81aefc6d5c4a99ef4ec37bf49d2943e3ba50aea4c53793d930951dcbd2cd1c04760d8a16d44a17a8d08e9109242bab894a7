{"_from": "@syncfusion/ej2-spreadsheet@*", "_id": "@syncfusion/ej2-spreadsheet@23.1.42", "_inBundle": false, "_integrity": "sha512-VEjAjL3/83zfK+07B7OFBqT053eNae9hNq9dT+S4S9JkgzWUpnb1w/iwUP15/OKcPMhi6MS96qyfQ3iJMSiy+w==", "_location": "/@syncfusion/ej2-spreadsheet", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-spreadsheet@*", "name": "@syncfusion/ej2-spreadsheet", "escapedName": "@syncfusion%2fej2-spreadsheet", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-spreadsheet", "/@syncfusion/ej2-react-spreadsheet", "/@syncfusion/ej2-vue-spreadsheet"], "_resolved": "https://nexus.syncfusion.com/repository/ej2-hotfix-new/@syncfusion/ej2-spreadsheet/-/ej2-spreadsheet-23.1.42.tgz", "_shasum": "4221a6422327dd0d0f96d2b91ec28af06a25112b", "_spec": "@syncfusion/ej2-spreadsheet@*", "_where": "/jenkins/workspace/elease-automation_release_23.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~23.1.41", "@syncfusion/ej2-charts": "~23.1.44", "@syncfusion/ej2-dropdowns": "~23.1.44", "@syncfusion/ej2-grids": "~23.1.44", "@syncfusion/ej2-navigations": "~23.1.44"}, "deprecated": false, "description": "Feature-rich JavaScript Spreadsheet (Excel) control with built-in support for selection, editing, formatting, importing and exporting to Excel", "devDependencies": {}, "es2015": "./dist/es6/ej2-spreadsheet.es5.js", "keywords": ["ej2", "syncfusion", "spreadsheet", "excel", "workbook", "worksheet", "xlsx", "xlsb", "csv", "grid", "data", "excel-like", "excel-style", "editor", "excel-editor", "excel-viewer", "sheets", "formulas", "sorting", "filtering", "formatting", "virtual-scrolling", "edit-cell", "editable-table", "javascript", "typescript"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-spreadsheet.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-spreadsheet", "repository": {"type": "git", "url": "https://github.com/syncfusion/ej2-javascript-ui-controls/tree/master/controls/spreadsheet"}, "typings": "index.d.ts", "version": "23.1.44", "sideEffects": true, "homepage": "https://www.syncfusion.com/javascript-ui-controls"}