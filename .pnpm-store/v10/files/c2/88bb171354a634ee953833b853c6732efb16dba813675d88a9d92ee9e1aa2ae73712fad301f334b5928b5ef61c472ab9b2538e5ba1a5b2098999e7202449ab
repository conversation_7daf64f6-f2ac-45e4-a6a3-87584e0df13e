/* stylelint-disable property-no-vendor-prefix */
.e-ddl.e-popup {
  border: 0;
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.4);
  margin-top: 1px;
}
.e-ddl.e-popup .e-input-group input {
  line-height: 15px;
}

.e-ddl.e-popup .e-input-group {
  display: -ms-flexbox;
  display: flex;
  width: auto;
}

.e-popup.e-ddl .e-dropdownbase {
  min-height: 26px;
}

.e-ddl.e-popup .e-filter-parent {
  border-left-width: 0;
  border-right-width: 0;
}

.e-bigger .e-popup.e-ddl-device-filter {
  margin-top: 0;
}

.e-bigger .e-ddl.e-popup .e-list-item, .e-bigger .e-ddl.e-popup .e-list-group-item, .e-bigger .e-ddl.e-popup .e-fixed-head {
  font-size: 15px;
  line-height: 45px;
  padding-left: 0;
  text-indent: 16px;
}

.e-bigger .e-ddl.e-popup .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger .e-ddl.e-popup .e-input-group {
  padding: 4px 0;
}

.e-bigger .e-ddl.e-popup .e-input-group input, .e-bigger .e-ddl.e-popup .e-input-group input.e-input {
  height: 38px;
}

.e-input-group:not(.e-disabled) .e-control.e-dropdownlist ~ .e-ddl-icon:active,
.e-input-group:not(.e-disabled) .e-control.e-dropdownlist ~ .e-ddl-icon:hover,
.e-ddl.e-popup .e-input-group:not(.e-disabled) .e-clear-icon:active,
.e-ddl.e-popup .e-input-group:not(.e-disabled) .e-clear-icon:hover {
  background: transparent;
  color: #333;
}

.e-bigger.e-small .e-ddl.e-popup .e-list-item, .e-bigger.e-small .e-ddl.e-popup .e-list-group-item, .e-bigger.e-small .e-ddl.e-popup .e-fixed-head {
  font-size: 14px;
  line-height: 40px;
  padding-left: 0;
  text-indent: 16px;
}

.e-bigger.e-small .e-ddl.e-popup .e-dd-group .e-list-item {
  padding-left: 4px;
}

.e-bigger.e-small .e-ddl.e-popup .e-input-group {
  padding: 0;
}

.e-bigger.e-small .e-ddl.e-popup .e-input-group input, .e-bigger.e-small .e-ddl.e-popup .e-input-group input.e-input {
  height: 34px;
}

/* stylelint-disable property-no-vendor-prefix */
@keyframes material-spinner-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes fabric-spinner-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* !componenticons */
.e-icon-check::before {
  content: "\e7ff";
}

.e-grid .e-group-animate .e-drag.e-icon-drag::before,
.e-grid-menu .e-group-animate .e-drag.e-icon-drag::before {
  content: "\e330";
}
.e-grid .e-group-animate .e-nextgroup.e-icon-next::before,
.e-grid-menu .e-group-animate .e-nextgroup.e-icon-next::before {
  content: "\ebe0";
}
.e-grid .e-icon-ascending::before,
.e-grid-menu .e-icon-ascending::before {
  content: "\e840";
}
.e-grid .e-icon-descending::before,
.e-grid-menu .e-icon-descending::before {
  content: "\e83f";
}
.e-grid .e-icon-hide::before,
.e-grid-menu .e-icon-hide::before {
  content: "\e825";
}
.e-grid .e-ungroupbutton.e-icon-hide::before,
.e-grid-menu .e-ungroupbutton.e-icon-hide::before {
  content: "\e953";
}
.e-grid .e-icon-rowselect::before,
.e-grid-menu .e-icon-rowselect::before {
  content: "\e930";
}
.e-grid .e-icon-sortdirect::before,
.e-grid-menu .e-icon-sortdirect::before {
  content: "\e890";
}
.e-grid .e-icon-gdownarrow::before,
.e-grid-menu .e-icon-gdownarrow::before {
  content: "\e85e";
}
.e-grid .e-icon-grightarrow::before,
.e-grid-menu .e-icon-grightarrow::before {
  content: "\e84f";
}
.e-grid .e-icon-filter::before,
.e-grid .e-icon-filter.e-filtered::before,
.e-grid-menu .e-icon-filter::before,
.e-grid-menu .e-icon-filter.e-filtered::before {
  content: "\e21c";
}
.e-grid .e-resfilter-icon::before,
.e-grid-menu .e-resfilter-icon::before {
  content: "\e250";
}
.e-grid .e-ressort-icon::before,
.e-grid-menu .e-ressort-icon::before {
  content: "\e612";
}
.e-grid .e-excl-filter-icon::before,
.e-grid-menu .e-excl-filter-icon::before {
  content: "\e250";
}
.e-grid .e-excl-filter-icon.e-filtered::before,
.e-grid-menu .e-excl-filter-icon.e-filtered::before {
  content: "\e247";
}
.e-grid.e-rtl .e-icon-grightarrow::before,
.e-grid-menu.e-rtl .e-icon-grightarrow::before {
  content: "\e84f";
}
.e-grid .e-icon-group::before,
.e-grid-menu .e-icon-group::before {
  content: "\e926";
}
.e-grid .e-icon-ungroup::before,
.e-grid-menu .e-icon-ungroup::before {
  content: "\e926";
}
.e-grid .e-icon-reorderuparrow::before,
.e-grid-menu .e-icon-reorderuparrow::before {
  content: "\e918";
}
.e-grid .e-icon-reorderdownarrow::before,
.e-grid-menu .e-icon-reorderdownarrow::before {
  content: "\e919";
}
.e-grid .e-print::before,
.e-grid-menu .e-print::before {
  content: "\e7df";
}
.e-grid .e-add::before,
.e-grid-menu .e-add::before {
  content: "\e7d5";
}
.e-grid .e-resback-icon::before,
.e-grid-menu .e-resback-icon::before {
  content: "\e610";
}
.e-grid .e-wordexport::before,
.e-grid-menu .e-wordexport::before {
  content: "\e7b0";
}
.e-grid .e-pdfexport::before,
.e-grid-menu .e-pdfexport::before {
  content: "\e240";
}
.e-grid .e-csvexport::before,
.e-grid-menu .e-csvexport::before {
  content: "\e241";
}
.e-grid .e-excelexport::before,
.e-grid-menu .e-excelexport::before {
  content: "\e242";
}
.e-grid .e-edit::before,
.e-grid-menu .e-edit::before {
  content: "\e97c";
}
.e-grid .e-columnmenu::before,
.e-grid .e-columnmenu.e-filtered::before,
.e-grid-menu .e-columnmenu::before,
.e-grid-menu .e-columnmenu.e-filtered::before {
  content: "\e984";
}
.e-grid .e-delete::before,
.e-grid-menu .e-delete::before {
  content: "\e965";
}
.e-grid .e-cancel::before,
.e-grid-menu .e-cancel::before {
  content: "\e953";
}
.e-grid .e-copy::before,
.e-grid-menu .e-copy::before {
  content: "\e70a";
}
.e-grid .e-save::before,
.e-grid-menu .e-save::before {
  content: "\e954";
}
.e-grid .e-update::before,
.e-grid-menu .e-update::before {
  content: "\e735";
}
.e-grid .e-search-icon::before,
.e-grid-menu .e-search-icon::before {
  content: "\e97d";
}
.e-grid .e-cancel-icon::before,
.e-grid-menu .e-cancel-icon::before {
  content: "\e825";
}
.e-grid .e-columnchooserdiv::before,
.e-grid-menu .e-columnchooserdiv::before {
  content: "\e714";
}
.e-grid .e-ccsearch-icon::before,
.e-grid-menu .e-ccsearch-icon::before {
  content: "\e97d";
}
.e-grid .e-columnchooser::before,
.e-grid-menu .e-columnchooser::before {
  content: "\e21e";
}
.e-grid .e-columnchooser-btn::before,
.e-grid-menu .e-columnchooser-btn::before {
  content: "\e969";
}
.e-grid .e-cc-icon::before,
.e-grid-menu .e-cc-icon::before {
  content: "\e82e";
}
.e-grid .e-icon-rowdragicon::before,
.e-grid-menu .e-icon-rowdragicon::before {
  content: "\e330";
}
.e-grid.e-rtl .e-icon-rowdragicon::before,
.e-grid-menu.e-rtl .e-icon-rowdragicon::before {
  content: "\e330";
}
.e-grid .e-ccsearch-icon.e-cc-cancel::before,
.e-grid .e-chkcancel-icon::before,
.e-grid-menu .e-ccsearch-icon.e-cc-cancel::before,
.e-grid-menu .e-chkcancel-icon::before {
  content: "\e7a7";
}

.e-responsive-dialog .e-resfilterback::before {
  content: "\e610";
}
.e-responsive-dialog .e-filterset::before {
  content: "\e21c";
}
.e-responsive-dialog .e-search-icon::before {
  content: "\e97d";
}
.e-responsive-dialog .e-chkcancel-icon::before {
  content: "\e7a7";
}
.e-responsive-dialog .e-icon-filter-clear::before {
  content: "\e247";
}

.e-row-responsive-filter .e-dlg-closeicon-btn .e-icon-dlg-close::before {
  content: "\e610";
}

/*! Grid layout */
.e-disableuserselect {
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

/*! Blazor column menu custom css */
.e-contextmenu-container.e-sfcontextmenu.e-hide-menu {
  visibility: hidden;
}

.e-emptyicon {
  opacity: 0;
}

.e-device .e-flmenu-valuediv {
  padding: 24px 0 0;
}

.e-bigger .e-grid,
.e-grid.e-bigger {
  font-size: 14px;
  /* stylelint-disable */
}
.e-bigger .e-grid .e-gridheader thead .e-icons:not(.e-check):not(.e-stop),
.e-grid.e-bigger .e-gridheader thead .e-icons:not(.e-check):not(.e-stop) {
  font-size: 10px;
}
.e-bigger .e-grid .e-row .e-icon-rowdragicon::before,
.e-grid.e-bigger .e-row .e-icon-rowdragicon::before {
  font-size: 10px;
}
.e-bigger .e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper,
.e-grid.e-bigger .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper {
  padding: 0 1.5px;
}
.e-bigger .e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-input.e-search,
.e-grid.e-bigger .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-input.e-search {
  width: 100%;
}
.e-bigger .e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-input-group-icon.e-icons.e-sicon.e-clear-icon,
.e-grid.e-bigger .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-input-group-icon.e-icons.e-sicon.e-clear-icon {
  margin: 0;
}
.e-bigger .e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon::before,
.e-grid.e-bigger .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon::before {
  font-size: 17px;
}
.e-bigger .e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-search .e-search-icon,
.e-grid.e-bigger .e-res-toolbar .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-search .e-search-icon {
  padding-left: 0;
}
.e-bigger .e-grid .e-toolbar-items .e-toolbar-item.e-search-wrapper,
.e-grid.e-bigger .e-toolbar-items .e-toolbar-item.e-search-wrapper {
  padding-bottom: 5px;
  padding-top: 5px;
}
.e-bigger .e-grid .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-search .e-search-icon,
.e-grid.e-bigger .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-search .e-search-icon {
  min-width: 38px;
}
.e-bigger .e-grid .e-toolbar .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon,
.e-grid.e-bigger .e-toolbar .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon {
  margin: 0;
  min-width: 38px;
  padding: 0;
}
.e-bigger .e-grid .e-toolbar .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon::before,
.e-grid.e-bigger .e-toolbar .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon::before {
  font-size: 12px;
}
.e-bigger .e-grid.e-rtl .e-toolbar .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon,
.e-grid.e-bigger.e-rtl .e-toolbar .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon {
  margin: 0;
  min-width: 38px;
  padding: 0;
}
.e-bigger .e-grid .e-toolbar .e-toolbar-items .e-tbar-btn.e-btn .e-btn-icon.e-icons,
.e-grid.e-bigger .e-toolbar .e-toolbar-items .e-tbar-btn.e-btn .e-btn-icon.e-icons {
  font-size: 18px;
}
.e-bigger .e-grid .e-flmenu-valuediv,
.e-grid.e-bigger .e-flmenu-valuediv {
  padding: 24px 0 0;
}
.e-bigger .e-grid .e-headercell,
.e-grid.e-bigger .e-headercell {
  height: 45px;
  padding: 0 10px 0;
}
.e-bigger .e-grid .e-headercelldiv,
.e-grid.e-bigger .e-headercelldiv {
  font-size: 14px;
  height: 29px;
  line-height: 29px;
}
.e-bigger .e-grid .e-headercell,
.e-bigger .e-grid .e-detailheadercell,
.e-grid.e-bigger .e-headercell,
.e-grid.e-bigger .e-detailheadercell {
  font-size: 14px;
}
.e-bigger .e-grid .e-icons,
.e-grid.e-bigger .e-icons {
  font-size: 14px;
}
.e-bigger .e-grid .e-gridheader thead .e-icons,
.e-grid.e-bigger .e-gridheader thead .e-icons {
  font-size: 14px;
}
.e-bigger .e-grid .e-icon-gdownarrow,
.e-grid.e-bigger .e-icon-gdownarrow {
  font-size: 14px;
}
.e-bigger .e-grid .e-icon-grightarrow,
.e-grid.e-bigger .e-icon-grightarrow {
  font-size: 14px;
}
.e-bigger .e-grid .e-toolbar .e-tbar-btn.e-btn .e-btn-icon.e-icons.e-columnchooser-btn,
.e-grid.e-bigger .e-toolbar .e-tbar-btn.e-btn .e-btn-icon.e-icons.e-columnchooser-btn {
  font-size: 12px;
}
.e-bigger .e-grid .e-gridheader tr th:last-child,
.e-grid.e-bigger .e-gridheader tr th:last-child {
  padding-right: 13px;
}
.e-bigger .e-grid .e-gridheader tr th:last-child.e-filterbarcell,
.e-grid.e-bigger .e-gridheader tr th:last-child.e-filterbarcell {
  padding-left: 10px;
}
.e-bigger .e-grid .e-gridheader tr th:first-child,
.e-grid.e-bigger .e-gridheader tr th:first-child {
  padding-left: 13px;
}
.e-bigger .e-grid .e-gridheader tr th:first-child.e-filterbarcell,
.e-grid.e-bigger .e-gridheader tr th:first-child.e-filterbarcell {
  padding-left: 10px;
}
.e-bigger .e-grid .e-headercelldiv,
.e-grid.e-bigger .e-headercelldiv {
  font-size: 15px;
}
.e-bigger .e-grid .e-rowcell,
.e-bigger .e-grid .e-summarycell,
.e-bigger .e-grid .e-groupcaption,
.e-grid.e-bigger .e-rowcell,
.e-grid.e-bigger .e-summarycell,
.e-grid.e-bigger .e-groupcaption {
  font-size: 14px;
  padding: 9px 10px;
}
.e-bigger .e-grid .e-summarycell.e-lastrowcell,
.e-grid.e-bigger .e-summarycell.e-lastrowcell {
  border-bottom-width: 1px;
}
.e-bigger .e-grid .e-rowcell:first-child,
.e-bigger .e-grid .e-summarycell:first-child,
.e-grid.e-bigger .e-rowcell:first-child,
.e-grid.e-bigger .e-summarycell:first-child {
  padding-left: 13px;
}
.e-bigger .e-grid .e-rowcell:last-child,
.e-bigger .e-grid .e-summarycell:last-child,
.e-grid.e-bigger .e-rowcell:last-child,
.e-grid.e-bigger .e-summarycell:last-child {
  padding-right: 13px;
}
.e-bigger .e-grid .e-filterbarcell,
.e-bigger .e-grid .e-filterbarcelldisabled,
.e-grid.e-bigger .e-filterbarcell,
.e-grid.e-bigger .e-filterbarcelldisabled {
  height: 45px;
  padding: 0 10px;
}
.e-bigger .e-grid .e-filterbarcell input,
.e-grid.e-bigger .e-filterbarcell input {
  height: 40px;
}
.e-bigger .e-grid .e-ftrchk,
.e-grid.e-bigger .e-ftrchk {
  padding-bottom: 12px;
  padding-top: 12px;
}
.e-bigger .e-grid .e-columnmenu,
.e-grid.e-bigger .e-columnmenu {
  bottom: 35px;
  top: 37px;
}
.e-bigger .e-grid.e-device.e-noselect,
.e-grid.e-bigger.e-device.e-noselect {
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.e-bigger .e-grid .e-dialog.e-checkboxfilter,
.e-bigger .e-grid .e-dialog.e-flmenu,
.e-grid.e-bigger .e-dialog.e-checkboxfilter,
.e-grid.e-bigger .e-dialog.e-flmenu {
  min-width: 260px;
}
.e-bigger .e-grid .e-dialog.e-checkboxfilter,
.e-grid.e-bigger .e-dialog.e-checkboxfilter {
  min-height: 370px;
}
.e-bigger .e-grid .e-checkboxfilter .e-dlg-content,
.e-grid.e-bigger .e-checkboxfilter .e-dlg-content {
  padding-left: 19.25px;
}
.e-bigger .e-grid .e-checkboxfilter .e-ftrchk, .e-bigger .e-grid .e-checkboxfilter .e-searchbox,
.e-grid.e-bigger .e-checkboxfilter .e-ftrchk,
.e-grid.e-bigger .e-checkboxfilter .e-searchbox {
  padding-left: 12px;
}
.e-bigger .e-grid.e-rtl .e-checkboxfilter .e-dlg-content,
.e-grid.e-bigger.e-rtl .e-checkboxfilter .e-dlg-content {
  padding-left: 31.25px;
  padding-right: 19.25px;
}
.e-bigger .e-grid.e-rtl .e-checkboxfilter .e-ftrchk, .e-bigger .e-grid.e-rtl .e-checkboxfilter .e-searchbox,
.e-grid.e-bigger.e-rtl .e-checkboxfilter .e-ftrchk,
.e-grid.e-bigger.e-rtl .e-checkboxfilter .e-searchbox {
  padding-left: 0;
  padding-right: 12px;
}
.e-bigger .e-grid .e-filtermenudiv,
.e-grid.e-bigger .e-filtermenudiv {
  margin: -26px -7px;
}
.e-bigger .e-grid .e-sortfilterdiv,
.e-grid.e-bigger .e-sortfilterdiv {
  margin: -21px 6px;
}
.e-bigger .e-grid .e-gridheader .e-fltr-icon .e-sortfilterdiv,
.e-grid.e-bigger .e-gridheader .e-fltr-icon .e-sortfilterdiv {
  margin: -22px 20px 0 0;
}
.e-bigger .e-grid.e-rtl .e-gridheader .e-headercell .e-headercelldiv.e-headerchkcelldiv,
.e-grid.e-bigger.e-rtl .e-gridheader .e-headercell .e-headercelldiv.e-headerchkcelldiv {
  padding: 0 0 0 3px;
}
.e-bigger .e-grid .e-gridheader .e-columnheader.e-wrap .e-sortfilterdiv,
.e-bigger .e-grid .e-wrap .e-sortfilterdiv,
.e-grid.e-bigger .e-gridheader .e-columnheader.e-wrap .e-sortfilterdiv,
.e-grid.e-bigger .e-wrap .e-sortfilterdiv {
  margin: -19px 6px;
}
.e-bigger .e-grid .e-gridheader .e-columnheader.e-wrap .e-rightalign .e-sortfilterdiv .e-wrap .e-rightalign .e-sortfilterdiv,
.e-grid.e-bigger .e-gridheader .e-columnheader.e-wrap .e-rightalign .e-sortfilterdiv .e-wrap .e-rightalign .e-sortfilterdiv {
  margin: -19px -4px;
}
.e-bigger .e-grid .e-gridheader .e-columnheader.e-wrap .e-fltr-icon .e-sortfilterdiv .e-wrap .e-fltr-icon .e-sortfilterdiv,
.e-grid.e-bigger .e-gridheader .e-columnheader.e-wrap .e-fltr-icon .e-sortfilterdiv .e-wrap .e-fltr-icon .e-sortfilterdiv {
  margin: -17px 20px;
}
.e-bigger .e-grid .e-gridheader .e-columnheader.e-wrap .e-rightalign.e-fltr-icon .e-sortfilterdiv,
.e-bigger .e-grid .e-wrap .e-rightalign.e-fltr-icon .e-sortfilterdiv,
.e-grid.e-bigger .e-gridheader .e-columnheader.e-wrap .e-rightalign.e-fltr-icon .e-sortfilterdiv,
.e-grid.e-bigger .e-wrap .e-rightalign.e-fltr-icon .e-sortfilterdiv {
  margin: -17px 0;
}
.e-bigger .e-grid.e-wrap .e-rowcell, .e-bigger .e-grid.e-wrap .e-columnheader .e-stackedheadercelldiv, .e-bigger .e-grid.e-wrap .e-columnheader .e-headercelldiv,
.e-grid.e-bigger.e-wrap .e-rowcell,
.e-grid.e-bigger.e-wrap .e-columnheader .e-stackedheadercelldiv,
.e-grid.e-bigger.e-wrap .e-columnheader .e-headercelldiv {
  line-height: 18px;
}
.e-bigger .e-grid .e-gridheader .e-rightalign .e-sortfilterdiv,
.e-grid.e-bigger .e-gridheader .e-rightalign .e-sortfilterdiv {
  margin: -21px -4px;
}
.e-bigger .e-grid .e-gridheader .e-rightalign.e-fltr-icon .e-sortnumber,
.e-grid.e-bigger .e-gridheader .e-rightalign.e-fltr-icon .e-sortnumber {
  float: left;
  margin: 7px 0 0;
}
.e-bigger .e-grid .e-gridheader .e-rightalign .e-sortnumber,
.e-grid.e-bigger .e-gridheader .e-rightalign .e-sortnumber {
  float: left;
  margin: 7px 0 0;
}
.e-bigger .e-grid .e-sortnumber,
.e-grid.e-bigger .e-sortnumber {
  border-radius: 55%;
  display: inline-block;
  float: right;
  font-size: 9px;
  height: 15px;
  line-height: 15px;
  margin: 7px -2px 0;
  text-align: center;
  width: 15px;
}
.e-bigger .e-grid .e-gridheader .e-fltr-icon .e-sortnumber,
.e-grid.e-bigger .e-gridheader .e-fltr-icon .e-sortnumber {
  margin: 7px -2px 0;
}
.e-bigger .e-grid .e-gridheader .e-rightalign.e-fltr-icon .e-sortfilterdiv,
.e-grid.e-bigger .e-gridheader .e-rightalign.e-fltr-icon .e-sortfilterdiv {
  margin: -22px 11px 0 0;
}
.e-bigger .e-grid.e-rtl .e-headercell .e-sortfilterdiv, .e-bigger .e-grid.e-rtl .e-detailheadercell .e-sortfilterdiv,
.e-grid.e-bigger.e-rtl .e-headercell .e-sortfilterdiv,
.e-grid.e-bigger.e-rtl .e-detailheadercell .e-sortfilterdiv {
  margin: -22px 0 0 13px;
}
.e-bigger .e-grid.e-rtl .e-gridheader .e-rightalign .e-sortfilterdiv,
.e-grid.e-bigger.e-rtl .e-gridheader .e-rightalign .e-sortfilterdiv {
  margin: -22px 0 0 -6px;
}
.e-bigger .e-grid.e-rtl .e-gridheader .e-fltr-icon .e-sortfilterdiv,
.e-grid.e-bigger.e-rtl .e-gridheader .e-fltr-icon .e-sortfilterdiv {
  margin: -22px 0 0 10px;
}
.e-bigger .e-grid.e-rtl .e-gridheader .e-rightalign.e-fltr-icon .e-sortfilterdiv,
.e-grid.e-bigger.e-rtl .e-gridheader .e-rightalign.e-fltr-icon .e-sortfilterdiv {
  margin: -22px 0 0 12px;
}
.e-bigger .e-grid.e-rtl .e-filtermenudiv,
.e-grid.e-bigger.e-rtl .e-filtermenudiv {
  margin: -27px 0 -26px -5px;
}
.e-bigger .e-grid.e-rtl .e-rightalign .e-filtermenudiv,
.e-grid.e-bigger.e-rtl .e-rightalign .e-filtermenudiv {
  margin: -27px 0 -26px -5px;
}
.e-bigger .e-grid.e-rtl .e-headercell .e-headercelldiv .e-sortnumber, .e-bigger .e-grid.e-rtl .e-detailheadercell .e-headercelldiv .e-sortnumber,
.e-grid.e-bigger.e-rtl .e-headercell .e-headercelldiv .e-sortnumber,
.e-grid.e-bigger.e-rtl .e-detailheadercell .e-headercelldiv .e-sortnumber {
  margin: 7px 0 0 3px;
}
.e-bigger .e-grid.e-rtl .e-headercell.e-rightalign .e-headercelldiv .e-sortnumber, .e-bigger .e-grid.e-rtl .e-detailheadercell.e-rightalign .e-headercelldiv .e-sortnumber,
.e-grid.e-bigger.e-rtl .e-headercell.e-rightalign .e-headercelldiv .e-sortnumber,
.e-grid.e-bigger.e-rtl .e-detailheadercell.e-rightalign .e-headercelldiv .e-sortnumber {
  margin: 7px 0 0 3px;
}
.e-bigger .e-grid.e-rtl .e-headercell.e-fltr-icon .e-headercelldiv .e-sortnumber,
.e-grid.e-bigger.e-rtl .e-headercell.e-fltr-icon .e-headercelldiv .e-sortnumber {
  margin: 7px 0 0 3px;
}
.e-bigger .e-grid .e-rowcell,
.e-bigger .e-grid .e-summarycell,
.e-bigger .e-grid .e-emptyrow td,
.e-bigger .e-grid .e-frozencontent table tr td:first-child:empty,
.e-bigger .e-grid .e-movablecontent table tr td:first-child:empty, .e-bigger .e-grid:not(.e-grid-min-height) .e-gridcontent tr td:not(.e-indentcell):first-child:empty, .e-bigger .e-grid:not(.e-grid-min-height) .e-gridcontent tr.e-row .e-rowcell:empty,
.e-grid.e-bigger .e-rowcell,
.e-grid.e-bigger .e-summarycell,
.e-grid.e-bigger .e-emptyrow td,
.e-grid.e-bigger .e-frozencontent table tr td:first-child:empty,
.e-grid.e-bigger .e-movablecontent table tr td:first-child:empty,
.e-grid.e-bigger:not(.e-grid-min-height) .e-gridcontent tr td:not(.e-indentcell):first-child:empty,
.e-grid.e-bigger:not(.e-grid-min-height) .e-gridcontent tr.e-row .e-rowcell:empty {
  line-height: 26px;
}
.e-bigger .e-grid .e-xlsel-bottom-border.e-xlsel-top-border,
.e-grid.e-bigger .e-xlsel-bottom-border.e-xlsel-top-border {
  line-height: 22px;
}
.e-bigger .e-grid .e-xlsel-bottom-border,
.e-grid.e-bigger .e-xlsel-bottom-border {
  line-height: 24px;
}
.e-bigger .e-grid .e-filterbarcell input,
.e-grid.e-bigger .e-filterbarcell input {
  font-size: 14px;
}
.e-bigger .e-grid .e-groupdroparea,
.e-grid.e-bigger .e-groupdroparea {
  font-size: 21px;
  min-height: 74px;
  padding: 22px 24px;
}
.e-bigger .e-grid .e-cloneproperties,
.e-grid.e-bigger .e-cloneproperties {
  padding: 4px 5px;
}
.e-bigger .e-grid .e-cloneproperties.e-draganddrop,
.e-grid.e-bigger .e-cloneproperties.e-draganddrop {
  padding: 4px 5px;
}
.e-bigger .e-grid .e-headerclone,
.e-grid.e-bigger .e-headerclone {
  font-size: 14px;
}
.e-bigger .e-grid .e-groupdroparea.e-grouped,
.e-grid.e-bigger .e-groupdroparea.e-grouped {
  padding: 11px 11px 12px;
}
.e-bigger .e-grid .e-frozenheader table tr td.e-rowcell,
.e-grid.e-bigger .e-frozenheader table tr td.e-rowcell {
  height: 44px -6;
}
.e-bigger .e-grid .e-frozencontent table tr td:first-child:empty,
.e-bigger .e-grid .e-movablecontent table tr td:first-child:empty,
.e-bigger .e-grid .e-frozenhdrcont table tr td:first-child:empty, .e-bigger .e-grid:not(.e-grid-min-height) .e-gridcontent tr td:not(.e-indentcell):first-child:empty, .e-bigger .e-grid:not(.e-grid-min-height) .e-gridcontent tr.e-row .e-rowcell:empty,
.e-grid.e-bigger .e-frozencontent table tr td:first-child:empty,
.e-grid.e-bigger .e-movablecontent table tr td:first-child:empty,
.e-grid.e-bigger .e-frozenhdrcont table tr td:first-child:empty,
.e-grid.e-bigger:not(.e-grid-min-height) .e-gridcontent tr td:not(.e-indentcell):first-child:empty,
.e-grid.e-bigger:not(.e-grid-min-height) .e-gridcontent tr.e-row .e-rowcell:empty {
  height: 44px;
}
.e-bigger .e-grid.e-bigger .e-columnmenu,
.e-grid.e-bigger.e-bigger .e-columnmenu {
  bottom: 16px;
  margin: -24.5px -24px;
}
.e-bigger .e-grid.e-device .e-columnmenu,
.e-grid.e-bigger.e-device .e-columnmenu {
  margin: -26px -20px;
}
.e-bigger .e-grid .e-columnmenu,
.e-grid.e-bigger .e-columnmenu {
  right: 23px;
}
.e-bigger .e-grid .e-groupheadercell,
.e-bigger .e-grid .e-groupheadercell:hover,
.e-grid.e-bigger .e-groupheadercell,
.e-grid.e-bigger .e-groupheadercell:hover {
  border-radius: 0;
  height: 33px;
  margin: 9px 0 0 10px;
  padding: 0 10px;
  font-size: 14px;
}
.e-bigger .e-grid.e-device .e-groupheadercell,
.e-grid.e-bigger.e-device .e-groupheadercell {
  padding: 0 10px;
}
.e-bigger .e-grid .e-groupheadercell:hover,
.e-grid.e-bigger .e-groupheadercell:hover {
  padding: 0 10px;
}
.e-bigger .e-grid .e-gdclone,
.e-grid.e-bigger .e-gdclone {
  border-radius: 0;
  padding: 6px;
}
.e-bigger .e-grid .e-groupheadercell span,
.e-grid.e-bigger .e-groupheadercell span {
  height: 32px;
  line-height: 32px;
  padding: 0;
}
.e-bigger .e-grid .e-groupheadercell .e-ungroupbutton,
.e-grid.e-bigger .e-groupheadercell .e-ungroupbutton {
  font-size: 13px;
}
.e-bigger .e-grid .e-groupheadercell .e-grouptext,
.e-grid.e-bigger .e-groupheadercell .e-grouptext {
  line-height: 32px;
}
.e-bigger .e-grid .e-row .e-input-group .e-input.e-field,
.e-bigger .e-grid .e-row .e-input-focus .e-input.e-field,
.e-grid.e-bigger .e-row .e-input-group .e-input.e-field,
.e-grid.e-bigger .e-row .e-input-focus .e-input.e-field {
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 14px;
  padding-bottom: 0;
  padding-top: 0;
}
.e-bigger .e-grid.e-device .e-row .e-input-group .e-input.e-field, .e-bigger .e-grid.e-device .e-row .e-input-focus .e-input.e-field,
.e-grid.e-bigger.e-device .e-row .e-input-group .e-input.e-field,
.e-grid.e-bigger.e-device .e-row .e-input-focus .e-input.e-field {
  padding-bottom: 0;
  padding-top: 0;
}
.e-bigger .e-grid .e-row .e-input-group,
.e-grid.e-bigger .e-row .e-input-group {
  margin-bottom: 2px;
  vertical-align: middle;
}
.e-bigger .e-grid.e-device .e-headercell,
.e-grid.e-bigger.e-device .e-headercell {
  padding: 0 8px;
}
.e-bigger .e-grid.e-device .e-headercell:first-child,
.e-grid.e-bigger.e-device .e-headercell:first-child {
  padding: 0 8px 0 10px;
}
.e-bigger .e-grid.e-device .e-headercell:last-child,
.e-grid.e-bigger.e-device .e-headercell:last-child {
  padding: 0 10px 0 8px;
}
.e-bigger .e-grid.e-device .e-groupheadercell span,
.e-grid.e-bigger.e-device .e-groupheadercell span {
  line-height: 30px;
  padding: 0;
}
.e-bigger .e-grid.e-device .e-rowcell, .e-bigger .e-grid.e-device .e-summarycell,
.e-grid.e-bigger.e-device .e-rowcell,
.e-grid.e-bigger.e-device .e-summarycell {
  padding: 7px 8px;
}
.e-bigger .e-grid:not(.e-row-responsive).e-device .e-rowcell:first-child, .e-bigger .e-grid:not(.e-row-responsive).e-device .e-summarycell:first-child,
.e-grid.e-bigger:not(.e-row-responsive).e-device .e-rowcell:first-child,
.e-grid.e-bigger:not(.e-row-responsive).e-device .e-summarycell:first-child {
  padding: 7px 8px 7px 10px;
}
.e-bigger .e-grid:not(.e-row-responsive).e-device .e-rowcell:last-child, .e-bigger .e-grid:not(.e-row-responsive).e-device .e-summarycell:last-child,
.e-grid.e-bigger:not(.e-row-responsive).e-device .e-rowcell:last-child,
.e-grid.e-bigger:not(.e-row-responsive).e-device .e-summarycell:last-child {
  padding: 7px 10px 7px 8px;
}
.e-bigger .e-grid.e-device .e-filterbarcell,
.e-grid.e-bigger.e-device .e-filterbarcell {
  padding: 3px 8px 2px 8px;
}
.e-bigger .e-grid.e-device .e-filterbarcell:first-child,
.e-grid.e-bigger.e-device .e-filterbarcell:first-child {
  padding: 3px 8px 2px 10px;
}
.e-bigger .e-grid.e-device .e-filterbarcell:last-child,
.e-grid.e-bigger.e-device .e-filterbarcell:last-child {
  padding: 3px 10px 2px 8px;
}
.e-bigger .e-grid.e-device .e-groupheadercell .e-ungroupbutton,
.e-grid.e-bigger.e-device .e-groupheadercell .e-ungroupbutton {
  line-height: 31px;
}
.e-bigger .e-grid.e-device .e-normaledit .e-rowcell,
.e-grid.e-bigger.e-device .e-normaledit .e-rowcell {
  padding-bottom: 0;
  padding-top: 0;
}
.e-bigger .e-grid.e-device .e-editedbatchcell.e-rowcell,
.e-grid.e-bigger.e-device .e-editedbatchcell.e-rowcell {
  padding-bottom: 0;
  padding-top: 0;
}
.e-bigger .e-grid .e-unboundcell,
.e-bigger .e-grid .e-editedrow .e-normaledit .e-unboundcell,
.e-bigger .e-grid .e-addedrow .e-normaledit .e-unboundcell,
.e-grid.e-bigger .e-unboundcell,
.e-grid.e-bigger .e-editedrow .e-normaledit .e-unboundcell,
.e-grid.e-bigger .e-addedrow .e-normaledit .e-unboundcell {
  padding-bottom: 6px;
  padding-top: 6px;
}
.e-bigger .e-grid .e-grouptext,
.e-grid.e-bigger .e-grouptext {
  margin-right: 10px;
  width: 76px;
}
.e-bigger .e-grid .e-groupsort,
.e-bigger .e-grid span.e-ungroupbutton,
.e-bigger .e-grid .e-toggleungroup,
.e-grid.e-bigger .e-groupsort,
.e-grid.e-bigger span.e-ungroupbutton,
.e-grid.e-bigger .e-toggleungroup {
  margin-left: 0;
  margin-top: 0;
}
.e-bigger .e-grid span.e-ungroupbutton.e-icons,
.e-grid.e-bigger span.e-ungroupbutton.e-icons {
  font-size: 13px;
  margin-left: 0;
  padding-top: 0;
  margin-top: 0;
  margin-right: 0;
}
.e-bigger .e-grid.e-device span.e-ungroupbutton.e-icons,
.e-grid.e-bigger.e-device span.e-ungroupbutton.e-icons {
  margin-top: 0;
}
.e-bigger .e-grid span.e-groupsort.e-icons,
.e-grid.e-bigger span.e-groupsort.e-icons {
  margin-right: 10px;
  margin-top: 0;
}
.e-bigger .e-grid.e-device span.e-groupsort.e-icons,
.e-grid.e-bigger.e-device span.e-groupsort.e-icons {
  margin-top: 0;
}
.e-bigger .e-grid.e-rtl .e-groupheadercell, .e-bigger .e-grid.e-rtl .e-groupheadercell:hover,
.e-grid.e-bigger.e-rtl .e-groupheadercell,
.e-grid.e-bigger.e-rtl .e-groupheadercell:hover {
  margin: 8px 10px 0 0;
  padding: 0 10px;
}
.e-bigger .e-grid.e-rtl.e-device .e-groupheadercell,
.e-grid.e-bigger.e-rtl.e-device .e-groupheadercell {
  padding: 0 10px;
}
.e-bigger .e-grid.e-rtl span.e-ungroupbutton.e-icons,
.e-grid.e-bigger.e-rtl span.e-ungroupbutton.e-icons {
  margin-left: 0;
  margin-right: 8px;
  margin-top: 0;
  padding-left: 0;
}
.e-bigger .e-grid .e-groupcaption,
.e-grid.e-bigger .e-groupcaption {
  line-height: 27px;
}
.e-bigger .e-grid .e-ccdlg .e-dlg-content,
.e-grid.e-bigger .e-ccdlg .e-dlg-content {
  margin: 30px 0 20px;
  padding: 25px 25px 5px 12px;
}
.e-bigger .e-grid .e-ccdlg .e-ccul-ele,
.e-grid.e-bigger .e-ccdlg .e-ccul-ele {
  padding: 0;
}
.e-bigger .e-grid .e-ccdlg .e-cc-searchdiv,
.e-grid.e-bigger .e-ccdlg .e-cc-searchdiv {
  padding-left: 7px;
}
.e-bigger .e-grid .e-ccdlg .e-checkbox-wrapper.e-control.e-keyboard,
.e-grid.e-bigger .e-ccdlg .e-checkbox-wrapper.e-control.e-keyboard {
  padding-left: 13px;
}
.e-bigger .e-grid .e-ccdlg li.e-cclist,
.e-grid.e-bigger .e-ccdlg li.e-cclist {
  padding: 11px 0;
}
.e-bigger .e-grid .e-ccdlg .e-toolbar .e-ccdiv,
.e-grid.e-bigger .e-ccdlg .e-toolbar .e-ccdiv {
  margin-top: 0;
}
.e-bigger .e-grid .e-rowcell.e-frozen-right-border,
.e-bigger .e-grid .e-headercell.e-frozen-right-border,
.e-bigger .e-grid .e-filterbarcell.e-frozen-right-border,
.e-grid.e-bigger .e-rowcell.e-frozen-right-border,
.e-grid.e-bigger .e-headercell.e-frozen-right-border,
.e-grid.e-bigger .e-filterbarcell.e-frozen-right-border {
  border-right: 2px solid rgba(0, 120, 214, 0.6);
}
.e-bigger .e-grid .e-rowcell.e-frozen-left-border,
.e-bigger .e-grid .e-headercell.e-frozen-left-border,
.e-bigger .e-grid .e-filterbarcell.e-frozen-left-border,
.e-grid.e-bigger .e-rowcell.e-frozen-left-border,
.e-grid.e-bigger .e-headercell.e-frozen-left-border,
.e-grid.e-bigger .e-filterbarcell.e-frozen-left-border {
  border-left: 2px solid rgba(0, 120, 214, 0.6);
}
.e-bigger .e-grid .e-rowcell.e-freezeline,
.e-bigger .e-grid .e-gridheader .e-filterbarcell.e-freezeline,
.e-grid.e-bigger .e-rowcell.e-freezeline,
.e-grid.e-bigger .e-gridheader .e-filterbarcell.e-freezeline {
  position: relative;
}
.e-bigger .e-grid .e-rowcell .e-frozen-right-cursor,
.e-bigger .e-grid .e-rowcell .e-frozen-left-cursor,
.e-bigger .e-grid .e-gridheader .e-headercell .e-frozen-right-cursor,
.e-bigger .e-grid .e-gridheader .e-headercell .e-frozen-left-cursor,
.e-bigger .e-grid .e-gridheader .e-filterbarcell .e-frozen-right-cursor,
.e-bigger .e-grid .e-gridheader .e-filterbarcell .e-frozen-left-cursor,
.e-grid.e-bigger .e-rowcell .e-frozen-right-cursor,
.e-grid.e-bigger .e-rowcell .e-frozen-left-cursor,
.e-grid.e-bigger .e-gridheader .e-headercell .e-frozen-right-cursor,
.e-grid.e-bigger .e-gridheader .e-headercell .e-frozen-left-cursor,
.e-grid.e-bigger .e-gridheader .e-filterbarcell .e-frozen-right-cursor,
.e-grid.e-bigger .e-gridheader .e-filterbarcell .e-frozen-left-cursor {
  cursor: move;
  height: 100%;
  position: absolute;
  top: 0;
  width: 4px;
}
.e-bigger .e-grid .e-rowcell .e-frozen-left-cursor,
.e-bigger .e-grid .e-gridheader .e-headercell .e-frozen-left-cursor,
.e-bigger .e-grid .e-gridheader .e-filterbarcell .e-frozen-left-cursor,
.e-grid.e-bigger .e-rowcell .e-frozen-left-cursor,
.e-grid.e-bigger .e-gridheader .e-headercell .e-frozen-left-cursor,
.e-grid.e-bigger .e-gridheader .e-filterbarcell .e-frozen-left-cursor {
  right: 0;
}
.e-bigger .e-grid .e-rowcell .e-frozen-right-cursor,
.e-bigger .e-grid .e-gridheader .e-headercell .e-frozen-right-cursor,
.e-bigger .e-grid .e-gridheader .e-filterbarcell .e-frozen-right-cursor,
.e-grid.e-bigger .e-rowcell .e-frozen-right-cursor,
.e-grid.e-bigger .e-gridheader .e-headercell .e-frozen-right-cursor,
.e-grid.e-bigger .e-gridheader .e-filterbarcell .e-frozen-right-cursor {
  left: 0;
}
.e-bigger .e-grid .e-gridheader .e-headercell .e-frozen-left-cursor.e-frozen-resize-cursor,
.e-bigger .e-grid .e-gridheader .e-filterbarcell .e-frozen-left-cursor.e-frozen-resize-cursor,
.e-bigger .e-grid .e-rowcell .e-frozen-left-cursor.e-frozen-resize-cursor,
.e-grid.e-bigger .e-gridheader .e-headercell .e-frozen-left-cursor.e-frozen-resize-cursor,
.e-grid.e-bigger .e-gridheader .e-filterbarcell .e-frozen-left-cursor.e-frozen-resize-cursor,
.e-grid.e-bigger .e-rowcell .e-frozen-left-cursor.e-frozen-resize-cursor {
  right: 3px;
}
.e-bigger .e-grid .e-gridheader .e-headercell .e-frozen-right-cursor.e-frozen-resize-cursor:not(.e-frozen-default-cursor),
.e-bigger .e-grid .e-gridheader .e-filterbarcell .e-frozen-right-cursor.e-frozen-resize-cursor:not(.e-frozen-default-cursor),
.e-grid.e-bigger .e-gridheader .e-headercell .e-frozen-right-cursor.e-frozen-resize-cursor:not(.e-frozen-default-cursor),
.e-grid.e-bigger .e-gridheader .e-filterbarcell .e-frozen-right-cursor.e-frozen-resize-cursor:not(.e-frozen-default-cursor) {
  left: 3px;
}
.e-bigger .e-grid .e-frozen-helper,
.e-grid.e-bigger .e-frozen-helper {
  border-left: 2px solid rgba(0, 120, 214, 0.6);
  cursor: move;
  position: absolute;
  z-index: 2;
}
.e-bigger .e-grid .e-content.e-freezeline-moving,
.e-grid.e-bigger .e-content.e-freezeline-moving {
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.e-bigger .e-grid:not(.sf-grid) .e-gridheader table th[rowspan], .e-bigger .e-grid:not(.sf-grid).e-device .e-gridheader table th[rowspan],
.e-grid:not(.sf-grid).e-bigger .e-gridheader table th[rowspan],
.e-grid:not(.sf-grid).e-bigger.e-device .e-gridheader table th[rowspan] {
  padding-bottom: 20px;
}

.e-grid {
  border-radius: 0;
  border-style: none solid solid;
  border-width: 1px;
  display: block;
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 13px;
  height: auto;
  position: relative;
  /* stylelint-enable */
  /* stylelint-disable */
  /* stylelint-enable */
}
.e-grid .e-gridheader {
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.e-grid .e-groupdroparea.e-sticky,
.e-grid .e-toolbar.e-sticky,
.e-grid .e-gridheader.e-sticky {
  position: sticky;
  z-index: 10;
}
.e-grid .e-gridheader.e-sticky .e-headercontent .e-reorderuparrow,
.e-grid .e-gridheader.e-sticky .e-headercontent .e-reorderdownarrow,
.e-grid .e-ccdlg.e-sticky {
  position: fixed;
  z-index: 10;
}
.e-grid .e-groupdroparea.e-sticky {
  opacity: 1;
}
.e-grid .e-gridheader .e-firstrow-dragborder, .e-grid.e-rtl .e-gridheader .e-firstrow-dragborder,
.e-grid .e-gridcontent .e-lastrow-dragborder, .e-grid.e-rtl .e-gridcontent .e-lastrow-dragborder {
  bottom: 0;
  background-color: #0078d6;
  height: 2px;
  position: absolute;
  z-index: 5;
}
.e-grid .e-frozenrow-border {
  background-color: #0078d6;
  height: 2px;
  position: relative;
  z-index: 5;
}
.e-grid .e-frozenrow-border.e-frozenrow-empty {
  height: 0;
}
.e-grid.e-top-shadow .e-frozenrow-border {
  height: 4px;
  margin-top: -4px;
  background-color: transparent !important;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.12);
  clip-path: inset(0px 0px -15px 0px);
}
.e-grid .e-grid-relative {
  position: relative;
}
.e-grid .e-dropitemscount {
  border: 1px solid #eaeaea;
  border-radius: 17px;
  box-sizing: content-box;
  font-size: 13px;
  line-height: normal;
  margin-left: -8px;
  min-width: 12px;
  padding: 3px 7px 4px;
  position: absolute;
  text-align: center;
  top: -10px;
  z-index: 5;
}
.e-grid.e-rtl .e-dropitemscount {
  left: -6px !important;
}
.e-grid.e-rtl .e-toolbar .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon {
  padding: 0;
  margin: 0;
}
.e-grid.e-verticallines .e-cloneproperties.e-draganddrop .e-rowdragdrop, .e-grid.e-bothlines .e-cloneproperties.e-draganddrop .e-rowdragdrop {
  border-top: 1px solid #eaeaea;
  border-right: 1px solid #eaeaea;
  border-bottom: 0;
}
.e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder, .e-grid .e-gridcontent .e-rowcell.e-dragborder, .e-grid .e-gridcontent .e-rowdragdrop.e-dragborder, .e-grid .e-gridcontent .e-detailrowcollapse.e-dragborder, .e-grid .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder, .e-grid .e-gridheader .e-rowcell.e-dragborder, .e-grid .e-gridheader .e-rowdragdrop.e-dragborder, .e-grid .e-gridheader .e-detailrowcollapse.e-dragborder, .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder, .e-grid.e-rtl .e-gridcontent .e-detailrowcollapse.e-dragborder, .e-grid.e-rtl .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridheader .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridheader .e-rowdragdrop.e-dragborder, .e-grid.e-rtl .e-gridheader .e-detailrowcollapse.e-dragborder {
  z-index: 5;
}
.e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-leftfreeze, .e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-rightfreeze, .e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-fixedfreeze, .e-grid .e-gridcontent .e-rowcell.e-dragborder.e-leftfreeze, .e-grid .e-gridcontent .e-rowcell.e-dragborder.e-rightfreeze, .e-grid .e-gridcontent .e-rowcell.e-dragborder.e-rightfreeze, .e-grid .e-gridcontent .e-rowdragdrop.e-dragborder.e-leftfreeze, .e-grid .e-gridcontent .e-rowdragdrop.e-dragborder.e-rightfreeze, .e-grid .e-gridcontent .e-rowdragdrop.e-dragborder.e-fixedfreeze, .e-grid .e-gridcontent .e-detailrowcollapse.e-dragborder.e-leftfreeze, .e-grid .e-gridcontent .e-detailrowcollapse.e-dragborder.e-rightfreeze, .e-grid .e-gridcontent .e-detailrowcollapse.e-dragborder.e-fixedfreeze, .e-grid .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder.e-leftfreeze, .e-grid .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder.e-rightfreeze, .e-grid .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder.e-fixedfreeze, .e-grid .e-gridheader .e-rowcell.e-dragborder.e-leftfreeze, .e-grid .e-gridheader .e-rowcell.e-dragborder.e-rightfreeze, .e-grid .e-gridheader .e-rowcell.e-dragborder.e-rightfreeze, .e-grid .e-gridheader .e-rowdragdrop.e-dragborder.e-leftfreeze, .e-grid .e-gridheader .e-rowdragdrop.e-dragborder.e-rightfreeze, .e-grid .e-gridheader .e-rowdragdrop.e-dragborder.e-fixedfreeze, .e-grid .e-gridheader .e-detailrowcollapse.e-dragborder.e-leftfreeze, .e-grid .e-gridheader .e-detailrowcollapse.e-dragborder.e-rightfreeze, .e-grid .e-gridheader .e-detailrowcollapse.e-dragborder.e-fixedfreeze, .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-fixedfreeze, .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder.e-fixedfreeze, .e-grid.e-rtl .e-gridcontent .e-detailrowcollapse.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridcontent .e-detailrowcollapse.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridcontent .e-detailrowcollapse.e-dragborder.e-fixedfreeze, .e-grid.e-rtl .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder.e-fixedfreeze, .e-grid.e-rtl .e-gridheader .e-rowcell.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridheader .e-rowcell.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridheader .e-rowcell.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridheader .e-rowdragdrop.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridheader .e-rowdragdrop.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridheader .e-rowdragdrop.e-dragborder.e-fixedfreeze, .e-grid.e-rtl .e-gridheader .e-detailrowcollapse.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridheader .e-detailrowcollapse.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridheader .e-detailrowcollapse.e-dragborder.e-fixedfreeze {
  z-index: 6;
}
.e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder, .e-grid .e-gridcontent .e-rowcell.e-dragborder, .e-grid .e-gridcontent .e-rowdragdrop.e-dragborder, .e-grid .e-gridcontent .e-detailrowcollapse.e-dragborder, .e-grid .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder, .e-grid .e-gridheader .e-rowcell.e-dragborder, .e-grid .e-gridheader .e-rowdragdrop.e-dragborder, .e-grid .e-gridheader .e-detailrowcollapse.e-dragborder, .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder, .e-grid.e-rtl .e-gridcontent .e-detailrowcollapse.e-dragborder, .e-grid.e-rtl .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridheader .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridheader .e-rowdragdrop.e-dragborder, .e-grid.e-rtl .e-gridheader .e-detailrowcollapse.e-dragborder {
  box-shadow: 0 2px 0 0 #0078d6;
}
.e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid .e-gridcontent .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid .e-gridcontent .e-rowdragdrop.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid .e-gridcontent .e-detailrowcollapse.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid .e-gridheader .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid .e-gridheader .e-rowdragdrop.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid .e-gridheader .e-detailrowcollapse.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridcontent .e-detailrowcollapse.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridheader tr.e-row:first-child .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridheader .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridheader .e-rowdragdrop.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridheader .e-detailrowcollapse.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze) {
  position: relative;
}
.e-grid .e-gridheader thead .e-icons:not(.e-check):not(.e-stop) {
  font-size: 10px;
}
.e-grid .e-row .e-icon-rowdragicon::before {
  display: block;
  text-indent: 10px;
  font-size: 12px;
  font-weight: bold;
  color: #333;
  opacity: 0.54;
}
.e-grid .e-row .e-icon-rowdragmoveicon::before {
  font-size: 12px;
  font-weight: bold;
}
.e-grid .e-row .e-icon-rowdragmoveicon {
  padding-left: 10px;
}
.e-grid .e-draganddrop .e-rowcell, .e-grid.e-rtl .e-draganddrop .e-rowcell {
  padding-left: 6px;
  padding-right: 6px;
}
.e-grid .e-gridcontent .e-rowdragdrop,
.e-grid .e-gridheader .e-rowdragdrop {
  border-style: solid;
  border-width: 1px 0 0 0;
  border-color: #eaeaea;
}
.e-grid .e-gridcontent .e-rowdragdrop.e-lastrowcell,
.e-grid .e-gridcontent .e-recordpluscollapse.e-lastrowcell,
.e-grid .e-gridcontent .e-indentcell.e-lastrowcell,
.e-grid .e-gridcontent .e-groupcaption.e-lastrowcell {
  border-bottom-width: 1px;
}
.e-grid .e-cloneproperties.e-draganddrop .e-row .e-icon-rowdragicon::before {
  position: relative;
  left: -5px;
}
.e-grid .e-icon-rowdragicon {
  font-size: 12px;
  text-indent: 8px;
}
.e-grid .e-toolbar {
  border-bottom: 0;
  border-left: 0;
  border-right: 0;
  border-top: 1px solid #eaeaea;
  border-radius: 0;
}
.e-grid .e-toolbar .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon {
  padding: 0;
  min-width: 30px;
  margin-right: 0;
}
.e-grid .e-toolbar .e-toolbar-item.e-search-wrapper .e-sicon.e-clear-icon::before {
  font-size: 12px;
}
.e-grid .e-toolbar-items .e-input-group-icon.e-icons.e-sicon:hover:not(.e-clear-icon), .e-grid .e-toolbar-items .e-input-group-icon.e-icons.e-sicon:active:not(.e-clear-icon), .e-grid .e-toolbar-items .e-input-group-icon.e-icons.e-sicon:focus:not(.e-clear-icon) {
  background: none;
  box-shadow: none;
}
.e-grid .e-toolbar-items .e-input-group-icon.e-icons.e-sicon:not(.e-clear-icon) {
  border: none;
}
.e-grid .e-res-toolbar {
  border-bottom: 1px solid;
  border-bottom-color: #eaeaea;
}
.e-grid .e-res-toolbar .e-toolbar-items {
  min-height: initial;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-tbar-btn:hover, .e-grid .e-res-toolbar .e-toolbar-items .e-tbar-btn:active, .e-grid .e-res-toolbar .e-toolbar-items .e-tbar-btn:focus, .e-grid .e-res-toolbar .e-toolbar-items .e-search-icon:hover, .e-grid .e-res-toolbar .e-toolbar-items .e-search-icon:active, .e-grid .e-res-toolbar .e-toolbar-items .e-search-icon:focus, .e-grid .e-res-toolbar .e-toolbar-items .e-sicon.e-clear-icon:hover, .e-grid .e-res-toolbar .e-toolbar-items .e-sicon.e-clear-icon:active, .e-grid .e-res-toolbar .e-toolbar-items .e-sicon.e-clear-icon:focus {
  background: none;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-icons {
  color: #333;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-left .e-toolbar-item .e-tbar-btn .e-resfilter-icon {
  font-size: 18px;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-responsive-right {
  width: calc(100% - 50px);
}
.e-grid .e-res-toolbar .e-toolbar-items .e-responsive-right .e-search-wrapper .e-input-group::before, .e-grid .e-res-toolbar .e-toolbar-items .e-responsive-right .e-search-wrapper .e-input-group::after {
  background: none;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-right .e-search-wrapper {
  width: 100%;
  padding-left: 16px;
  padding-right: 0;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-right .e-search-wrapper .e-search-icon {
  font-size: 18px;
  margin-top: 0;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-right .e-search-wrapper .e-input-group {
  border: 0;
  opacity: 1;
  padding: 0;
  padding-right: 6px;
  width: 100%;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-right .e-search-wrapper .e-input-group .e-input {
  margin-top: 1px;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-toolbar-right .e-search-wrapper .e-input-group .e-search-icon {
  margin-top: 0;
  padding-left: 0;
}
.e-grid .e-res-toolbar .e-toolbar-items .e-responsive-right .e-search-wrapper {
  padding-left: 0;
}
.e-grid.e-bigger .e-res-toolbar .e-toolbar-items .e-responsive-right .e-search-wrapper .e-sicon.e-clear-icon {
  margin: 0;
}
.e-grid.e-bigger .e-res-toolbar .e-toolbar-items .e-responsive-right .e-search-wrapper .e-sicon.e-clear-icon::before {
  font-size: 18px;
}
.e-grid.e-rtl .e-res-toolbar .e-toolbar-items .e-responsive-right .e-search-wrapper {
  padding-left: 26px;
}
.e-grid.e-rtl .e-res-toolbar .e-toolbar-items .e-responsive-right .e-search-wrapper .e-sicon.e-clear-icon {
  margin: 0;
}
.e-grid .e-toolbar-items .e-toolbar-item.e-search-wrapper {
  padding-bottom: 4px;
  padding-top: 4px;
}
.e-grid .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-search:focus {
  opacity: 1;
}
.e-grid .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-search {
  margin-bottom: 0;
  opacity: 0.6;
  width: 230px;
}
.e-grid .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-search.e-input-focus {
  opacity: 1;
}
.e-grid .e-toolbar-items .e-toolbar-item.e-search-wrapper .e-search .e-search-icon {
  min-width: 30px;
}
.e-bigger .e-grid .e-group-animator .e-groupheadercell, .e-bigger .e-grid .e-group-animator .e-groupheadercell:hover {
  margin: 19px 0 19px 0;
  border-bottom-width: 1px;
}
.e-grid .e-groupdroparea.e-group-animate.e-grouped {
  height: 54px;
}
.e-bigger .e-grid .e-group-animate.e-groupdroparea .e-nextgroup {
  margin-top: 22px;
}
.e-grid .e-group-animator .e-groupheadercell, .e-grid .e-group-animator .e-groupheadercell:hover {
  margin: 16px 0 16px 0;
  border-bottom-width: 1px;
}
.e-grid .e-group-animator:last-child .e-nextgroup.e-icons.e-icon-next {
  display: none;
}
.e-grid .e-groupdroparea.e-group-animate.e-grouped {
  padding: 0 16px 0;
  height: auto;
  border-bottom-width: 0;
}
.e-grid.e-rtl .e-group-animator {
  float: right;
}
.e-grid.e-rtl .e-group-animate .e-groupheadercell, .e-grid.e-rtl .e-group-animate .e-groupheadercell:hover {
  margin-right: 0;
  margin-left: 0;
}
.e-grid.e-rtl .e-group-animate.e-groupdroparea .e-nextgroup {
  transform: rotate(180deg);
  margin-top: 22px;
}
.e-grid .e-group-animate.e-groupdroparea .e-nextgroup {
  margin-top: 15px;
}
.e-grid .e-groupdroparea.e-group-animate .e-drag.e-icon-drag {
  display: inline-block;
  height: 24px;
  font-size: 11px;
  line-height: 25px;
  padding: 0;
  vertical-align: middle;
}
.e-grid .e-group-animator .e-icon-drag.e-icons {
  margin-left: 0;
}
.e-grid .e-groupdroparea.e-group-animate span.e-drag.e-icons.e-icon-drag {
  cursor: move;
}
.e-grid .e-group-animate .e-drag.e-icon-drag::before {
  opacity: 0.7;
}
.e-grid .e-group-animate span.e-nextgroup.e-icons.e-icon-next {
  display: inline-block;
  float: left;
  height: 24px;
  line-height: 32px;
  padding: 0;
  vertical-align: middle;
}
.e-grid .e-groupdroparea.e-grouped {
  padding: 8px 8px 5px;
}
.e-grid.e-default .e-gridheader.e-stackedfilter .e-grouptopleftcell, .e-grid.e-default.e-horizontallines .e-gridheader.e-stackedfilter .e-grouptopleftcell {
  border-top: 0;
}
.e-grid.e-default .e-gridheader.e-stackedfilter tr:first-child th.e-grouptopleftcell, .e-grid.e-default.e-horizontallines .e-gridheader.e-stackedfilter tr:first-child th.e-grouptopleftcell {
  border-bottom: 1px solid;
}
.e-grid.e-default.e-verticallines .e-gridheader.e-stackedfilter tr:first-child th.e-grouptopleftcell, .e-grid.e-default.e-hidelines .e-gridheader.e-stackedfilter tr:first-child th.e-grouptopleftcell {
  border-bottom: 0;
}
.e-grid.e-default .e-gridheader.e-stackedfilter tr:last-child th.e-grouptopleftcell, .e-grid.e-default.e-horizontallines .e-gridheader.e-stackedfilter tr:last-child th.e-grouptopleftcell {
  border-top: 1px solid;
}
.e-grid.e-default.e-hidelines .e-gridheader.e-stackedfilter tr:last-child th.e-grouptopleftcell, .e-grid.e-default.e-verticallines .e-gridheader.e-stackedfilter tr:last-child th.e-grouptopleftcell {
  border-top: 0;
}
.e-grid.e-default .e-grouptopleftcell {
  border-top: 1px solid;
}
.e-grid.e-default .e-gridheader .e-headercell.e-firstheader, .e-grid.e-default.e-horizontallines .e-headercell.e-firstheader {
  border-left: 1px solid;
}
.e-grid.e-default.e-hidelines .e-headercell.e-firstheader {
  border-left: 0;
}
.e-grid.e-default.e-verticallines .e-grouptopleftcell, .e-grid.e-default.e-bothlines .e-grouptopleftcell, .e-grid.e-default.e-hidelines .e-grouptopleftcell {
  border-top-width: 0;
}
.e-grid.e-default.e-verticallines .e-detailrowcollapse, .e-grid.e-default.e-verticallines .e-detailrowexpand, .e-grid.e-default.e-verticallines .e-rowdragdrop, .e-grid.e-default.e-hidelines .e-detailrowcollapse, .e-grid.e-default.e-hidelines .e-detailrowexpand, .e-grid.e-default.e-hidelines .e-rowdragdrop {
  border-top-width: 0;
}
.e-grid.e-default.e-horizontallines .e-grouptopleftcell {
  border-top: 1px solid;
}
.e-grid .e-gridheader .e-headercell .e-rhandler, .e-grid .e-gridheader .e-headercell .e-rsuppress {
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 4px;
}
.e-grid .e-gridheader .e-frozenheader .e-headercell:last-child .e-rhandler:last-of-type, .e-grid .e-gridheader .e-frozenheader .e-headercell:last-child .e-rsuppress:last-of-type {
  border-right: 0;
}
.e-grid .e-gridheader .e-headercell.e-leftfreeze.e-freezeleftborder .e-rhandler {
  border-right: 0;
}
.e-grid.e-rtl .e-gridheader .e-headercell.e-leftfreeze.e-freezeleftborder .e-rhandler {
  border-left: 0;
}
.e-grid .e-gridheader .e-frozenheader.e-frozen-right-header .e-headercell .e-rhandler, .e-grid .e-gridheader .e-frozenheader.e-frozen-right-header .e-headercell .e-rsuppress {
  left: 0;
}
.e-grid.e-device .e-gridheader .e-headercell .e-rhandler {
  width: 14px;
}
.e-grid.e-rtl .e-gridheader .e-headercell .e-rhandler, .e-grid.e-rtl .e-gridheader .e-headercell .e-rsuppress {
  left: 0;
  right: auto;
}
.e-grid.e-resize-lines .e-gridheader th.e-headercell.e-lastcell .e-rhandler {
  border-right-width: 0;
}
.e-grid.e-resize-lines .e-gridheader .e-frozen-right-header th.e-headercell .e-rhandler {
  border-right-width: 0;
}
.e-grid.e-rtl .e-gridheader .e-frozenheader.e-frozen-right-header .e-headercell .e-rhandler, .e-grid.e-rtl .e-gridheader .e-frozenheader.e-frozen-right-header .e-headercell .e-rsuppress {
  right: 0;
}
.e-grid.e-rtl.e-resize-lines .e-gridheader .e-frozen-right-header th.e-headercell .e-rhandler {
  border-right-width: 1px;
  border-left-width: 0;
}
.e-grid .e-rhelper {
  position: absolute;
  width: 1px;
}
.e-grid .e-virtual-rhandler {
  z-index: 5;
}
.e-grid.e-device .e-ricon::before {
  border-bottom: 6px solid transparent;
  border-right: 6px solid;
  border-top: 6px solid transparent;
  content: "";
  display: block;
  height: 0;
  position: absolute;
  right: 4px;
  top: 4px;
  width: 20px;
}
.e-grid.e-device .e-ricon::after {
  border-bottom: 6px solid transparent;
  border-left: 6px solid;
  border-top: 6px solid transparent;
  content: "";
  display: block;
  height: 0;
  left: 4px;
  position: absolute;
  top: 4px;
  width: 20px;
  z-index: 3;
}
.e-grid.e-rcursor, .e-grid .e-gridheader .e-stackedheadercell.e-rcursor, .e-grid .e-gridheader .e-rcursor {
  cursor: col-resize;
}
.e-grid.e-editing .e-gridheader .e-rcursor {
  cursor: default;
}
.e-grid .e-table {
  border-collapse: separate;
  table-layout: fixed;
  width: 100%;
}
.e-grid .e-tableborder {
  border-right: 1px solid;
}
.e-grid .e-virtualtable {
  will-change: transform;
  z-index: 1;
}
.e-grid .e-headercelldiv {
  border: 0 none;
  display: block;
  font-size: 14px;
  font-weight: normal;
  height: 29px;
  line-height: 29px;
  margin: 0;
  overflow: hidden;
  padding: 0 1px 0;
  text-align: left;
  text-transform: none;
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.e-grid .e-headercelldiv.e-headerchkcelldiv {
  overflow: visible;
}
.e-grid .e-headercell.e-templatecell .e-headercelldiv {
  height: auto;
  min-height: 29px;
}
.e-grid .e-gridheader .e-headercontent .e-rightalign .e-headercelldiv.e-headerchkcelldiv {
  padding: 0 0.6em;
}
.e-grid .e-gridheader .e-headercontent .e-centeralign .e-headercelldiv.e-headerchkcelldiv {
  padding: 0 0.6em;
}
.e-grid .e-headercell,
.e-grid .e-headercell.e-stackedheadercell,
.e-grid .e-headercell.e-defaultcursor {
  cursor: default;
}
.e-grid .e-headercell.e-mousepointer,
.e-grid .e-headercell.e-stackedheadercell.e-mousepointer {
  cursor: pointer;
}
.e-grid .e-gridheader .e-headercell,
.e-grid .e-gridheader .e-detailheadercell {
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.e-grid:not(.e-left-shadow, .e-right-shadow) .e-gridcontent tr:first-child td {
  border-top: 0 none;
}
.e-grid .e-gridcontent tr:first-child td.e-xlsel-top-border {
  border-top-width: 2px;
}
.e-grid .e-gridheader tr:first-child th {
  border-top: 0 none;
}
.e-grid .e-gridheader tr th:last-child.e-filterbarcell {
  padding-right: 10px;
}
.e-grid .e-gridheader tr th:last-child {
  padding-right: 10px;
}
.e-grid .e-gridheader tr th:first-child {
  padding-left: 10px;
}
.e-grid .e-gridheader tr th:first-child.e-filterbarcell {
  padding-left: 10px;
}
.e-grid .e-gridheader {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-top-style: solid;
  border-top-width: 1px;
}
.e-grid .e-frozenhdrcont,
.e-grid .e-frozenhdr {
  border-bottom-width: 0;
}
.e-grid .e-frozenhdrcont table tr:not(.e-editedrow):first-child td:not(.e-xlsel-top-border) {
  border-top-style: solid;
  border-top-width: 1px;
}
.e-grid.e-bothlines .e-filterbarcell, .e-grid.e-bothlines .e-filterbarcelldisabled {
  border-width: 1px 0 0 1px;
}
.e-grid .e-headercell,
.e-grid .e-detailheadercell {
  border-style: solid;
  border-width: 0;
  font-size: 14px;
  font-weight: normal;
  height: 36px;
  overflow: hidden;
  padding: 0 8px 0;
  position: relative;
  text-align: left;
}
.e-grid.e-device .e-headercell {
  padding: 0 8px;
}
.e-grid.e-device .e-headercell:first-child {
  padding: 0 8px 0 10px;
}
.e-grid.e-device .e-headercell:last-child {
  padding: 0 10px 0 8px;
}
.e-grid .e-rowcell {
  border-style: solid;
  border-width: 1px 0 0;
  display: table-cell;
  font-size: 13px;
  line-height: 21px;
  overflow: hidden;
  padding: 7px 8px;
  vertical-align: middle;
  white-space: nowrap;
  width: auto;
}
.e-grid.e-left-shadow .e-leftfreeze.e-freezeleftborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border), .e-grid.e-right-shadow .e-rightfreeze.e-freezerightborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border) {
  line-height: 19px;
  border-width: 1.5px;
}
.e-grid.e-rtl.e-left-shadow .e-leftfreeze.e-freezeleftborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border), .e-grid.e-rtl.e-right-shadow .e-rightfreeze.e-freezerightborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border) {
  line-height: 19px;
  border-width: 1.5px;
}
.e-grid.e-rtl.e-default.e-left-shadow .e-leftfreeze.e-freezeleftborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border), .e-grid.e-rtl.e-default.e-right-shadow .e-rightfreeze.e-freezerightborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border) {
  line-height: 19px;
  border-width: 1.5px;
}
.e-grid.e-wrap.e-left-shadow .e-leftfreeze.e-freezeleftborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border), .e-grid.e-wrap.e-right-shadow .e-rightfreeze.e-freezerightborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border) {
  line-height: 19px;
  border-width: 1.5px;
}
.e-grid.e-wrap.e-left-shadow .e-leftfreeze.e-freezeleftborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border), .e-grid.e-wrap.e-right-shadow .e-rightfreeze.e-freezerightborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border) {
  line-height: 19px;
  border-width: 1.5px;
}
.e-grid .e-frozenheader table .e-insertedrow td.e-rowcell,
.e-grid .e-frozencontent table .e-insertedrow td.e-rowcell,
.e-grid .e-movableheader table .e-insertedrow td.e-rowcell,
.e-grid .e-movablecontent table .e-insertedrow td.e-rowcell, .e-grid:not(.e-grid-min-height) .e-gridcontent .e-content tr.e-insertedrow .e-rowcell:empty, .e-grid .e-row.e-emptyrow {
  height: 36px;
}
.e-grid .e-editedrow .e-input-group input.e-input,
.e-grid .e-editedrow .e-input-group.e-control-wrapper input.e-input,
.e-grid .e-addedrow .e-input-group input.e-input,
.e-grid .e-addedrow .e-input-group.e-control-wrapper input.e-input {
  min-height: unset;
}
.e-grid:not(.e-grid-min-height) .e-gridcontent tr td:not(.e-indentcell):first-child:empty, .e-grid:not(.e-grid-min-height) .e-gridcontent tr.e-row .e-rowcell:empty {
  height: 35px;
}
.e-grid.e-afenabled .e-movablecontent,
.e-grid.e-afenabled .e-frozencontent,
.e-grid.e-afenabled .e-movableheader,
.e-grid.e-afenabled .e-frozenheader,
.e-grid.e-afenabled .e-headercontent, .e-grid.e-enabledboxbdr .e-movablecontent,
.e-grid.e-enabledboxbdr .e-frozencontent,
.e-grid.e-enabledboxbdr .e-movableheader,
.e-grid.e-enabledboxbdr .e-frozenheader,
.e-grid.e-enabledboxbdr .e-headercontent {
  position: relative;
}
.e-grid .e-rowcell:first-child,
.e-grid .e-summarycell:first-child {
  padding-left: 10px;
}
.e-grid .e-rowcell:last-child,
.e-grid .e-summarycell:last-child {
  padding-right: 10px;
}
.e-grid .e-unboundcell,
.e-grid .e-editedrow .e-normaledit .e-unboundcell,
.e-grid .e-addedrow .e-normaledit .e-unboundcell {
  padding-bottom: 4px;
  padding-top: 4px;
}
.e-grid .e-unboundcelldiv > button {
  margin: 0 3.5px;
}
.e-grid .e-unboundcelldiv {
  margin: 0 -3.5px;
}
.e-grid .e-summarycell {
  border-style: solid;
  border-width: 1px 0 0;
  font-size: 14px;
  font-weight: normal;
  height: auto;
  line-height: 21px;
  padding: 7px 8px;
  white-space: normal;
  word-wrap: break-word;
}
.e-grid .e-summarycell.e-lastrowcell {
  border-bottom-width: 1px;
}
.e-grid .e-summarycontent {
  display: -ms-flexbox;
  display: flex;
}
.e-grid .e-summarycontent .e-frozenfootercontent {
  float: left;
  width: min-content;
}
.e-grid .e-summarycontent .e-frozen-right-footercontent {
  float: right;
}
.e-grid .e-summarycontent .e-frozen-right-footercontent .e-firstsummarycell {
  border-left: 1px solid;
  border-color: #eaeaea;
}
.e-grid .e-summarycontent .e-movablefootercontent {
  height: inherit;
  overflow: hidden;
  -ms-flex: 1;
      flex: 1;
}
.e-grid .e-summarycontent .e-indentcell {
  border-width: 1px 0 0;
}
.e-grid .e-summarycontent .e-detailindentcelltop {
  border-width: 1px 0 0;
}
.e-grid.e-device .e-rowcell, .e-grid.e-device .e-summarycell {
  padding: 7px 8px;
}
.e-grid.e-device .e-rowcell:first-child, .e-grid.e-device .e-summarycell:first-child {
  padding: 7px 8px 7px 10px;
}
.e-grid.e-device .e-rowcell:last-child, .e-grid.e-device .e-summarycell:last-child {
  padding: 7px 10px 7px 8px;
}
.e-grid td.e-rowcell.e-checkbox {
  padding: 8px 8px;
}
.e-grid td.e-rowcell.e-checkbox input[type=checkbox] {
  margin-top: 5px;
}
.e-grid.e-default .e-rowcell.e-lastrowcell:not(.e-xlsel-bottom-border) {
  border-bottom-width: 1px;
}
.e-grid.e-default .e-detailrowcollapse.e-lastrowcell {
  border-bottom-width: 1px;
}
.e-grid.e-default .e-detailrow .e-lastrowcell {
  border-bottom: 1px solid;
  border-bottom-color: #eaeaea;
}
.e-grid.e-bothlines .e-rowcell.e-lastrowcell:not(.e-xlsel-bottom-border) {
  border-bottom-width: 1px;
}
.e-grid.e-bothlines .e-rowcell {
  border-width: 1px 0 0 1px;
}
.e-grid:not(.sf-grid).e-gridheader table th[rowspan], .e-grid:not(.sf-grid).e-device .e-gridheader table th[rowspan] {
  padding-bottom: 4px;
  vertical-align: bottom;
}
.e-grid .e-emptyrow td {
  line-height: 21px;
  padding: 0.7em;
}
.e-grid.e-responsive .e-rowcell, .e-grid.e-responsive .e-headercelldiv {
  text-overflow: ellipsis;
  white-space: nowrap;
}
.e-grid.e-responsive .e-rowcell.e-gridchkbox, .e-grid.e-responsive .e-rowcell.e-gridchkbox-cell, .e-grid.e-responsive .e-headercelldiv.e-headerchkcelldiv {
  text-overflow: clip;
}
.e-grid.e-default .e-headercell, .e-grid.e-default .e-detailheadercell {
  border-width: 0;
}
.e-grid [aria-selected] + tr .e-detailindentcell {
  border-top-style: solid;
  border-top-width: 1px;
}
.e-grid.e-default.e-verticallines .e-headercell, .e-grid.e-default.e-verticallines .e-detailheadercell {
  border-width: 0 0 0 1px;
}
.e-grid.e-default.e-verticallines .e-headercell.e-stackedheadercell {
  border-bottom: 1px solid;
}
.e-grid.e-default .e-stackedheadercell {
  border-width: 1px 0 1px 1px;
}
.e-grid.e-default .e-stackedheadercell.e-leftfreeze.e-freezeleftborder {
  border-right-width: 2px;
}
.e-grid.e-default .e-stackedheadercell.e-rightfreeze.e-freezerightborder {
  border-left-width: 2px;
}
.e-grid.e-default .e-stackedheadercell.e-fixedfreeze.e-freezeleftborder {
  border-left-width: 1px;
}
.e-grid.e-default .e-stackedheadercell.e-fixedfreeze.e-freezerightborder {
  border-right-width: 1px;
}
.e-grid.e-default.e-verticallines tr th:first-child:not(.e-firstcell) {
  border-left-width: 0;
}
.e-grid:not(.sf-grid).e-default.e-bothlines .e-headercell, .e-grid:not(.sf-grid).e-default.e-bothlines .e-detailheadercell {
  border-width: 0 0 0 1px;
}
.e-grid:not(.sf-grid).e-default table th[rowspan] {
  border-width: 0 0 0 1px;
}
.e-grid:not(.sf-grid).e-default table th[rowspan].e-leftfreeze.e-freezeleftborder {
  border-right-width: 2px;
}
.e-grid:not(.sf-grid).e-default table th[rowspan].e-rightfreeze.e-freezerightborder {
  border-left-width: 2px;
}
.e-grid:not(.sf-grid).e-default table th[rowspan].e-fixedfreeze.e-freezeleftborder {
  border-left-width: 1px;
}
.e-grid:not(.sf-grid).e-default table th[rowspan].e-fixedfreeze.e-freezerightborder {
  border-right-width: 1px;
}
.e-grid.e-default.e-verticallines tr th:first-child:not(.e-firstcell) {
  border-left-width: 0;
}
.e-grid tr th.e-firstcell {
  border-left-style: solid;
  border-left-width: 1px;
}
.e-grid.e-default tr th.e-detailheadercell:first-child {
  border-left-width: 0;
}
.e-grid.e-default:not(.e-rtl) tr td:first-child:not(.e-xlsel-left-border, .e-fixedfreeze.e-freezeleftborder), .e-grid.e-default:not(.e-rtl) tr th.e-headercell:first-child:not(.e-firstcell):not(.e-headercell.e-frozen-left-border), .e-grid.e-default:not(.e-rtl) tr th.e-filterbarcell:first-child:not(.e-filterbarcell.e-frozen-left-border) {
  border-left-width: 0;
}
.e-grid.e-default .e-gridheader th.e-firstcell.e-movablefirst {
  border-left-width: 0;
}
.e-grid .e-hide {
  display: none;
}
.e-grid .e-rowcell,
.e-grid .e-gridcontent,
.e-grid .e-gridheader,
.e-grid .e-headercontent,
.e-grid .e-groupdroparea,
.e-grid .e-gridfooter,
.e-grid .e-summarycontent {
  overflow: hidden;
  vertical-align: middle;
}
.e-grid .e-sortfilterdiv {
  float: right;
  height: 16px;
  margin: -21px 3px 0 0;
  padding: 2px 2px 2px 4px;
  width: 16px;
}
.e-grid .e-gridheader .e-rightalign .e-sortfilterdiv {
  float: left;
  margin: -21px 10px 0 0;
}
.e-grid .e-gridheader .e-sortfilter .e-rightalign .e-headercelldiv,
.e-grid .e-gridheader .e-rightalign .e-stackedheadercelldiv {
  padding: 0 0 0 1em;
}
.e-grid .e-gridheader .e-sortfilter .e-rightalign.e-fltr-icon .e-headercelldiv {
  margin: 0 20px 0 10px;
}
.e-grid .e-gridheader .e-sortfilter .e-fltr-icon .e-headercelldiv {
  margin: 0 20px 0 0;
}
.e-grid .e-gridheader .e-sortfilter .e-headercelldiv,
.e-grid .e-gridheader .e-stackedheadercelldiv {
  padding: 0 25px 0 0;
}
.e-grid .e-gridheader .e-headercell .e-headercelldiv.e-headerchkcelldiv {
  padding: 0 25px 0 0;
}
.e-grid.e-rtl .e-gridheader .e-headercell .e-headercelldiv.e-headerchkcelldiv {
  padding: 0 0 0 0;
}
.e-grid .e-filtermenudiv {
  float: right;
  margin: -24px -7px;
  padding: 6px;
  text-align: right;
}
.e-grid .e-filtermenudiv:hover {
  cursor: pointer;
}
.e-grid.e-print-grid-layout .e-pager, .e-grid.e-print-grid-layout .e-filterbar, .e-grid.e-print-grid-layout .e-icons:not(.e-frame), .e-grid.e-print-grid-layout .e-grouptopleftcell, .e-grid.e-print-grid-layout .e-recordpluscollapse, .e-grid.e-print-grid-layout .e-indentcell, .e-grid.e-print-grid-layout .e-recordplusexpand {
  display: none;
}
.e-grid.e-print-grid-layout .e-indentcell.e-detailindentcelltop {
  display: table-cell;
}
.e-grid.e-print-grid-layout .e-content {
  overflow-y: hidden;
}
.e-grid.e-print-grid-layout .e-grouptext {
  width: auto;
}
.e-grid.e-print-grid-layout .e-detailcell {
  padding: 0.8em 0.6em;
}
.e-grid.e-print-grid {
  left: -1000px;
  top: -1000px;
}
.e-grid .e-flmenu-valuediv {
  padding: 24px 0 0;
}
.e-grid .e-flbldcontent {
  padding: 18px 18px 0;
}
.e-grid .e-flblbtn {
  width: 110px;
}
.e-grid .e-sortnumber {
  border-radius: 55%;
  display: inline-block;
  float: right;
  font-size: 9px;
  height: 15px;
  line-height: 15px;
  margin: 7px 0 0;
  text-align: center;
  width: 15px;
}
.e-grid .e-gridheader .e-fltr-icon .e-sortnumber {
  margin: 8px 0 0;
}
.e-grid .e-gridheader .e-rightalign.e-fltr-icon .e-sortnumber {
  float: left;
  margin: 8px 10px 0;
}
.e-grid .e-gridheader .e-rightalign .e-sortnumber {
  float: left;
  margin: 7px 0 0;
}
.e-grid .e-gridheader .e-fltr-icon .e-sortfilterdiv {
  margin: -20px 20px 0 0;
}
.e-grid .e-gridheader .e-rightalign.e-fltr-icon .e-sortfilterdiv {
  margin: -20px 0 0 8px;
}
.e-grid.e-wrap .e-gridheader .e-rightalign .e-sortnumber {
  margin: 3px 2px 0 15px;
}
.e-grid.e-wrap .e-gridheader .e-sortnumber {
  margin: 3px -5px 0 2px;
}
.e-grid.e-wrap .e-gridheader .e-sortfilterdiv {
  margin: -15px -5px;
}
.e-grid .e-movableheader .e-editcell.e-normaledit,
.e-grid .e-frozenheader .e-editcell.e-normaledit {
  border-top-color: transparent;
}
.e-grid .e-columnmenu {
  position: absolute;
  right: 23px;
  float: right;
  margin: -25px -24px;
  padding: 6px;
}
.e-grid.e-wrap .e-columnmenu {
  line-height: 18px;
}
.e-grid.e-rtl .e-columnmenu {
  left: 5px;
  right: auto;
  margin: -24px -18px -18px 1px;
}
.e-grid.e-wrap.e-rtl .e-columnmenu {
  line-height: 18px;
}
.e-grid [class^=e-] {
  box-sizing: border-box;
}
.e-grid .e-rowdragdropcell {
  border-style: solid;
  border-width: 1px 0 0;
}
.e-grid .e-detailrowcollapse,
.e-grid .e-detailrowexpand {
  border-style: solid;
  border-width: 1px 0 0;
  cursor: pointer;
}
.e-grid .e-detailindentcell {
  border-right-style: solid;
  border-right-width: 1px;
}
.e-grid .e-detailcell {
  border-top-style: solid;
  border-top-width: 1px;
  padding: 0.3em;
  font-weight: normal;
  text-align: left;
}
.e-grid th.e-detailcell .e-rowcell,
.e-grid th.e-detailcell .e-gridpager {
  text-align: left;
}
.e-grid.e-rtl th.e-detailcell .e-rowcell, .e-grid.e-rtl th.e-detailcell .e-gridpager,
.e-grid th.e-detailcell .e-rtl .e-rowcell,
.e-grid th.e-detailcell .e-rtl .e-gridpager {
  text-align: right;
}
.e-grid.e-verticallines .e-rowcell, .e-grid.e-verticallines .e-filterbarcell {
  border-width: 0 0 0 1px;
}
.e-grid.e-hidelines .e-rowcell, .e-grid.e-horizontallines .e-headercell, .e-grid.e-hidelines .e-headercell, .e-grid.e-horizontallines .e-detailheadercell, .e-grid.e-hidelines .e-detailheadercell, .e-grid.e-hidelines tr th.e-firstcell, .e-grid.e-hidelines .e-filterbarcell {
  border-width: 0;
}
.e-grid.e-horizontallines .e-headercell.e-stackedheadercell {
  border-width: 0 0 1px 1px;
}
.e-grid.e-horizontallines .e-rowcell {
  border-width: 1px 0 0;
}
.e-grid.e-horizontallines .e-filterbarcell {
  border-width: 1px 0 0;
}
.e-grid.e-horizontallines .e-rowcell.e-lastrowcell:not(.e-xlsel-bottom-border), .e-grid.e-verticallines .e-rowcell.e-lastrowcell:not(.e-xlsel-bottom-border), .e-grid.e-hidelines .e-rowcell.e-lastrowcell:not(.e-xlsel-bottom-border) {
  border-bottom-width: 1px;
}
.e-grid.e-horizontallines .e-detailrowvisible, .e-grid.e-verticallines .e-detailrowvisible, .e-grid.e-hidelines .e-detailrowvisible, .e-grid.e-bothlines .e-detailrowvisible {
  border-left-width: 0;
}
.e-grid.e-verticallines .e-firstchildrow .e-rowcell, .e-grid.e-verticallines .e-firstchildrow .e-detailrowcollapse, .e-grid.e-verticallines .e-firstchildrow .e-detailrowexpand, .e-grid.e-hidelines .e-firstchildrow .e-rowcell, .e-grid.e-hidelines .e-firstchildrow .e-detailrowcollapse, .e-grid.e-hidelines .e-firstchildrow .e-detailrowexpand {
  border-top-width: 1px;
}
.e-grid .e-filterbarcell .e-icons::before {
  display: block;
  margin: 0 auto;
}
.e-grid .e-filterbarcell .e-filtertext::-webkit-search-cancel-button,
.e-grid .e-search input::-webkit-search-cancel-button {
  display: none;
}
.e-grid .e-filterbarcell .e-filtertext::-ms-clear,
.e-grid .e-search input::-ms-clear {
  display: none;
}
.e-grid .e-filterbarcell,
.e-grid .e-filterbarcelldisabled {
  border-collapse: collapse;
  border-style: solid;
  border-width: 1px 0 0;
  cursor: default;
  height: 36px;
  overflow: hidden;
  padding: 0 8px;
  vertical-align: middle;
}
.e-grid .e-rowdragheader {
  border-color: #eaeaea;
  border-style: solid;
  border-width: 1px 0 0;
}
.e-grid .e-filterbarcell input {
  border-radius: 0;
  border-style: solid;
  border-width: 1px;
  font-size: 13px;
  font-weight: normal;
  height: 32px;
  padding-right: 24px;
  text-indent: 10px;
  width: 100%;
}
.e-grid.e-device .e-filterbarcell {
  padding: 3px 8px 2px 8px;
}
.e-grid.e-device .e-filterbarcell:first-child {
  padding: 3px 8px 2px 10px;
}
.e-grid.e-device .e-filterbarcell:last-child {
  padding: 3px 10px 2px 8px;
}
.e-grid .e-searchclear {
  float: right;
  position: relative;
}
.e-grid.e-rtl .e-searchclear {
  float: left;
  position: relative;
}
.e-grid .e-checkboxlist {
  height: 200px;
  margin-top: 5px;
  min-height: 160px;
  overflow-y: auto;
}
.e-grid .e-checkfltrnmdiv {
  height: 60px;
  padding: 23px;
  text-align: center;
}
.e-grid .e-checkboxlist > span {
  padding-left: 7px;
}
.e-grid .e-chk-hidden {
  -moz-appearance: none; /* stylelint-disable-line property-no-vendor-prefix */
  height: 1px;
  opacity: 0;
  width: 1px;
}
.e-grid .e-checkselect,
.e-grid .e-checkselectall {
  margin: 0;
  opacity: 0;
  position: absolute;
  width: 0;
}
.e-grid .e-rowcell .e-checkbox-wrapper,
.e-grid .e-rowcell .e-css.e-checkbox-wrapper {
  -webkit-user-select: auto; /* stylelint-disable-line property-no-vendor-prefix */
  height: 20px;
  line-height: 21px;
  position: relative;
  top: 0;
  -ms-user-select: auto;
      user-select: auto;
}
.e-grid .e-dialog.e-checkboxfilter,
.e-grid .e-dialog.e-flmenu {
  min-width: 250px;
}
.e-grid .e-dialog.e-checkboxfilter .e-dlg-content,
.e-grid .e-dialog.e-flmenu .e-dlg-content {
  border-radius: 0;
}
.e-grid .e-dialog.e-checkboxfilter {
  min-height: 348px;
}
.e-grid .e-checkboxfilter .e-searchbox {
  display: block;
}
.e-grid .e-checkboxfilter .e-dlg-content {
  padding-left: 18px;
}
.e-grid .e-checkboxfilter .e-ftrchk, .e-grid .e-checkboxfilter .e-searchbox {
  padding-left: 7px;
}
.e-grid .e-ftrchk {
  padding-bottom: 7px;
  padding-top: 7px;
}
.e-grid .e-filterdiv,
.e-grid .e-fltrtempdiv {
  padding: 2px 0;
  position: relative;
  text-align: center;
  width: 100%;
}
.e-grid .e-pager {
  border-bottom: transparent;
  border-left: transparent;
  border-right: transparent;
}
.e-grid .e-gridpopup {
  font-weight: normal;
  position: absolute;
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
  z-index: 99999;
}
.e-grid .e-gridpopup .e-content {
  border-radius: 4px;
  border-style: solid;
  border-width: 1px;
  font-size: 14px;
  padding: 4px;
}
.e-grid .e-footerpadding .e-lastsummarycell {
  border-left: none;
  border-right: 1px solid;
}
.e-grid.e-rtl .e-footerpadding tr.e-summaryrow td.e-lastsummarycell:last-child {
  border-right: none;
  border-left: 1px solid #eaeaea;
}
.e-grid .e-footerpadding {
  padding-left: 0;
  padding-right: 14px;
}
.e-grid.e-rtl .e-footerpadding {
  padding-left: 14px;
  padding-right: 0;
}
.e-grid .e-gridpopup span {
  border: 1px solid transparent;
  cursor: pointer;
  display: inline-block;
  height: 26px;
  padding: 4px;
  width: 26px;
}
.e-grid .e-gridpopup .e-tail::before,
.e-grid .e-gridpopup .e-tail::after {
  border: 10px solid transparent;
  content: "";
  height: 0;
  left: 8px;
  position: absolute;
  width: 0;
}
.e-grid .e-gridpopup .e-downtail::after {
  top: 34px;
}
.e-grid .e-gridpopup .e-uptail::after {
  top: -17px;
}
.e-grid .e-gridpopup .e-uptail::before {
  top: -19px;
}
.e-grid .e-gridpopup .e-sortdirect,
.e-grid .e-gridpopup .e-rowselect {
  line-height: 18px;
  text-indent: 1px;
}
.e-grid .e-cloneproperties {
  border-style: solid;
  border-width: 1px;
  box-shadow: 0 0;
  font-size: 14px;
  font-weight: normal;
  opacity: 1;
  overflow: hidden;
  padding: 4px 5px;
  text-align: center;
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
  vertical-align: middle;
  white-space: nowrap;
  z-index: 10;
}
.e-grid .e-cloneproperties.e-draganddrop {
  border-spacing: 0;
  font-size: 14px;
  font-weight: normal;
  overflow: visible;
}
.e-grid .e-cloneproperties.e-draganddrop table {
  border-spacing: 0;
}
.e-grid .e-defaultcur {
  cursor: default;
}
.e-grid .e-notallowedcur {
  cursor: not-allowed;
}
.e-grid .e-grabcur {
  cursor: grabbing;
}
.e-grid .e-cloneproperties.e-headerclone table {
  border-spacing: 0;
}
.e-grid .e-headerclone {
  border-radius: 0;
  font-size: 14px;
  font-weight: normal;
  line-height: 29px;
}
.e-grid .e-draganddrop {
  border-width: 0 1px 1px;
  font-weight: normal;
  padding: 0;
}
.e-grid .e-draganddrop .e-rowcell {
  opacity: 0.95;
}
.e-grid.e-default .e-gridheader th.e-firstcell, .e-grid.e-default .e-gridheader th.e-laststackcell {
  border-left-style: solid;
  border-left-width: 1px;
}
.e-grid:not(.sf-grid).e-bothlines .e-gridheader th.e-stackedheadercell {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-left-style: solid;
  border-left-width: 1px;
}
.e-grid.e-default.e-hidelines .e-gridheader th.e-firstcell {
  border-left-width: 0;
}
.e-grid .e-gridheader .e-headercontent .e-reorderuparrow,
.e-grid .e-gridheader .e-headercontent .e-reorderdownarrow,
.e-grid .e-gridheader .e-headercontent .e-reorderuparrow-virtual,
.e-grid .e-gridheader .e-headercontent .e-reorderdownarrow-virtual {
  font-size: 8px;
  margin-left: -1.5px;
  margin-top: -4.5px;
  position: absolute;
  z-index: 10;
}
.e-grid .e-gridheader .e-headercontent .e-reorderuparrow {
  margin-top: -4.5px;
}
.e-grid .e-gridheader .e-headercontent .e-reorderdownarrow {
  margin-top: 4px;
}
.e-grid td.e-active {
  font-weight: normal;
}
.e-grid:not(.e-row-responsive) .e-gridcontent tr.e-row:first-child td.e-cellselectionbackground,
.e-grid td.e-cellselectionbackground {
  font-weight: normal;
}
.e-grid .e-groupdroparea {
  height: auto;
  line-height: normal;
  min-height: 59px;
}
.e-grid .e-griddragarea {
  border: 1px solid;
  opacity: 0.6;
  position: absolute;
}
.e-grid .e-gdclone {
  border-radius: 0;
  padding: 2px;
}
.e-grid .e-content {
  -webkit-overflow-scrolling: touch; /* stylelint-disable-line property-no-vendor-prefix */
  overflow-x: auto;
  overflow-y: scroll;
  position: relative;
}
.e-grid.e-ungroupdrag .e-columnheader, .e-grid.e-ungroupdrag .e-groupdroparea, .e-grid.e-rowdrag .e-columnheader, .e-grid.e-rowdrag .e-groupdroparea {
  cursor: not-allowed;
}
.e-grid .e-groupdroparea, .e-grid.e-ungroupdrag .e-gridcontent, .e-grid.e-rowdrag .e-gridcontent {
  cursor: default;
}
.e-grid .e-groupdroparea {
  border-style: solid none;
  border-width: 1px 0 0;
}
.e-grid .e-groupdroparea {
  border-top-width: 1px;
  font-size: 17px;
  font-weight: lighter;
  opacity: 1;
  padding: 16px 0 8px;
  text-align: center;
  text-indent: 1pt;
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}
.e-grid .e-grouptext {
  display: inline-block;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: auto;
}
.e-grid .e-grid-icon {
  float: left;
}
.e-grid .e-groupheadercell,
.e-grid .e-groupheadercell:hover {
  border: 1px solid;
  border-collapse: collapse;
  border-radius: 0;
  cursor: pointer;
  font-size: 14px;
  font-weight: normal;
  height: 26px;
  margin: 8px 0 0 8px;
  overflow: hidden;
  padding: 1px 8px;
  vertical-align: middle;
}
.e-grid .e-groupheadercell:hover {
  border: 1px solid;
  padding: 1px 8px;
}
.e-grid .e-groupheadercell span {
  display: inline-block;
  float: left;
  height: 24px;
  line-height: 22px;
  padding: 0;
  vertical-align: middle;
}
.e-grid .e-groupheadercell .e-grouptext {
  line-height: 22px;
}
.e-grid .e-groupheadercell .e-ungroupbutton {
  font-size: 11px;
  line-height: 25px;
}
.e-grid.e-device .e-groupheadercell span {
  line-height: 26px;
}
.e-grid.e-device .e-groupheadercell .e-ungroupbutton {
  line-height: 28px;
}
.e-grid.e-device .e-groupheadercell .e-groupsort {
  margin-top: 0;
}
.e-grid .e-groupheadercell .e-cancel {
  padding-right: 12px;
}
.e-grid .e-groupheadercell .e-icons::before {
  display: inline;
}
.e-grid .e-groupsort,
.e-grid .e-ungroupbutton,
.e-grid .e-toggleungroup {
  font-size: 12px;
  margin-left: 0;
}
.e-grid .e-groupsort {
  margin-right: 8px;
  margin-top: 0;
}
.e-grid span.e-ungroupbutton.e-icons {
  margin-left: 0;
  margin-top: 0;
  padding-top: 0;
  padding-right: 0;
}
.e-grid .e-grptogglebtn {
  padding: 7px;
}
.e-grid .e-icon-gdownarrow {
  font-size: 12px;
  padding: 16px 8px 13px 9px;
  text-decoration: none;
}
.e-grid .e-icon-grightarrow {
  font-size: 12px;
  padding: 14px 9px 15px 9px;
  text-decoration: none;
}
.e-grid .e-recordplusexpand {
  padding-top: 5px;
}
.e-grid .e-indentcell {
  border-style: solid;
  border-width: 0 1px 0 0;
}
.e-grid .e-indentcell.e-indentcelltop, .e-grid .e-indentcell.e-detailindentcelltop {
  border-width: 1px 0 0;
}
.e-grid .e-recordplusexpand,
.e-grid .e-recordpluscollapse {
  border-style: solid;
  border-width: 1px 0 0;
  cursor: pointer;
}
.e-grid .e-disablepointer {
  cursor: default;
}
.e-grid .e-groupcaption {
  border-style: solid;
  border-width: 1px 0 0;
  display: table-cell;
  font-size: 15px;
  line-height: 20px;
  overflow: hidden;
  padding: 0.7em;
  text-overflow: ellipsis;
  vertical-align: middle;
  white-space: nowrap;
  width: auto;
}
.e-grid .e-virtualtable .e-groupcaption {
  line-height: 21px;
  padding: 7px 8px;
}
.e-grid .e-autofill {
  border: 1px solid;
  height: 8px;
  position: absolute;
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 8px;
}
.e-grid .e-headercontent {
  border-style: solid;
  border-width: 0;
}
.e-grid .e-stackedheadercell {
  border-width: 1px 0 1px 1px;
  white-space: nowrap;
}
.e-grid .e-noselect {
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.e-grid .e-toolbar .e-tbar-btn.e-btn .e-btn-icon.e-icons.e-columnchooser-btn {
  font-size: 9px;
}
.e-grid .e-toolbar .e-toolbar-items .e-toolbar-right .e-cc-toolbar .e-tbar-btn .e-columnchooser-btn {
  margin-top: 3px;
}
.e-grid .e-toolbar-item.e-cc.e-ccdiv.e-cc-toolbar {
  margin-top: -2px;
  padding: 2px 0 0;
}
.e-grid .e-edit-dialog .e-dlg-content {
  position: relative;
}
.e-grid .e-edit-dialog {
  min-height: 350px;
}
.e-grid .e-griderror label {
  display: inline !important;
}
.e-grid .e-tooltip-wrap.e-griderror {
  z-index: 1000;
}
.e-grid .e-tooltip-wrap.e-griderror .e-arrow-tip.e-tip-top {
  left: 44%;
}
.e-grid .e-normaledit .e-rowcell {
  padding-bottom: 0;
  padding-top: 0;
}
.e-grid.e-device .e-normaledit .e-rowcell {
  padding-bottom: 0;
  padding-top: 0;
}
.e-grid .e-gridcontent .e-normaledit .e-rowcell.e-lastrowadded {
  border-bottom: 1px solid #eaeaea;
  border-top: 0 none #eaeaea;
}
.e-grid .e-normaledit {
  border-top: 0;
  padding: 0;
}
.e-grid .e-gridcontent table tbody .e-normaledit .e-rowcell {
  border-top: 1px solid;
}
.e-grid:not(.e-row-responsive, .e-left-shadow, .e-right-shadow) .e-gridcontent tr.e-row:first-child .e-rowcell:not(.e-xlsel-top-border) {
  border-top: 0;
}
.e-grid.e-device .e-editedbatchcell.e-rowcell,
.e-grid .e-editedbatchcell.e-rowcell, .e-grid.e-device .e-gridcontent .e-normaledit .e-rowcell {
  padding-bottom: 0;
  padding-top: 0;
}
.e-grid .e-editedbatchcell,
.e-grid td.e-boolcell {
  padding-left: 8px;
  padding-right: 8px;
}
.e-grid td.e-boolcell input {
  margin: 3px 3px 3px 4px;
}
.e-grid td.e-boolcell.e-rowcell {
  padding-bottom: 5px;
  padding-top: 5px;
}
.e-grid .e-dialog .e-gridform .e-rowcell {
  border: 0;
  padding: 0;
}
.e-grid .e-row .e-input-group .e-input.e-field,
.e-grid .e-row .e-input-focus .e-input.e-field {
  font-family: "Segoe UI", "GeezaPro", "DejaVu Serif", sans-serif, "-apple-system", "BlinkMacSystemFont";
  font-size: 13px;
  padding-bottom: 0;
  padding-top: 0;
}
.e-grid .e-row .e-input-group {
  margin-bottom: 2px;
  margin-top: 2px;
  vertical-align: middle;
}
.e-grid .e-defaultcell.e-ralign,
.e-grid .e-editedrow .e-defaultcell.e-ralign,
.e-grid .e-defaultcell.e-ralign:focus,
.e-grid .e-editedrow .e-defaultcell.e-ralign:focus {
  padding-right: 10px;
}
.e-grid .e-dlg-content .e-defaultcell.e-ralign,
.e-grid .e-dlg-content .e-defaultcell.e-ralign:focus {
  padding-right: 0;
}
.e-grid .e-hiddenrow {
  display: none;
}
.e-grid .e-columnchooserdiv {
  float: right;
  margin: -12px;
}
.e-grid .e-ccdlg .e-dlg-content {
  margin: 22px 0 20px;
  overflow: visible;
  padding: 25px 25px 0 12px;
}
.e-grid .e-ccdlg .e-checkbox-wrapper.e-control.e-keyboard {
  padding-left: 13px;
}
.e-grid .e-ccdlg .e-main-div {
  box-sizing: border-box;
  position: relative;
}
.e-grid .e-ccdlg .e-ccul-ele {
  margin: 13px 0;
  padding: 0;
}
.e-grid .e-ccdlg li.e-cclist {
  padding: 7px 0 6px;
}
.e-grid .e-ccdlg .e-checkbox-wrapper .e-frame {
  margin-left: 0;
}
.e-grid .e-ccdlg label.e-cc {
  margin: 0;
}
.e-grid .e-ccdlg .e-footer-content {
  border-style: solid;
  border-width: 0;
}
.e-grid .e-ccdlg .e-cc-contentdiv {
  height: 196px;
  overflow-y: auto;
}
.e-grid .e-ccdlg .e-cc-searchdiv {
  border-style: solid;
  border-width: 1px;
  left: 6px;
  opacity: 0.6;
  position: absolute;
  right: 0;
  top: -46px;
}
.e-grid .e-ccdlg .e-cc-searchdiv span.e-ccsearch-icon.e-icons {
  float: right;
  opacity: 0.6;
  padding: 2px 2px;
}
.e-grid .e-ccdlg .e-cc-searchdiv span.e-ccsearch-icon.e-cc-cancel {
  font-size: 11px;
  padding: 4px 2px;
}
.e-grid .e-ccdlg .e-cc-searchdiv.e-input-focus {
  opacity: 1;
}
.e-grid .e-ccdlg .e-cc-searchdiv.e-input-focus span.e-ccsearch-icon.e-icons {
  opacity: 1;
}
.e-grid .e-ccdlg .e-innerdiv.e-ccnmdiv {
  height: 60px;
  padding: 23px;
  text-align: center;
}
.e-grid .e-ccdlg .e-checkbox-wrapper .e-label {
  text-overflow: ellipsis;
}
.e-grid .e-ccdlg .e-cc-chbox {
  margin: 3px;
  vertical-align: middle;
}
.e-grid .e-ccdlg .e-cc-lab-name {
  padding: 7px;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.e-grid .e-ccdlg .e-cc.e-input,
.e-grid .e-ccdlg .e-cc.e-input:focus {
  border: 0;
  padding-bottom: 1px;
  padding-left: 4px;
  padding-top: 1px;
}
.e-grid .e-checkboxfiltertext {
  width: 135px;
  word-break: normal;
  /* stylelint-disable */
  /* stylelint-enable */
}
.e-grid .e-frozenhdrcont .e-headercontent > .e-table,
.e-grid .e-frozenhdrcont .e-frozenheader > .e-table,
.e-grid .e-frozenhdrcont .e-movableheader > .e-table,
.e-grid .e-frozenhdrcont .e-headercontent .e-virtualtable > .e-table {
  border-bottom: 2px solid;
}
.e-grid .e-frozenheader > .e-table,
.e-grid .e-frozencontent > .e-table,
.e-grid .e-frozenheader .e-virtualtable > .e-table,
.e-grid .e-frozencontent .e-virtualtable > .e-table {
  border-left: 0;
  border-right: 2px solid;
}
.e-grid .e-frozenheader.e-frozen-right-header > .e-table,
.e-grid .e-frozencontent.e-frozen-right-content > .e-table,
.e-grid .e-rowcell .e-frozen-default-cursor,
.e-grid .e-gridheader .e-headercell .e-frozen-default-cursor,
.e-grid .e-gridheader .e-filterbarcell .e-frozen-default-cursor {
  border-left: 2px solid;
  border-right: 0;
}
.e-grid .e-tooltip-wrap.e-griderror.e-unfreeze {
  z-index: 1;
}
.e-grid .e-leftfreeze,
.e-grid .e-rightfreeze {
  position: sticky;
  z-index: 2;
}
.e-grid .e-fixedfreeze {
  position: sticky;
  z-index: 3;
}
.e-grid .e-xlsel-top-border, .e-grid.e-rtl.e-default .e-rowcell.e-xlsel-top-border {
  border-top-width: 2px;
  line-height: 19px;
}
.e-grid .e-xlsel-left-border,
.e-grid .e-fixedfreeze.e-freezeleftborder.e-xlsel-left-border, .e-grid.e-rtl.e-default .e-rowcell.e-xlsel-left-border, .e-grid.e-rtl.e-default .e-rowcell.e-fixedfreeze.e-freezeleftborder.e-xlsel-left-border {
  border-left-width: 2px;
}
.e-grid .e-xlsel-right-border,
.e-grid .e-fixedfreeze.e-freezerightborder.e-xlsel-right-border, .e-grid.e-rtl.e-default .e-rowcell.e-xlsel-right-border, .e-grid.e-rtl.e-default .e-rowcell.e-fixedfreeze.e-freezerightborder.e-xlsel-right-border {
  border-right-width: 2px;
}
.e-grid.e-wrap .e-xlsel-bottom-border.e-xlsel-top-border {
  line-height: 16px;
}
.e-grid.e-wrap .e-xlsel-bottom-border {
  line-height: 14px;
}
.e-grid.e-rtl.e-wrap .e-xlsel-bottom-border.e-xlsel-top-border {
  line-height: 16px;
}
.e-grid.e-rtl.e-wrap .e-xlsel-bottom-border {
  line-height: 14px;
}
.e-grid .e-xlsel-bottom-border.e-xlsel-top-border {
  line-height: 17px;
}
.e-grid.e-rtl.e-default .e-rowcell.e-xlsel-bottom-border.e-xlsel-top-border {
  line-height: 17px;
}
.e-grid .e-xlsel-bottom-border, .e-grid.e-rtl.e-default .e-rowcell.e-xlsel-bottom-border {
  border-bottom-width: 2px;
  line-height: 19px;
}
.e-grid .e-virtual-content.e-content {
  overflow-x: hidden;
}
.e-grid .e-leftfreeze.e-freezeleftborder {
  border-right-width: 2px;
}
.e-grid.e-left-shadow .e-leftfreeze.e-freezeleftborder:not(.e-dragborder) {
  clip-path: inset(0 -15px 0 0);
  box-shadow: 4px 0 8px rgba(0, 0, 0, 0.12);
}
.e-grid.e-left-shadow .e-leftfreeze.e-freezeleftborder:not(.e-xlsel-right-border) {
  border-right-color: transparent;
}
.e-grid.e-left-shadow .e-leftfreeze.e-freezeleftborder.e-dragborder {
  box-shadow: 0 2px 0 0 #0078d6, 4px 0 8px rgba(0, 0, 0, 0.12);
}
.e-grid.e-rtl.e-left-shadow .e-leftfreeze.e-freezeleftborder:not(.e-dragborder) {
  clip-path: inset(0 0 0 -15px);
  box-shadow: -4px 0 8px rgba(0, 0, 0, 0.12);
}
.e-grid.e-rtl.e-left-shadow .e-leftfreeze.e-freezeleftborder:not(.e-xlsel-left-border) {
  border-left-color: transparent;
}
.e-grid.e-rtl.e-left-shadow .e-leftfreeze.e-freezeleftborder.e-dragborder {
  box-shadow: 0 2px 0 0 #0078d6, -4px 0 8px rgba(0, 0, 0, 0.12);
}
.e-grid .e-rightfreeze.e-freezerightborder {
  border-left-width: 2px;
}
.e-grid.e-right-shadow .e-rightfreeze.e-freezerightborder:not(.e-dragborder) {
  clip-path: inset(0 0 0 -15px);
  box-shadow: -4px 0 8px rgba(0, 0, 0, 0.12);
}
.e-grid.e-right-shadow .e-rightfreeze.e-freezerightborder:not(.e-xlsel-left-border) {
  border-left-color: transparent;
}
.e-grid.e-right-shadow .e-rightfreeze.e-freezerightborder.e-dragborder {
  box-shadow: 0 2px 0 0 #0078d6, -4px 0 8px rgba(0, 0, 0, 0.12);
}
.e-grid.e-rtl.e-right-shadow .e-rightfreeze.e-freezerightborder:not(.e-dragborder) {
  clip-path: inset(0 -15px 0 0);
  box-shadow: 4px 0 8px rgba(0, 0, 0, 0.12);
}
.e-grid.e-rtl.e-right-shadow .e-rightfreeze.e-freezerightborder:not(.e-xlsel-right-border) {
  border-right-color: transparent;
}
.e-grid.e-rtl.e-right-shadow .e-rightfreeze.e-freezerightborder.e-dragborder {
  box-shadow: 0 2px 0 0 #0078d6, 4px 0 8px rgba(0, 0, 0, 0.12);
}
.e-grid .e-fixedfreeze.e-freezeleftborder {
  border-left-width: 1px;
}
.e-grid .e-fixedfreeze.e-freezerightborder {
  border-right-width: 1px;
}
.e-grid .e-frozenheader {
  float: left;
  width: min-content;
}
.e-grid .e-frozenheader.e-frozen-right-header {
  float: right;
}
.e-grid.e-rtl .e-frozenheader > .e-table, .e-grid.e-rtl .e-frozencontent > .e-table, .e-grid.e-rtl .e-frozenheader .e-virtualtable > .e-table, .e-grid.e-rtl .e-frozencontent .e-virtualtable > .e-table {
  border-left: 2px solid;
  border-right: 0;
}
.e-grid.e-rtl .e-frozenheader.e-frozen-right-header > .e-table, .e-grid.e-rtl .e-frozencontent.e-frozen-right-content > .e-table {
  border-left: 0;
  border-right: 2px solid;
}
.e-grid.e-rtl .e-frozenheader {
  float: right;
}
.e-grid.e-rtl .e-frozenheader.e-frozen-right-header {
  float: left;
}
.e-grid.e-rtl .e-frozencontent {
  float: right;
}
.e-grid.e-rtl .e-frozen-right-content {
  float: left;
}
.e-grid.e-rtl .e-frozenfootercontent {
  float: right;
}
.e-grid.e-rtl .e-frozen-right-footercontent {
  float: left;
}
.e-grid .e-movableheader {
  overflow: hidden;
}
.e-grid .e-frozenhdrcont {
  -ms-touch-action: none; /* stylelint-disable-line property-no-vendor-prefix */
}
.e-grid .e-frozencontent {
  -ms-touch-action: none; /* stylelint-disable-line property-no-vendor-prefix */
  border-bottom-width: 1px;
  float: left;
  width: min-content;
}
.e-grid .e-frozen-right-content {
  float: right;
}
.e-grid .e-movablecontent {
  -ms-overflow-style: none; /* stylelint-disable-line property-no-vendor-prefix */
  -ms-flex: 1;
      flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
}
.e-grid:not(.sf-grid) .e-movablecontent::-webkit-scrollbar { /* stylelint-disable-line property-no-vendor-prefix */
  display: none;
}
.e-grid .e-content.e-mac-safari::-webkit-scrollbar { /* stylelint-disable-line property-no-vendor-prefix */
  width: 7px;
}
.e-grid .e-content.e-mac-safari::-webkit-scrollbar-thumb { /* stylelint-disable-line property-no-vendor-prefix */
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
}
.e-grid .e-frozenscrollbar {
  border-top: 1px solid #eaeaea;
}
.e-grid .e-movablescrollbar {
  -ms-flex: 1;
      flex: 1;
  overflow: hidden;
  overflow-x: scroll;
}
.e-grid .e-columnchooser::before {
  line-height: 1.9;
}
.e-grid .e-toolbar .e-ccdiv .e-columnchooser.e-cctbn-icon {
  font-size: 15px;
  vertical-align: middle;
}
.e-grid .e-toolbar .e-ccdiv {
  margin-top: -2px;
  padding: 0 10px;
}
.e-grid.e-rtl .e-tableborder {
  border-left: 1px solid;
  border-right: 0;
}
.e-grid.e-rtl .e-checkboxfilter .e-dlg-content {
  padding-left: 25px;
  padding-right: 18px;
}
.e-grid.e-rtl .e-checkboxfilter .e-ftrchk, .e-grid.e-rtl .e-checkboxfilter .e-searchbox {
  padding-left: 0;
  padding-right: 7px;
}
.e-grid.e-rtl .e-leftfreeze.e-freezeleftborder {
  border-left-width: 2px;
}
.e-grid.e-rtl .e-rightfreeze.e-freezerightborder {
  border-right-width: 2px;
}
.e-grid.e-rtl .e-fixedfreeze.e-freezeleftborder {
  border-right-width: 1px;
}
.e-grid.e-rtl .e-fixedfreeze.e-freezerightborder {
  border-left-width: 1px;
}
.e-grid.e-rtl .e-headercell, .e-grid.e-rtl .e-detailheadercell {
  border-width: 0;
  text-align: right;
}
.e-grid.e-rtl .e-headercell.e-fltr-icon .e-headercelldiv,
.e-grid.e-rtl .e-headercell.e-fltr-icon .e-headercelldiv.e-headerchkcelldiv, .e-grid.e-rtl .e-detailheadercell.e-fltr-icon .e-headercelldiv,
.e-grid.e-rtl .e-detailheadercell.e-fltr-icon .e-headercelldiv.e-headerchkcelldiv {
  padding: 0 0 0 25px;
  text-align: right;
}
.e-grid.e-rtl .e-headercell.e-fltr-icon .e-headercelldiv .e-sortnumber,
.e-grid.e-rtl .e-headercell.e-fltr-icon .e-headercelldiv.e-headerchkcelldiv .e-sortnumber, .e-grid.e-rtl .e-detailheadercell.e-fltr-icon .e-headercelldiv .e-sortnumber,
.e-grid.e-rtl .e-detailheadercell.e-fltr-icon .e-headercelldiv.e-headerchkcelldiv .e-sortnumber {
  float: left;
  margin: 8px 0 0 3px;
}
.e-grid.e-rtl .e-headercell .e-headercelldiv,
.e-grid.e-rtl .e-headercell .e-headercelldiv.e-headerchkcelldiv, .e-grid.e-rtl .e-detailheadercell .e-headercelldiv,
.e-grid.e-rtl .e-detailheadercell .e-headercelldiv.e-headerchkcelldiv {
  padding: 0 0 0 25px;
  text-align: right;
}
.e-grid.e-rtl .e-headercell .e-headercelldiv .e-sortnumber,
.e-grid.e-rtl .e-headercell .e-headercelldiv.e-headerchkcelldiv .e-sortnumber, .e-grid.e-rtl .e-detailheadercell .e-headercelldiv .e-sortnumber,
.e-grid.e-rtl .e-detailheadercell .e-headercelldiv.e-headerchkcelldiv .e-sortnumber {
  float: left;
  margin: 8px 0 0 3px;
}
.e-grid.e-rtl .e-headercell .e-filterbarcell input, .e-grid.e-rtl .e-detailheadercell .e-filterbarcell input {
  border-width: 1px;
}
.e-grid.e-rtl .e-headercell .e-sortfilterdiv, .e-grid.e-rtl .e-detailheadercell .e-sortfilterdiv {
  float: left;
  margin: -21px 0 0 -3px;
}
.e-grid.e-rtl .e-headercell.e-leftalign.e-headercell.e-fltr-icon .e-headercelldiv, .e-grid.e-rtl .e-detailheadercell.e-leftalign.e-headercell.e-fltr-icon .e-headercelldiv {
  margin-left: 10px;
}
.e-grid.e-rtl .e-headercell.e-leftalign.e-headercell.e-fltr-icon .e-headercelldiv .e-sortnumber, .e-grid.e-rtl .e-detailheadercell.e-leftalign.e-headercell.e-fltr-icon .e-headercelldiv .e-sortnumber {
  margin: 8px -10px 0 0;
}
.e-grid.e-rtl .e-headercell.e-leftalign.e-headercell.e-fltr-icon .e-filtermenudiv, .e-grid.e-rtl .e-detailheadercell.e-leftalign.e-headercell.e-fltr-icon .e-filtermenudiv {
  margin-left: -5px;
}
.e-grid.e-rtl .e-headercell.e-leftalign .e-sortfilterdiv, .e-grid.e-rtl .e-detailheadercell.e-leftalign .e-sortfilterdiv {
  float: right;
  margin: -21px 3px;
}
.e-grid.e-rtl .e-headercell.e-leftalign .e-headercelldiv, .e-grid.e-rtl .e-detailheadercell.e-leftalign .e-headercelldiv {
  padding: 0 25px 0 0.7em;
}
.e-grid.e-rtl .e-headercell.e-leftalign .e-headercelldiv .e-sortnumber, .e-grid.e-rtl .e-detailheadercell.e-leftalign .e-headercelldiv .e-sortnumber {
  float: right;
  margin: 7px 2px 0 0;
}
.e-grid.e-rtl .e-headercell.e-fltr-icon.e-rightalign .e-sortnumber, .e-grid.e-rtl .e-detailheadercell.e-fltr-icon.e-rightalign .e-sortnumber {
  float: left;
  margin: 8px 0 0 6px;
}
.e-grid.e-rtl .e-headercell.e-rightalign .e-sortnumber, .e-grid.e-rtl .e-detailheadercell.e-rightalign .e-sortnumber {
  float: left;
  margin: 8px 0 0 6px;
}
.e-grid.e-rtl .e-rowcell:first-child,
.e-grid.e-rtl .e-summarycell:first-child {
  padding-right: 10px;
}
.e-grid.e-rtl .e-rowcell:last-child,
.e-grid.e-rtl .e-summarycell:last-child {
  padding-left: 10px;
}
.e-grid.e-rtl.e-wrap .e-gridheader .e-rightalign .e-sortnumber {
  margin: 3px 5px 0 2px;
}
.e-grid.e-rtl.e-wrap .e-gridheader .e-sortnumber {
  margin: 3px 5px 0 2px;
}
.e-grid.e-rtl.e-wrap .e-gridheader .e-sortfilterdiv {
  margin: -17px 17px;
}
.e-grid.e-rtl.e-wrap .e-gridheader .e-rightalign .e-sortfilterdiv {
  margin: -17px 17px;
}
.e-grid.e-rtl .e-gridheader .e-fltr-icon .e-sortfilterdiv {
  margin: -20px 0 0 10px;
}
.e-grid.e-rtl .e-gridheader .e-rightalign.e-fltr-icon .e-sortfilterdiv {
  margin: -20px 0 0 14px;
}
.e-grid.e-rtl .e-gridheader .e-sortfilter .e-rightalign.e-fltr-icon .e-headercelldiv {
  margin: 0;
}
.e-grid.e-rtl.e-wrap .e-columnheader .e-rightalign.e-fltr-icon .e-headercelldiv, .e-grid.e-rtl .e-columnheader.e-wrap .e-rightalign.e-fltr-icon .e-headercelldiv {
  height: auto;
  margin-bottom: 2px;
  margin-top: 0;
}
.e-grid.e-rtl .e-gridheader .e-sortfilter .e-fltr-icon .e-headercelldiv {
  margin: 0;
}
.e-grid.e-rtl.e-verticallines .e-grouptopleftcell, .e-grid.e-rtl.e-bothlines .e-grouptopleftcell, .e-grid.e-rtl.e-hidelines .e-grouptopleftcell {
  border-top: 0;
}
.e-grid.e-rtl .e-grouptopleftcell {
  border-top: 1px solid;
}
.e-grid.e-rtl .e-groupheadercell span.e-grouptext {
  margin-left: 0;
  margin-right: 8px;
}
.e-grid.e-rtl .e-groupheadercell span {
  float: right;
  padding: 0;
}
.e-grid.e-rtl.e-horizontallines .e-grouptopleftcell {
  border-top: 1px solid;
}
.e-grid.e-rtl .e-rowcell {
  border-width: 1px 0 0;
}
.e-grid.e-rtl .e-rowcell.e-leftfreeze.e-freezeleftborder {
  border-left-width: 2px;
}
.e-grid.e-rtl .e-rowcell.e-rightfreeze.e-freezerightborder {
  border-right-width: 2px;
}
.e-grid.e-rtl .e-rowcell.e-fixedfreeze.e-freezeleftborder:not(.e-xlsel-right-border) {
  border-right-width: 1px;
}
.e-grid.e-rtl .e-rowcell.e-fixedfreeze.e-freezerightborder:not(.e-xlsel-left-border) {
  border-left-width: 1px;
}
.e-grid.e-rtl .e-stackedheadercell.e-leftfreeze.e-freezeleftborder {
  border-left-width: 2px;
}
.e-grid.e-rtl .e-stackedheadercell.e-rightfreeze.e-freezerightborder {
  border-right-width: 2px;
}
.e-grid.e-rtl .e-stackedheadercell.e-fixedfreeze.e-freezeleftborder {
  border-right-width: 1px;
}
.e-grid.e-rtl .e-stackedheadercell.e-fixedfreeze.e-freezerightborder {
  border-left-width: 1px;
}
.e-grid.e-rtl .e-summarycell.e-rightfreeze.e-freezerightborder, .e-grid.e-rtl .e-summarycell.e-fixedfreeze.e-freezeleftborder {
  border-left-width: 0;
}
.e-grid.e-rtl .e-summarycell.e-leftfreeze.e-freezeleftborder, .e-grid.e-rtl .e-summarycell.e-fixedfreeze.e-freezerightborder {
  border-right-width: 0;
}
.e-grid.e-rtl .e-filterbarcell, .e-grid.e-rtl .e-filterbarcelldisabled {
  border-width: 1px 0 0;
}
.e-grid.e-rtl .e-lastrowcell {
  border-width: 1px 1px 1px 0;
}
.e-grid.e-rtl .e-gridheader .e-rightalign .e-sortfilterdiv {
  margin: -21px 0 0 -3px;
}
.e-grid.e-rtl .e-cloneproperties {
  border-width: 1px 1px 3px;
}
.e-grid.e-rtl tr td:first-child, .e-grid.e-rtl tr th:first-child {
  border-left-width: 0;
}
.e-grid.e-rtl.e-default.e-bothlines tr td:first-child:not(.e-summarycell), .e-grid.e-rtl.e-default.e-bothlines tr th:first-child {
  border-left-width: 1px;
}
.e-grid.e-rtl.e-default.e-bothlines tr td:first-child.e-detailrowcollapse, .e-grid.e-rtl.e-default.e-bothlines tr td:first-child.e-detailrowexpand {
  border-left-width: 0;
}
.e-grid.e-rtl.e-default tr td:last-child:not(.e-xlsel-left-border), .e-grid.e-rtl.e-default .e-gridheader table tr th:last-child {
  border-left: 0;
}
.e-grid.e-rtl.e-default.e-verticallines tr th:last-child {
  border-left: 1px solid;
}
.e-grid.e-rtl.e-default .e-gridheader tr th:last-child {
  padding-left: 10px;
  padding-right: 10px;
}
.e-grid.e-rtl.e-default .e-gridheader tr th:first-child {
  padding-left: 10px;
  padding-right: 10px;
}
.e-grid.e-rtl.e-default th:first-child .e-grid.e-rtl.e-default .e-headercell, .e-grid.e-rtl.e-default .e-detailheadercell {
  border-width: 0;
}
.e-grid.e-rtl.e-default .e-rowcell {
  border-width: 1px 0 0;
}
.e-grid.e-rtl.e-default.e-verticallines .e-headercell, .e-grid.e-rtl.e-default.e-verticallines .e-rowcell, .e-grid.e-rtl.e-default.e-verticallines .e-filterbarcell, .e-grid.e-rtl.e-default.e-verticallines .e-detailheadercell, .e-grid.e-rtl.e-default.e-verticallines .e-gridheader th.e-firstcell {
  border-width: 0 0 0 1px;
}
.e-grid.e-rtl.e-default.e-verticallines tr th:first-child:not(.e-firstcell) {
  border-left-width: 1px;
}
.e-grid.e-rtl.e-default .e-stackedheadercell, .e-grid.e-rtl.e-default.e-horizontallines .e-stackedheadercell {
  border-width: 0 1px 1px 0;
}
.e-grid.e-rtl.e-default .e-gridheader th.e-firstcell, .e-grid.e-rtl.e-default.e-horizontallines .e-gridheader th.e-firstcell {
  border-left-width: 0;
  border-right-width: 1px;
}
.e-grid.e-rtl.e-default.e-bothlines .e-gridheader th.e-firstcell {
  border-left-width: 1px;
  border-right-width: 0;
}
.e-grid.e-rtl.e-default .e-gridheader .e-headercell.e-firstheader, .e-grid.e-rtl.e-default.e-horizontallines .e-headercell.e-firstheader {
  border-left: 0;
}
.e-grid.e-rtl.e-default.e-hidelines .e-gridheader th.e-firstcell {
  border-left: 0;
  border-right: 0;
}
.e-grid.e-rtl.e-default .e-gridheader .e-headercell.e-firstheader, .e-grid.e-rtl.e-default.e-horizontallines .e-gridheader .e-headercell.e-firstheader {
  border-right: 1px solid;
}
.e-grid.e-rtl.e-default.e-verticallines .e-gridheader .e-headercell.e-firstheader {
  border-right: 0;
}
.e-grid.e-rtl.e-default.e-verticallines .e-gridheader .e-headercell.e-firstheader, .e-grid.e-rtl.e-default.e-verticallines .e-gridheader th.e-grouptopleftcell.e-lastgrouptopleftcell {
  border-left: 1px solid;
}
.e-grid.e-rtl.e-default.e-verticallines .e-headercell.e-stackedheadercell {
  border-bottom: 1px solid;
}
.e-grid.e-rtl.e-default .e-detailcell, .e-grid.e-rtl.e-default.e-bothlines .e-detailcell {
  border-right-style: solid;
  border-right-width: 1px;
  text-align: right;
}
.e-grid.e-rtl .e-cc-searchdiv span.e-ccsearch-icon.e-icons {
  float: left;
}
.e-grid.e-rtl .e-groupsort, .e-grid.e-rtl .e-ungroupbutton, .e-grid.e-rtl .e-toggleungroup {
  margin-left: 0;
  margin-right: 8px;
}
.e-grid.e-rtl span.e-ungroupbutton.e-icons {
  margin-left: 0;
  margin-right: 6px;
  padding-top: 0;
}
.e-grid.e-rtl:not(.sf-grid).e-default.e-bothlines .e-headercell, .e-grid.e-rtl:not(.sf-grid).e-default.e-bothlines .e-detailheadercell {
  border-width: 0 0 0 1px;
}
.e-grid.e-rtl.e-default.e-bothlines .e-dragheadercell, .e-grid.e-rtl.e-default.e-bothlines .e-rowdragheader, .e-grid.e-rtl.e-default.e-bothlines .e-cloneproperties.e-draganddrop td.e-rowdragdrop, .e-grid.e-rtl.e-default.e-verticallines .e-cloneproperties.e-draganddrop td.e-rowdragdrop {
  border-left: 1px solid #eaeaea;
  padding-left: 3px;
}
.e-grid.e-rtl .e-cloneproperties.e-draganddrop .e-row .e-icon-rowdragicon::before {
  left: 4px;
  position: relative;
}
.e-grid.e-rtl.e-default.e-bothlines .e-gridheader .e-headercell.e-firstheader, .e-grid.e-rtl.e-default.e-bothlines .e-gridheader th.e-grouptopleftcell.e-lastgrouptopleftcell {
  border-left: 1px solid;
  border-right: 0;
}
.e-grid.e-rtl:not(.sf-grid).e-bothlines .e-gridheader th.e-stackedheadercell {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.e-grid.e-rtl.e-bothlines .e-filterbarcell, .e-grid.e-rtl.e-bothlines .e-filterbarcelldisabled {
  border-width: 1px 0 0 1px;
}
.e-grid.e-rtl.e-bothlines .e-rowcell, .e-grid.e-rtl.e-bothlines .e-rowcell.e-lastrowcell {
  border-width: 1px 0 0 1px;
}
.e-grid.e-rtl.e-verticallines .e-rowcell, .e-grid.e-rtl.e-verticallines .e-filterbarcell {
  border-width: 1px 0 0 1px;
}
.e-grid.e-rtl.e-hidelines .e-rowcell, .e-grid.e-rtl.e-hidelines .e-headercell, .e-grid.e-rtl.e-hidelines .e-detailheadercell, .e-grid.e-rtl.e-hidelines .e-filterbarcell {
  border-width: 0;
}
.e-grid.e-rtl.e-horizontallines .e-rowcell {
  border-width: 1px 0 0;
}
.e-grid.e-rtl.e-horizontallines .e-filterbarcell {
  border-width: 1px 0 0;
}
.e-grid.e-rtl.e-horizontallines .e-rowcell.e-lastrowcell:not(.e-xlsel-bottom-border), .e-grid.e-rtl.e-verticallines .e-rowcell.e-lastrowcell:not(.e-xlsel-bottom-border), .e-grid.e-rtl.e-hidelines .e-rowcell.e-lastrowcell:not(.e-xlsel-bottom-border) {
  border-bottom-width: 1px;
}
.e-grid.e-rtl.e-verticallines .e-firstchildrow .e-rowcell, .e-grid.e-rtl.e-hidelines .e-firstchildrow .e-rowcell {
  border-top-width: 1px;
}
.e-grid.e-rtl .e-groupheadercell .e-icons::before {
  display: inline-block;
}
.e-grid.e-rtl .e-groupheadercell .e-cancel {
  padding-left: 23px;
  padding-right: 0;
  padding-top: 2px;
}
.e-grid.e-rtl .e-groupheadercell, .e-grid.e-rtl .e-groupheadercell:hover {
  margin-left: 0;
  margin-right: 8px;
  padding: 1px 0 1px 8px;
}
.e-grid.e-rtl .e-groupheadercell, .e-grid.e-rtl .e-groupheadercell:hover {
  float: right;
}
.e-grid.e-rtl .e-groupdroparea {
  text-align: center;
}
.e-grid.e-rtl .e-ungroupbutton {
  float: left;
}
.e-grid.e-rtl .e-gridcontent table tr:not(.e-summaryrow) td.e-indentcell {
  border-style: solid;
  border-width: 0 0 0 1px;
}
.e-grid.e-rtl .e-defaultcell.e-ralign, .e-grid.e-rtl .e-row .e-input.e-defaultcell.e-ralign, .e-grid.e-rtl .e-defaultcell.e-ralign:focus, .e-grid.e-rtl .e-editedrow .e-defaultcell.e-ralign:focus {
  padding-left: 10px;
}
.e-grid.e-rtl .e-detailindentcell {
  border-left-style: solid;
  border-left-width: 1px;
  border-right-width: 0;
}
.e-grid.e-rtl .e-filtermenudiv {
  float: left;
  margin: -25px 0 -24px -5px;
  padding: 7px;
}
.e-grid:not(.sf-grid).e-rtl.e-default table th[rowspan] {
  border-width: 0 1px 0 0;
}
.e-grid:not(.sf-grid).e-rtl.e-default table th[rowspan].e-leftfreeze.e-freezeleftborder {
  border-left-width: 2px;
}
.e-grid:not(.sf-grid).e-rtl.e-default table th[rowspan].e-rightfreeze.e-freezerightborder {
  border-right-width: 2px;
}
.e-grid:not(.sf-grid).e-rtl.e-default table th[rowspan].e-fixedfreeze.e-freezeleftborder {
  border-right-width: 1px;
}
.e-grid:not(.sf-grid).e-rtl.e-default table th[rowspan].e-fixedfreeze.e-freezerightborder {
  border-left-width: 1px;
}
.e-grid.e-wrap .e-rowcell, .e-grid.e-wrap .e-columnheader .e-stackedheadercelldiv, .e-grid.e-wrap .e-columnheader .e-headercelldiv, .e-grid.e-wrap .e-columnheader .e-headercell.e-fltr-icon .e-headercelldiv {
  height: Auto;
  line-height: 18px;
  overflow-wrap: break-word;
  text-overflow: clip;
  white-space: normal;
  word-wrap: break-word;
}
.e-grid.e-wrap .e-stackedheader .e-columnheader .e-stackedheadercelldiv, .e-grid.e-wrap .e-stackedheader .e-columnheader .e-headercelldiv {
  max-height: 36px;
}
.e-grid.e-wrap .e-columnheader .e-headercelldiv, .e-grid.e-wrap .e-columnheader .e-headercell.e-fltr-icon .e-headercelldiv, .e-grid .e-columnheader.e-wrap .e-headercelldiv, .e-grid .e-columnheader.e-wrap .e-headercell.e-fltr-icon .e-headercelldiv {
  margin-bottom: 2px;
  margin-top: 0;
}
.e-grid.e-wrap .e-columnheader .e-filtermenudiv, .e-grid.e-wrap .e-columnheader .e-sortfilterdiv .e-filtermenudiv {
  line-height: 18px;
}
.e-grid .e-columnheader.e-wrap .e-filtermenudiv, .e-grid .e-columnheader.e-wrap .e-sortfilterdiv .e-filtermenudiv {
  line-height: 18px;
}
.e-grid .e-columnheader.e-wrap .e-headercelldiv, .e-grid .e-columnheader.e-wrap .e-headercell.e-fltr-icon .e-headercelldiv, .e-grid .e-columnheader.e-wrap .e-stackedheadercelldiv, .e-grid .e-gridcontent.e-wrap .e-rowcell, .e-grid .e-frozenhdrcont.e-wrap .e-rowcell {
  height: Auto;
  line-height: 18px;
  overflow-wrap: break-word;
  text-overflow: clip;
  white-space: normal;
  word-wrap: break-word;
}
.e-grid .e-frozenhdrcont.e-wrap .e-xlsel-bottom-border.e-xlsel-top-border {
  line-height: 14px;
}
.e-grid .e-frozenhdrcont.e-wrap .e-xlsel-bottom-border {
  line-height: 16px;
}
.e-grid .e-stackedheadercelldiv {
  overflow: hidden;
  text-overflow: ellipsis;
}
.e-grid .e-columnheader.e-wrap .e-sortfilterdiv, .e-grid.e-wrap .e-columnheader .e-sortfilterdiv {
  margin: -17px 11px 0 0;
}
.e-grid .e-columnheader.e-wrap .e-rightalign .e-sortfilterdiv, .e-grid.e-wrap .e-columnheader .e-rightalign .e-sortfilterdiv {
  margin: -17px 10px 0 0;
}
.e-grid .e-columnheader.e-wrap .e-fltr-icon .e-sortfilterdiv, .e-grid.e-wrap .e-columnheader .e-fltr-icon .e-sortfilterdiv {
  margin: -17px 18px 0 14px;
}
.e-grid .e-columnheader.e-wrap .e-fltr-icon.e-rightalign .e-sortfilterdiv, .e-grid.e-wrap .e-columnheader .e-fltr-icon.e-rightalign .e-sortfilterdiv {
  margin: -17px 14px;
}
.e-grid .e-columnheader.e-wrap .e-icon-group::before, .e-grid.e-wrap .e-columnheader .e-icon-group::before {
  display: inline-block;
}
.e-grid.e-responsive .e-rowcell.e-gridclip, .e-grid.e-responsive .e-gridclip .e-headercelldiv, .e-grid.e-responsive .e-gridclip .e-stackedheadercelldiv {
  text-overflow: clip;
}
.e-grid .e-clipboard {
  cursor: default;
  height: 1px;
  left: -1000px;
  overflow: hidden;
  position: fixed;
  resize: none;
  top: -1000px;
  width: 1px;
}
.e-grid.e-resize-lines th.e-headercell.e-stackedheadercell:not(.e-freezerightborder, .e-freezeleftborder), .e-grid.e-resize-lines th.e-headercell.e-firstcell:not(.e-freezerightborder, .e-freezeleftborder), .e-grid.e-resize-lines.e-rtl th.e-headercell.e-stackedheadercell:not(.e-freezerightborder, .e-freezeleftborder), .e-grid.e-resize-lines.e-rtl th.e-headercell.e-firstcell:not(.e-freezerightborder, .e-freezeleftborder) {
  border-left: 0;
  border-right: 0;
  border-top: 0;
}
.e-grid.e-resize-lines tr.e-columnheader th:last-child.e-stackedheadercell .e-rhandler.e-rcursor :not(.e-laststackcell, .e-freezerightborder, .e-freezeleftborder), .e-grid.e-resize-lines.e-rtl tr.e-columnheader th:last-child.e-stackedheadercell .e-rhandler.e-rcursor :not(.e-laststackcell, .e-freezerightborder, .e-freezeleftborder) {
  border-right: 0;
}
.e-grid:not(.sf-grid).e-resize-lines.e-default table th[rowspan]:not(.e-freezerightborder, .e-freezeleftborder), .e-grid:not(.sf-grid).e-resize-lines.e-rtl.e-default table th[rowspan]:not(.e-freezerightborder, .e-freezeleftborder) {
  border-left: 0;
}
.e-grid.e-resize-lines.e-rtl tr.e-columnheader th:last-child.e-stackedheadercell .e-rhandler.e-rcursor, .e-grid.e-resize-lines.e-rtl tr.e-columnheader th.e-lastcell .e-rhandler.e-rcursor, .e-grid.e-resize-lines.e-rtl tr.e-columnheader th.e-laststackcell .e-rhandler.e-rcursor {
  border-left: 0;
}
.e-grid:not(.sf-grid).e-resize-lines.e-rtl.e-default table th[rowspan] {
  border-right: 0;
}
.e-grid .e-mask {
  display: inline-block;
  height: 10px;
  width: 80%;
}
.e-grid .e-mask.e-mask-group-intent {
  margin-left: 7px;
  width: 16px;
}
.e-grid .e-mask.e-mask-detail-intent {
  width: 16px;
}
.e-grid .e-mask.e-mask-checkbox-intent {
  margin-left: 3px;
  margin-right: 16px;
  width: 20px;
}
.e-grid .e-mask.e-mask-checkbox-filter-intent {
  width: 13px;
}
.e-grid .e-mask.e-mask-checkbox-filter-span-intent {
  margin-left: 8px;
}
.e-grid .e-mask.e-mask-drag-intent {
  margin-left: 12px;
  width: 12px;
}
.e-grid.sf-grid span.e-ungroupbutton.e-icons {
  margin-left: 0;
}
.e-grid.sf-grid .e-rowcell.e-frozen-right-border,
.e-grid.sf-grid .e-headercell.e-frozen-right-border,
.e-grid.sf-grid .e-filterbarcell.e-frozen-right-border {
  border-right: 2px solid rgba(0, 120, 214, 0.6);
}
.e-grid.sf-grid .e-rowcell.e-frozen-left-border,
.e-grid.sf-grid .e-headercell.e-frozen-left-border,
.e-grid.sf-grid .e-filterbarcell.e-frozen-left-border {
  border-left: 2px solid rgba(0, 120, 214, 0.6);
}
.e-grid.sf-grid .e-rowcell.e-freezeline,
.e-grid.sf-grid .e-gridheader .e-filterbarcell.e-freezeline {
  position: relative;
}
.e-grid.sf-grid .e-rowcell .e-frozen-right-cursor,
.e-grid.sf-grid .e-rowcell .e-frozen-left-cursor,
.e-grid.sf-grid .e-gridheader .e-headercell .e-frozen-right-cursor,
.e-grid.sf-grid .e-gridheader .e-headercell .e-frozen-left-cursor,
.e-grid.sf-grid .e-gridheader .e-filterbarcell .e-frozen-right-cursor,
.e-grid.sf-grid .e-gridheader .e-filterbarcell .e-frozen-left-cursor {
  cursor: move;
  height: 100%;
  position: absolute;
  top: 0;
  width: 4px;
}
.e-grid.sf-grid .e-rowcell .e-frozen-left-cursor,
.e-grid.sf-grid .e-gridheader .e-headercell .e-frozen-left-cursor,
.e-grid.sf-grid .e-gridheader .e-filterbarcell .e-frozen-left-cursor {
  right: 0;
}
.e-grid.sf-grid .e-rowcell .e-frozen-right-cursor,
.e-grid.sf-grid .e-gridheader .e-headercell .e-frozen-right-cursor,
.e-grid.sf-grid .e-gridheader .e-filterbarcell .e-frozen-right-cursor {
  left: 0;
}
.e-grid.sf-grid .e-gridheader .e-headercell .e-frozen-left-cursor.e-frozen-resize-cursor,
.e-grid.sf-grid .e-gridheader .e-filterbarcell .e-frozen-left-cursor.e-frozen-resize-cursor,
.e-grid.sf-grid .e-rowcell .e-frozen-left-cursor.e-frozen-resize-cursor {
  right: 3px;
}
.e-grid.sf-grid .e-gridheader .e-headercell .e-frozen-right-cursor.e-frozen-resize-cursor:not(.e-frozen-default-cursor),
.e-grid.sf-grid .e-gridheader .e-filterbarcell .e-frozen-right-cursor.e-frozen-resize-cursor:not(.e-frozen-default-cursor) {
  left: 3px;
}
.e-grid.sf-grid .e-frozen-helper {
  border-left: 2px solid rgba(0, 120, 214, 0.6);
  cursor: move;
  position: absolute;
  z-index: 2;
}
.e-grid.sf-grid .e-content.e-freezeline-moving {
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
.e-grid.sf-grid .e-gridheader .e-headercontent .e-reorderuparrow {
  margin-top: 1px;
}
.e-grid.sf-grid .e-gridheader .e-headercontent .e-reorderuparrow-virtual {
  margin-top: 1px;
}
.e-grid.sf-grid .e-gridheader .e-headercontent .e-reorderdownarrow {
  margin-top: -1px;
}
.e-grid.sf-grid .e-gridheader .e-headercontent .e-reorderdownarrow-virtual {
  margin-top: -1px;
}
.e-grid.sf-grid .e-masked-cell.e-rowcell .e-virtualcell {
  background-color: rgba(0, 0, 0, 0.1);
  display: inline-block;
  height: 10px;
  width: 80%;
}
.e-grid.sf-grid .e-movablecontent {
  height: inherit;
  overflow-x: auto;
  scrollbar-width: none; /* firefox */
}
.e-grid.sf-grid .e-movablecontent::-webkit-scrollbar {
  display: none; /* safari, chrome and edge */
}
.e-grid.sf-grid .e-frozencontent {
  border-bottom-style: solid;
  height: inherit;
}
.e-grid.sf-grid .e-gridcontent .e-content,
.e-grid.sf-grid .e-gridcontent .e-content .e-movablecontent {
  overflow-y: auto;
}
.e-grid.sf-grid .e-gridcontent .e-content.e-yscroll {
  overflow-y: scroll;
}
.e-grid.sf-grid .e-gridcontent .e-content .e-movablecontent.e-yscroll {
  overflow-y: hidden;
}
.e-grid.sf-grid .e-gridcontent .e-content.e-noscroll,
.e-grid.sf-grid .e-gridcontent .e-content .e-movablecontent.e-noscroll {
  overflow-y: hidden;
}
.e-grid.sf-grid .e-label.e-fltrcheck,
.e-grid.sf-grid .e-label.e-choosercheck {
  width: 0;
}
.e-grid.sf-grid .e-checkboxlist .e-fltrcheck,
.e-grid.sf-grid .e-cc-contentdiv .e-choosercheck {
  white-space: nowrap;
}
.e-grid.sf-grid .e-filterdiv .e-multiselect.e-input-group:not(.e-rtl),
.e-grid.sf-grid .e-fltrtempdiv .e-multiselect.e-input-group:not(.e-rtl) {
  text-align: left;
}
.e-grid.sf-grid.e-default table th:not([rowspan="1"]) {
  border-width: 0 0 0 1px;
}
.e-grid.sf-grid .e-gridheader table th:not([rowspan="1"]),
.e-grid.sf-grid .e-device .e-gridheader table th:not([rowspan="1"]) {
  padding-bottom: 4px;
  vertical-align: bottom;
}
.e-grid.sf-grid.e-default table th:not([rowspan="1"]).e-leftfreeze.e-freezeleftborder {
  border-right-width: 2px;
}
.e-grid.sf-grid.e-default table th:not([rowspan="1"]).e-rightfreeze.e-freezerightborder {
  border-left-width: 2px;
}
.e-grid.sf-grid.e-default table th:not([rowspan="1"]).e-fixedfreeze.e-freezeleftborder {
  border-left-width: 1px;
}
.e-grid.sf-grid.e-default table th:not([rowspan="1"]).e-fixedfreeze.e-freezerightborder {
  border-right-width: 1px;
}
.e-grid.sf-grid.e-rtl.e-default table th:not([rowspan="1"]) {
  border-width: 0 1px 0 0;
}
.e-grid.sf-grid.e-rtl.e-default table th:not([rowspan="1"]).e-leftfreeze.e-freezeleftborder {
  border-left-width: 2px;
}
.e-grid.sf-grid.e-rtl.e-default table th:not([rowspan="1"]).e-rightfreeze.e-freezerightborder {
  border-right-width: 2px;
}
.e-grid.sf-grid.e-rtl.e-default table th:not([rowspan="1"]).e-fixedfreeze.e-freezeleftborder {
  border-right-width: 1px;
}
.e-grid.sf-grid.e-rtl.e-default table th:not([rowspan="1"]).e-fixedfreeze.e-freezerightborder {
  border-left-width: 1px;
}
.e-grid.sf-grid.e-resize-lines.e-default table th:not([rowspan="1"]):not(.e-freezerightborder, .e-freezeleftborder), .e-grid.sf-grid.e-resize-lines.e-rtl.e-default table th:not([rowspan="1"]):not(.e-freezerightborder, .e-freezeleftborder) {
  border-left: 0;
}
.e-grid.sf-grid.e-resize-lines.e-rtl.e-default table th:not([rowspan="1"]) {
  border-right: 0;
}
.e-grid.sf-grid.e-rtl.e-bothlines .e-gridheader th.e-stackedheadercell {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}
.e-grid.sf-grid.e-rtl.e-default.e-bothlines .e-headercell, .e-grid.sf-grid.e-rtl.e-default.e-bothlines .e-detailheadercell {
  border-width: 0 0 0 1px;
}
.e-grid.sf-grid .e-bigger .e-grid.sf-grid.e-grid.sf-grid .e-gridheader table th:not([rowspan="1"]), .e-grid.sf-grid .e-bigger .e-grid.sf-grid.e-grid.sf-grid.e-device .e-gridheader table th:not([rowspan="1"]),
.e-grid.sf-grid .e-grid.sf-grid.e-grid.sf-grid.e-bigger .e-gridheader table th:not([rowspan="1"]),
.e-grid.sf-grid .e-grid.sf-grid.e-grid.sf-grid.e-bigger.e-device .e-gridheader table th:not([rowspan="1"]) {
  padding-bottom: 20px;
}

.e-bigger .e-grid .e-gridheader .e-headercontent .e-reorderuparrow,
.e-bigger .e-grid .e-gridheader .e-headercontent .e-reorderdownarrow,
.e-bigger.e-grid .e-gridheader .e-headercontent .e-reorderuparrow,
.e-bigger.e-grid .e-gridheader .e-headercontent .e-reorderdownarrow {
  font-size: 8px;
}
.e-bigger .e-grid .e-gridheader thead .e-icons,
.e-bigger.e-grid .e-gridheader thead .e-icons {
  font-size: 14px;
}
.e-bigger .e-grid .e-ccdlg .e-cc-searchdiv,
.e-bigger.e-grid .e-ccdlg .e-cc-searchdiv {
  padding-left: 0;
  padding-right: 7px;
}
.e-bigger .e-grid .e-columnmenu,
.e-bigger.e-grid .e-columnmenu {
  margin: -24.5px -24px;
}
.e-bigger .e-grid.e-rtl .e-columnmenu,
.e-bigger.e-grid.e-rtl .e-columnmenu {
  margin: -25px -18px -18px 1px;
}
.e-bigger .e-grid.e-rtl.e-device .e-columnmenu,
.e-bigger.e-grid.e-rtl.e-device .e-columnmenu {
  margin: -25px -18px -18px 1px;
}
.e-bigger .e-grid.e-rtl .e-groupheadercell span,
.e-bigger.e-grid.e-rtl .e-groupheadercell span {
  padding: 0;
}
.e-bigger .e-grid.e-device.e-rtl .e-groupheadercell span,
.e-bigger.e-grid.e-device.e-rtl .e-groupheadercell span {
  padding: 0;
}

.e-grid-min-height .e-rowcell,
.e-grid-min-height .e-icon-grightarrow,
.e-grid-min-height .e-icon-gdownarrow {
  line-height: 0;
  padding-bottom: 0;
  padding-top: 0;
}
.e-grid-min-height .e-gridheader .e-headercell,
.e-grid-min-height .e-gridheader .e-detailheadercell,
.e-grid-min-height .e-gridheader .e-headercell .e-headercelldiv {
  height: auto;
}
.e-grid-min-height .e-gridcontent .e-groupcaption {
  line-height: normal;
  padding: 0 0.7em;
}
.e-grid-min-height .e-summarycell {
  line-height: normal;
  padding: 0 8px;
}

.e-grid-min-height .e-grid-height .e-rowcell {
  line-height: 21px;
  padding: 7px 8px;
}
.e-grid-min-height .e-grid-height .e-gridheader .e-headercell,
.e-grid-min-height .e-grid-height .e-gridheader .e-detailheadercell,
.e-grid-min-height .e-grid-height .e-gridheader .e-headercell .e-headercelldiv {
  height: 29px;
}
.e-grid-min-height .e-grid-height .e-gridcontent .e-groupcaption {
  line-height: 20px;
  padding: 0.7em;
}
.e-grid-min-height .e-grid-height .e-summarycell {
  line-height: 21px;
  padding: 7px 8px;
}

.e-device.e-grid-min-height .e-grid-height .e-rowcell {
  padding: 7px 8px;
}

.e-device.e-grid-min-height .e-grid-height .e-rowcell:first-child {
  padding: 7px 8px 7px 10px;
}

.e-device.e-grid-min-height .e-grid-height .e-rowcell:last-child {
  padding: 7px 10px 7px 8px;
}

.e-device.e-grid-min-height .e-rowcell,
.e-device.e-grid-min-height .e-rowcell:first-child,
.e-device.e-grid-min-height .e-rowcell:last-child {
  padding-bottom: 0;
  padding-top: 0;
}

.e-bigger .e-grid.e-grid-min-height .e-grid-height .e-rowcell {
  line-height: 26px;
  padding: 9px 10px;
}

.e-bigger .e-grid.e-grid-min-height .e-grid-height .e-rowcell:first-child {
  line-height: 13px;
  padding-left: 13px;
}

.e-bigger .e-grid.e-grid-min-height .e-grid-height .e-rowcell:last-child {
  line-height: 13px;
  padding-right: 13px;
}

.e-bigger .e-grid.e-grid-min-height .e-rowcell,
.e-bigger .e-grid.e-grid-min-height .e-rowcell:first-child,
.e-bigger .e-grid.e-grid-min-height .e-rowcell:last-child {
  line-height: 0;
  padding-bottom: 0;
  padding-top: 0;
}

.e-bigger .e-wrap.e-grid-min-height .e-grid-height .e-rowcell {
  line-height: 18px;
}
.e-bigger .e-wrap.e-grid-min-height .e-grid-height .e-frozencontent table tr td:first-child:empty,
.e-bigger .e-wrap.e-grid-min-height .e-grid-height .e-movablecontent table tr td:first-child:empty,
.e-bigger .e-wrap.e-grid-min-height .e-grid-height .e-frozenhdrcont table tr td:first-child:empty, .e-bigger .e-wrap.e-grid-min-height .e-grid-height:not(.e-grid-min-height) .e-gridcontent tr td:not(.e-indentcell):first-child:empty, .e-bigger .e-wrap.e-grid-min-height .e-grid-height:not(.e-grid-min-height) .e-gridcontent tr.e-row .e-rowcell:empty {
  height: 44px;
}

.e-bigger .e-wrap.e-grid-min-height .e-rowcell {
  line-height: 18px;
}
.e-bigger .e-wrap.e-grid-min-height .e-frozencontent table tr td:first-child:empty,
.e-bigger .e-wrap.e-grid-min-height .e-movablecontent table tr td:first-child:empty,
.e-bigger .e-wrap.e-grid-min-height .e-frozenhdrcont table tr td:first-child:empty, .e-bigger .e-wrap.e-grid-min-height:not(.e-grid-min-height) .e-gridcontent tr td:not(.e-indentcell):first-child:empty, .e-bigger .e-wrap.e-grid-min-height:not(.e-grid-min-height) .e-gridcontent tr.e-row .e-rowcell:empty {
  height: 18px;
}

.e-rtl .e-grid .e-headercell, .e-rtl .e-grid .e-detailheadercell, .e-rtl .e-grid .e-headercelldiv, .e-rtl .e-grid .e-headercelldiv.e-headerchkcelldiv {
  text-align: right;
}

.e-edit-dialog .e-gridform .e-table {
  border-collapse: separate;
  border-spacing: 11px;
  width: 100%;
}

.e-edit-dialog .e-dlg-content {
  position: relative;
}

.e-bigger .e-grid.e-row-responsive .e-toolbar .e-tbar-btn:hover,
.e-bigger .e-grid.e-row-responsive .e-toolbar .e-tbar-btn:active,
.e-bigger .e-grid.e-row-responsive .e-toolbar .e-tbar-btn:focus,
.e-bigger.e-grid.e-row-responsive .e-toolbar .e-tbar-btn:hover,
.e-bigger.e-grid.e-row-responsive .e-toolbar .e-tbar-btn:active,
.e-bigger.e-grid.e-row-responsive .e-toolbar .e-tbar-btn:focus {
  background: none;
}
.e-bigger .e-grid.e-row-responsive .e-toolbar .e-tbar-btn:hover .e-icons,
.e-bigger .e-grid.e-row-responsive .e-toolbar .e-tbar-btn:active .e-icons,
.e-bigger .e-grid.e-row-responsive .e-toolbar .e-tbar-btn:focus .e-icons,
.e-bigger.e-grid.e-row-responsive .e-toolbar .e-tbar-btn:hover .e-icons,
.e-bigger.e-grid.e-row-responsive .e-toolbar .e-tbar-btn:active .e-icons,
.e-bigger.e-grid.e-row-responsive .e-toolbar .e-tbar-btn:focus .e-icons {
  padding-bottom: 0;
}
.e-bigger .e-grid.e-row-responsive .e-toolbar .e-search-icon,
.e-bigger.e-grid.e-row-responsive .e-toolbar .e-search-icon {
  background: none;
}
.e-bigger .e-grid.e-row-responsive .e-responsive-header,
.e-bigger.e-grid.e-row-responsive .e-responsive-header {
  border-top: 1px solid;
  border-top-color: #eaeaea;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent td::before, .e-bigger .e-grid.e-row-responsive .e-summarycontent td::before,
.e-bigger.e-grid.e-row-responsive .e-gridcontent td::before,
.e-bigger.e-grid.e-row-responsive .e-summarycontent td::before {
  content: attr(data-cell);
  font-weight: 500;
  left: 13px;
  padding-right: 10px;
  position: absolute;
  top: 6px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 45%;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent .e-verticalwrap td::before, .e-bigger .e-grid.e-row-responsive .e-summarycontent .e-verticalwrap td::before,
.e-bigger.e-grid.e-row-responsive .e-gridcontent .e-verticalwrap td::before,
.e-bigger.e-grid.e-row-responsive .e-summarycontent .e-verticalwrap td::before {
  line-height: 18px;
  white-space: normal;
  word-wrap: break-word;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent td[data-cell], .e-bigger .e-grid.e-row-responsive .e-summarycontent td[data-cell],
.e-bigger.e-grid.e-row-responsive .e-gridcontent td[data-cell],
.e-bigger.e-grid.e-row-responsive .e-summarycontent td[data-cell] {
  padding-bottom: 5px;
  padding-left: 55%;
  padding-top: 5px;
  position: relative;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent .e-input, .e-bigger .e-grid.e-row-responsive .e-summarycontent .e-input,
.e-bigger.e-grid.e-row-responsive .e-gridcontent .e-input,
.e-bigger.e-grid.e-row-responsive .e-summarycontent .e-input {
  display: block;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent td, .e-bigger .e-grid.e-row-responsive .e-summarycontent td,
.e-bigger.e-grid.e-row-responsive .e-gridcontent td,
.e-bigger.e-grid.e-row-responsive .e-summarycontent td {
  border: 0;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent .e-row, .e-bigger .e-grid.e-row-responsive .e-summarycontent .e-row,
.e-bigger.e-grid.e-row-responsive .e-gridcontent .e-row,
.e-bigger.e-grid.e-row-responsive .e-summarycontent .e-row {
  border-bottom: 1px solid #eaeaea;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent .e-normaledit .e-rowcell, .e-bigger .e-grid.e-row-responsive .e-summarycontent .e-normaledit .e-rowcell,
.e-bigger.e-grid.e-row-responsive .e-gridcontent .e-normaledit .e-rowcell,
.e-bigger.e-grid.e-row-responsive .e-summarycontent .e-normaledit .e-rowcell {
  height: auto;
  padding-left: 1%;
  padding-right: 1%;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent td.e-rowcell, .e-bigger .e-grid.e-row-responsive .e-summarycontent td.e-rowcell,
.e-bigger.e-grid.e-row-responsive .e-gridcontent td.e-rowcell,
.e-bigger.e-grid.e-row-responsive .e-summarycontent td.e-rowcell {
  border-bottom: 0;
  border-bottom-color: #eaeaea;
  height: auto;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent .e-emptyrow td.e-lastrowcell, .e-bigger .e-grid.e-row-responsive .e-summarycontent .e-emptyrow td.e-lastrowcell,
.e-bigger.e-grid.e-row-responsive .e-gridcontent .e-emptyrow td.e-lastrowcell,
.e-bigger.e-grid.e-row-responsive .e-summarycontent .e-emptyrow td.e-lastrowcell {
  border-bottom: 0;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent .e-normaledit :not(.e-responsive-editcell).e-rowcell, .e-bigger .e-grid.e-row-responsive .e-summarycontent .e-normaledit :not(.e-responsive-editcell).e-rowcell,
.e-bigger.e-grid.e-row-responsive .e-gridcontent .e-normaledit :not(.e-responsive-editcell).e-rowcell,
.e-bigger.e-grid.e-row-responsive .e-summarycontent .e-normaledit :not(.e-responsive-editcell).e-rowcell {
  border-top-width: 0;
}
.e-bigger .e-grid.e-row-responsive .e-gridcontent .e-normaledit .e-rowcell::before, .e-bigger .e-grid.e-row-responsive .e-summarycontent .e-normaledit .e-rowcell::before,
.e-bigger.e-grid.e-row-responsive .e-gridcontent .e-normaledit .e-rowcell::before,
.e-bigger.e-grid.e-row-responsive .e-summarycontent .e-normaledit .e-rowcell::before {
  bottom: 6px;
  position: static;
}
.e-bigger .e-grid.e-row-responsive .e-table,
.e-bigger.e-grid.e-row-responsive .e-table {
  display: block;
}
.e-bigger .e-grid.e-row-responsive .e-table tbody,
.e-bigger .e-grid.e-row-responsive .e-table tr,
.e-bigger .e-grid.e-row-responsive .e-table td:not(.e-hide),
.e-bigger .e-grid.e-row-responsive .e-table tfoot,
.e-bigger.e-grid.e-row-responsive .e-table tbody,
.e-bigger.e-grid.e-row-responsive .e-table tr,
.e-bigger.e-grid.e-row-responsive .e-table td:not(.e-hide),
.e-bigger.e-grid.e-row-responsive .e-table tfoot {
  display: block;
}
.e-bigger .e-grid.e-row-responsive > .e-gridheader,
.e-bigger.e-grid.e-row-responsive > .e-gridheader {
  display: none;
}
.e-bigger .e-grid.e-row-responsive .e-gridfooter,
.e-bigger.e-grid.e-row-responsive .e-gridfooter {
  padding-right: 0;
}

.e-bigger .e-grid.e-row-responsive.e-hidelines .e-gridcontent .e-rowcell:last-child,
.e-bigger.e-grid.e-row-responsive.e-hidelines .e-gridcontent .e-rowcell:last-child,
.e-bigger.e-grid.e-row-responsive.e-verticallines .e-gridcontent .e-rowcell:last-child,
.e-bigger .e-grid.e-row-responsive.e-verticallines .e-gridcontent .e-rowcell:last-child {
  border: 0;
}

.e-bigger .e-responsive-dialog.e-customfilter .e-responsivecoldiv,
.e-bigger.e-responsive-dialog.e-customfilter .e-responsivecoldiv {
  cursor: pointer;
}

.e-bigger .e-responsive-dialog .e-btn.e-ripple,
.e-bigger.e-responsive-dialog .e-btn.e-ripple {
  overflow: initial;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content,
.e-bigger.e-responsive-dialog .e-dlg-header-content {
  background: #fff;
  border-bottom: 1px solid;
  border-bottom-color: #eaeaea;
  padding: 16px;
  width: 100%;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-btn,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-btn {
  background: none;
  box-shadow: none;
  outline: none;
  position: initial;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn {
  background-color: transparent;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-btn .e-btn-icon,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-btn .e-btn-icon {
  color: #333;
  font-size: 17px;
  margin-top: 0;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-btn:hover .e-btn-icon, .e-bigger .e-responsive-dialog .e-dlg-header-content .e-btn:focus .e-btn-icon,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-btn:hover .e-btn-icon,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-btn:focus .e-btn-icon {
  color: #333;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-btn.e-dlg-closeicon-btn {
  float: left;
  padding-left: 0;
  padding-right: 40px;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-dlg-header,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-dlg-header {
  width: auto;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element {
  display: -ms-flexbox;
  display: flex;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-dlg-custom-header,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-dlg-custom-header {
  padding-top: 8px;
  width: 100%;
  font-weight: 500;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-filter-clear-btn,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-filter-clear-btn {
  color: #0078d6;
  margin-top: 5px;
  padding: 0;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-filter-clear-btn .e-icon-filter-clear,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-filter-clear-btn .e-icon-filter-clear {
  font-size: 18px;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-sort-clear-btn,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-sort-clear-btn {
  color: #0078d6;
  font-size: 19px;
  font-weight: 500;
  padding-top: 5px;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-apply-btn,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-apply-btn {
  font-size: 19px;
  font-weight: 500;
  padding: 0;
  padding-left: 20px;
  padding-right: 0;
  padding-top: 3px;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-apply-btn:disabled,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-apply-btn:disabled {
  color: #f4f4f4;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-btn,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-btn {
  background: none;
  border: 0;
  color: #0078d6;
}
.e-bigger .e-responsive-dialog .e-dlg-header-content .e-res-back-btn,
.e-bigger.e-responsive-dialog .e-dlg-header-content .e-res-back-btn {
  background: none;
  box-shadow: none;
  padding-left: 0;
}
.e-bigger .e-responsive-dialog.e-rtl .e-dlg-header-content .e-res-back-btn,
.e-bigger.e-responsive-dialog.e-rtl .e-dlg-header-content .e-res-back-btn {
  padding-left: 15px;
  padding-right: 0;
}
.e-bigger .e-responsive-dialog .e-dlg-content,
.e-bigger.e-responsive-dialog .e-dlg-content {
  padding: 16px;
  padding-top: 0;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-checkfltrnmdiv,
.e-bigger.e-responsive-dialog .e-dlg-content .e-checkfltrnmdiv {
  text-align: center;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-filtersetdiv,
.e-bigger.e-responsive-dialog .e-dlg-content .e-filtersetdiv {
  float: right;
  margin-right: 1%;
  margin-top: 1%;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-filtersetdiv .e-filterset,
.e-bigger.e-responsive-dialog .e-dlg-content .e-filtersetdiv .e-filterset {
  color: #0078d6;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-dialog,
.e-bigger.e-responsive-dialog .e-dlg-content .e-dialog {
  box-shadow: none;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-excelfilter,
.e-bigger.e-responsive-dialog .e-dlg-content .e-excelfilter {
  border: transparent;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-excelfilter .e-dlg-content,
.e-bigger.e-responsive-dialog .e-dlg-content .e-excelfilter .e-dlg-content {
  background-color: #fff;
  padding: 8px;
  padding-right: 16px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-excelfilter .e-checkboxlist > span,
.e-bigger.e-responsive-dialog .e-dlg-content .e-excelfilter .e-checkboxlist > span {
  padding-left: 9px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-xlflmenu,
.e-bigger.e-responsive-dialog .e-dlg-content .e-xlflmenu {
  border: transparent;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-xlflmenu .e-dlg-content,
.e-bigger.e-responsive-dialog .e-dlg-content .e-xlflmenu .e-dlg-content {
  padding: 16px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-ressortbutton-parent,
.e-bigger.e-responsive-dialog .e-dlg-content .e-ressortbutton-parent {
  float: right;
  margin-top: -9px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-ressortbutton-parent .e-ressortbutton,
.e-bigger.e-responsive-dialog .e-dlg-content .e-ressortbutton-parent .e-ressortbutton {
  background: none;
  width: 120px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-responsivecoldiv,
.e-bigger.e-responsive-dialog .e-dlg-content .e-responsivecoldiv {
  font-size: 16px;
  margin-bottom: 26px;
  margin-top: 26px;
  width: 100%;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog {
  bottom: 0;
  box-shadow: none;
  width: 100%;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul {
  background-color: #fff;
  max-width: 100%;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul li,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul li {
  color: #333333;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul .e-submenu.e-selected,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul .e-submenu.e-selected {
  background-color: #fff;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul .e-submenu,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul .e-submenu {
  padding: 0;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul .e-submenu .e-menu-icon,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul .e-submenu .e-menu-icon {
  margin-right: 4px;
  margin-left: 15px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul .e-submenu .e-caret,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-contextmenu-wrapper ul .e-submenu .e-caret {
  padding: 0;
  padding-right: 8px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer {
  height: 88%;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-searchbox,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-searchbox {
  padding-left: 10px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-searchbox .e-search-icon:focus, .e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-searchbox .e-search-icon:active, .e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-searchbox .e-search-icon:hover,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-searchbox .e-search-icon:focus,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-searchbox .e-search-icon:active,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-searchbox .e-search-icon:hover {
  background: none;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-spinner,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-spinner {
  height: 100%;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-spinner .e-chk-hidden,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-spinner .e-chk-hidden {
  -moz-appearance: none; /* stylelint-disable-line property-no-vendor-prefix */
  height: 1px;
  opacity: 0;
  width: 1px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-spinner .e-checkboxlist,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-spinner .e-checkboxlist {
  height: 100%;
  min-height: 160px;
  overflow-y: auto;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-spinner .e-checkboxlist .e-ftrchk,
.e-bigger.e-responsive-dialog .e-dlg-content .e-mainfilterdiv .e-dialog .e-searchcontainer .e-spinner .e-checkboxlist .e-ftrchk {
  padding-bottom: 12px;
  padding-top: 12px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-checkboxfilter .e-dlg-content,
.e-bigger.e-responsive-dialog .e-dlg-content .e-checkboxfilter .e-dlg-content {
  padding: 8px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-checkboxfilter .e-dlg-content .e-searchcontainer,
.e-bigger.e-responsive-dialog .e-dlg-content .e-checkboxfilter .e-dlg-content .e-searchcontainer {
  padding-left: 8px;
  margin-right: 7px;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-checkboxfilter .e-dlg-content .e-searchcontainer .e-searchbox,
.e-bigger.e-responsive-dialog .e-dlg-content .e-checkboxfilter .e-dlg-content .e-searchcontainer .e-searchbox {
  padding-left: 0;
}
.e-bigger .e-responsive-dialog .e-dlg-content .e-checkboxfilter .e-dlg-content .e-searchcontainer .e-spinner .e-checkboxlist,
.e-bigger.e-responsive-dialog .e-dlg-content .e-checkboxfilter .e-dlg-content .e-searchcontainer .e-spinner .e-checkboxlist {
  margin-left: -7px;
}
.e-bigger .e-responsive-dialog .e-res-contextmenu-wrapper .e-contextmenu,
.e-bigger.e-responsive-dialog .e-res-contextmenu-wrapper .e-contextmenu {
  background-color: #fff;
  border: transparent;
  box-shadow: none;
  margin-top: 23px;
  max-width: 100%;
  min-width: 100%;
  overflow-y: auto;
  padding: 16px;
  padding-top: 0;
}
.e-bigger .e-responsive-dialog .e-res-contextmenu-wrapper .e-contextmenu .e-menu-item,
.e-bigger.e-responsive-dialog .e-res-contextmenu-wrapper .e-contextmenu .e-menu-item {
  padding: 0;
}
.e-bigger .e-responsive-dialog .e-res-contextmenu-wrapper .e-contextmenu .e-menu-item.e-focused,
.e-bigger.e-responsive-dialog .e-res-contextmenu-wrapper .e-contextmenu .e-menu-item.e-focused {
  background-color: transparent;
}
.e-bigger .e-responsive-dialog .e-defaultcell.e-ralign, .e-bigger .e-responsive-dialog .e-editedrow .e-defaultcell.e-ralign, .e-bigger .e-responsive-dialog .e-insertedrow .e-defaultcell.e-ralign, .e-bigger .e-responsive-dialog .e-defaultcell.e-ralign:focus, .e-bigger .e-responsive-dialog .e-insertedrow .e-defaultcell.e-ralign:focus .e-bigger .e-responsive-dialog .e-editedrow .e-defaultcell.e-ralign:focus,
.e-bigger.e-responsive-dialog .e-defaultcell.e-ralign,
.e-bigger.e-responsive-dialog .e-editedrow .e-defaultcell.e-ralign,
.e-bigger.e-responsive-dialog .e-insertedrow .e-defaultcell.e-ralign,
.e-bigger.e-responsive-dialog .e-defaultcell.e-ralign:focus,
.e-bigger .e-responsive-dialog .e-insertedrow .e-defaultcell.e-ralign:focus .e-bigger.e-responsive-dialog .e-editedrow .e-defaultcell.e-ralign:focus,
.e-bigger.e-responsive-dialog .e-insertedrow .e-defaultcell.e-ralign:focus .e-bigger .e-responsive-dialog .e-editedrow .e-defaultcell.e-ralign:focus,
.e-bigger.e-responsive-dialog .e-insertedrow .e-defaultcell.e-ralign:focus .e-bigger.e-responsive-dialog .e-editedrow .e-defaultcell.e-ralign:focus {
  padding-right: 10px;
}

.e-bigger .e-responsive-dialog.e-ressortdiv .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-apply-btn,
.e-bigger.e-responsive-dialog.e-ressortdiv .e-dlg-header-content .e-dlg-header .e-res-custom-element .e-res-apply-btn {
  padding-top: 0;
}

.e-bigger .e-responsive-dialog.e-rtl .e-btn.e-dlg-closeicon-btn,
.e-bigger.e-responsive-dialog.e-rtl .e-btn.e-dlg-closeicon-btn {
  float: right;
  padding-left: 40px;
  padding-right: 0;
}
.e-bigger .e-responsive-dialog.e-rtl .e-res-apply-btn,
.e-bigger.e-responsive-dialog.e-rtl .e-res-apply-btn {
  padding-left: 0;
  padding-right: 20px;
}
.e-bigger .e-responsive-dialog.e-rtl .e-ressortbutton-parent,
.e-bigger.e-responsive-dialog.e-rtl .e-ressortbutton-parent {
  float: left;
}

/* stylelint-disable */
.e-ddl.e-popup.e-popup-flmenu .e-dropdownbase,
.e-ddl.e-popup.e-popup-flbar .e-dropdownbase {
  max-height: 298px !important;
}

/* stylelint-enable */
/* Apply styles for Firefox only */
/* stylelint-disable function-url-quotes */
@-moz-document url-prefix() {
  .e-grid-min-height .e-rowcell,
  .e-grid-min-height .e-icon-grightarrow,
  .e-grid-min-height .e-icon-gdownarrow {
    line-height: normal;
  }
  .e-bigger .e-grid.e-grid-min-height .e-rowcell,
  .e-bigger .e-grid.e-grid-min-height .e-rowcell:first-child,
  .e-bigger .e-grid.e-grid-min-height .e-rowcell:last-child {
    line-height: normal;
  }
}
/* stylelint-enable function-url-quotes */
/*! Grid theme */
.e-grid {
  border-color: #eaeaea;
}
.e-grid .e-content {
  background-color: #fff;
}
.e-grid .e-icons:not(.e-btn-icon) {
  color: #333;
}
.e-grid .e-unboundcelldiv .e-icons:not(.e-btn-icon) {
  color: #333;
}
.e-grid .e-table {
  background-color: #fff;
}
.e-grid .e-focused:not(.e-menu-item):not(.e-editedbatchcell) {
  box-shadow: 0 0 0 1px #666 inset;
}
.e-grid.e-left-shadow .e-leftfreeze.e-freezeleftborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border), .e-grid.e-right-shadow .e-rightfreeze.e-freezerightborder.e-focused:not(.e-menu-item, .e-xlsel-top-border, .e-xlsel-left-border, .e-xlsel-bottom-border, .e-xlsel-right-border) {
  border-color: #666;
}
.e-grid .e-gridheader .e-icons:not(.e-icon-hide):not(.e-check):not(.e-stop):not(.e-icon-reorderuparrow):not(.e-icon-reorderdownarrow) {
  color: #333;
}
.e-grid .e-gridheader .e-headercontent .e-icon-reorderuparrow,
.e-grid .e-gridheader .e-headercontent .e-icon-reorderdownarrow {
  color: #333;
}
.e-grid .e-groupdroparea .e-icons {
  color: #333;
}
.e-grid .e-tableborder {
  border-right-color: #eaeaea;
}
.e-grid .e-autofill {
  background-color: #0078d6;
}
.e-grid .e-autofill,
.e-grid .e-xlsel {
  border-color: #0078d6;
  z-index: 1;
}
.e-grid .e-autofill.e-freeze-autofill {
  z-index: 5;
}
.e-grid .e-xlsel {
  background-color: transparent;
  border-style: solid;
  pointer-events: none;
  position: absolute;
}
.e-grid .e-xlselaf {
  background-color: #0078d6;
  border-color: #0078d6;
  position: absolute;
  z-index: 3;
}
.e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder,
.e-grid .e-gridcontent .e-rowcell.e-dragborder,
.e-grid .e-gridcontent .e-groupcaption.e-dragborder,
.e-grid .e-gridcontent .e-summarycell.e-dragborder,
.e-grid .e-gridcontent .e-rowdragdrop.e-dragborder, .e-grid .e-gridheader thead tr th.e-firstrowdragborder, .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder, .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder {
  box-shadow: 0 2px 0 0 #0078d6;
  z-index: 5;
}
.e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-leftfreeze,
.e-grid .e-gridcontent .e-rowcell.e-dragborder.e-leftfreeze,
.e-grid .e-gridcontent .e-groupcaption.e-dragborder.e-leftfreeze,
.e-grid .e-gridcontent .e-summarycell.e-dragborder.e-leftfreeze,
.e-grid .e-gridcontent .e-rowdragdrop.e-dragborder.e-leftfreeze, .e-grid .e-gridheader thead tr th.e-firstrowdragborder.e-leftfreeze, .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder.e-leftfreeze, .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder.e-leftfreeze {
  z-index: 6;
}
.e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-rightfreeze,
.e-grid .e-gridcontent .e-rowcell.e-dragborder.e-rightfreeze,
.e-grid .e-gridcontent .e-groupcaption.e-dragborder.e-rightfreeze,
.e-grid .e-gridcontent .e-summarycell.e-dragborder.e-rightfreeze,
.e-grid .e-gridcontent .e-rowdragdrop.e-dragborder.e-rightfreeze, .e-grid .e-gridheader thead tr th.e-firstrowdragborder.e-rightfreeze, .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder.e-rightfreeze, .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder.e-rightfreeze {
  z-index: 6;
}
.e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-fixedfreeze,
.e-grid .e-gridcontent .e-rowcell.e-dragborder.e-fixedfreeze,
.e-grid .e-gridcontent .e-groupcaption.e-dragborder.e-fixedfreeze,
.e-grid .e-gridcontent .e-summarycell.e-dragborder.e-fixedfreeze,
.e-grid .e-gridcontent .e-rowdragdrop.e-dragborder.e-fixedfreeze, .e-grid .e-gridheader thead tr th.e-firstrowdragborder.e-fixedfreeze, .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder.e-fixedfreeze, .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder.e-fixedfreeze, .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder.e-fixedfreeze {
  z-index: 6;
}
.e-grid .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze),
.e-grid .e-gridcontent .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze),
.e-grid .e-gridcontent .e-groupcaption.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze),
.e-grid .e-gridcontent .e-summarycell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze),
.e-grid .e-gridcontent .e-rowdragdrop.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid .e-gridheader thead tr th.e-firstrowdragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridcontent tr.e-row:first-child .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridcontent .e-rowcell.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze), .e-grid.e-rtl .e-gridcontent .e-rowdragdrop.e-dragborder:not(.e-leftfreeze, .e-rightfreeze, .e-fixedfreeze) {
  position: relative;
}
.e-grid .e-gridheader {
  background-color: #fff;
  border-bottom-color: #eaeaea;
  border-top-color: #eaeaea;
  color: #666;
}
.e-grid .e-gridcontent tr:first-child td {
  border-top-color: transparent;
}
.e-grid .e-gridcontent tr:first-child td.e-xlsel-top-border {
  border-top-color: #0078d6;
}
.e-grid th.e-headercell[aria-sort=ascending] .e-headertext,
.e-grid th.e-headercell[aria-sort=descending] .e-headertext,
.e-grid th.e-headercell[aria-sort=ascending] .e-sortfilterdiv,
.e-grid th.e-headercell[aria-sort=descending] .e-sortfilterdiv {
  color: #666;
  opacity: 1;
}
.e-grid.e-default.e-verticallines .e-headercell.e-stackedheadercell {
  border-color: #eaeaea;
}
.e-grid.e-default.e-horizontallines .e-grouptopleftcell {
  border-color: #eaeaea;
}
.e-grid.e-default .e-gridheader.e-stackedfilter tr:last-child th.e-grouptopleftcell, .e-grid.e-default.e-horizontallines .e-gridheader.e-stackedfilter tr:last-child th.e-grouptopleftcell, .e-grid.e-default .e-gridheader.e-stackedfilter tr:first-child th.e-grouptopleftcell, .e-grid.e-default.e-horizontallines .e-gridheader.e-stackedfilter tr:first-child th.e-grouptopleftcell {
  border-color: #eaeaea;
}
.e-grid.e-default .e-gridheader .e-headercell.e-firstheader, .e-grid.e-default.e-horizontallines .e-headercell.e-firstheader {
  border-color: #eaeaea;
}
.e-grid .e-filterbarcell input {
  border-color: #eaeaea;
}
.e-grid .e-gridcontent {
  background-color: #fff;
}
.e-grid .e-gridfooter {
  background-color: #f6f6f6;
}
.e-grid .e-headercontent {
  border-color: #eaeaea;
}
.e-grid .e-rowdragheader {
  background-color: #fff;
}
.e-grid .e-headercell,
.e-grid .e-detailheadercell {
  background-color: #fff;
  border-color: #eaeaea;
}
.e-grid [aria-selected] + tr .e-detailindentcell {
  border-color: #eaeaea;
}
.e-grid tr th.e-firstcell {
  border-left-color: #eaeaea;
  border-right-color: #eaeaea;
}
.e-grid .e-rowcell:not(.e-editedbatchcell):not(.e-updatedtd),
.e-grid .e-detailrowcollapse:not(.e-editedbatchcell):not(.e-updatedtd),
.e-grid .e-detailrowexpand:not(.e-editedbatchcell):not(.e-updatedtd),
.e-grid .e-gridcontent .e-rowdragdrop:not(.e-editedbatchcell):not(.e-updatedtd),
.e-grid .e-gridheader .e-rowdragdrop:not(.e-editedbatchcell):not(.e-updatedtd),
.e-grid .e-emptyrow:not(.e-editedbatchcell):not(.e-updatedtd) {
  color: #333333;
}
.e-grid .e-summarycell {
  background-color: #fff;
  border-color: #eaeaea;
  color: #666;
}
.e-grid .e-summaryrow .e-summarycell,
.e-grid .e-summaryrow .e-templatecell,
.e-grid .e-summarycontent .e-indentcell,
.e-grid .e-indentcell.e-detailindentcelltop,
.e-grid .e-groupfooterrow.e-summaryrow .e-indentcell.e-indentcelltop {
  background-color: #f6f6f6;
  border-color: #eaeaea;
  color: #666;
}
.e-grid .e-rowcell,
.e-grid .e-groupcaption,
.e-grid .e-indentcell,
.e-grid .e-recordplusexpand,
.e-grid .e-recordpluscollapse,
.e-grid .e-rowdragdropcell,
.e-grid .e-detailrowcollapse,
.e-grid .e-detailrowexpand,
.e-grid .e-detailindentcell,
.e-grid .e-detailcell {
  border-color: #eaeaea;
}
.e-grid.e-default .e-grouptopleftcell {
  border-color: #eaeaea;
}
.e-grid .e-frozenhdrcont .e-headercontent > .e-table,
.e-grid .e-frozenhdrcont .e-frozenheader > .e-table,
.e-grid .e-frozenhdrcont .e-movableheader > .e-table,
.e-grid .e-frozenhdrcont .e-headercontent .e-virtualtable > .e-table {
  border-bottom-color: #0078d6;
}
.e-grid .e-frozencontent {
  border-bottom-color: #eaeaea;
}
.e-grid .e-frozenheader > .e-table,
.e-grid .e-frozencontent > .e-table,
.e-grid .e-frozencontent .e-virtualtable > .e-table,
.e-grid .e-frozenheader .e-virtualtable > .e-table {
  border-right-color: #0078d6;
}
.e-grid .e-frozenheader.e-frozenborderdisabled > .e-table,
.e-grid .e-frozencontent.e-frozenborderdisabled > .e-table,
.e-grid .e-frozencontent.e-frozenborderdisabled .e-virtualtable > .e-table,
.e-grid .e-frozenheader.e-frozenborderdisabled .e-virtualtable > .e-table {
  border-right-color: #fff;
}
.e-grid.e-rtl .e-leftfreeze.e-freezeleftborder {
  border-left-color: #0078d6;
}
.e-grid.e-rtl .e-rightfreeze.e-freezerightborder {
  border-right-color: #0078d6;
}
.e-grid.e-rtl .e-filterbarcell.e-fixedfreeze.e-freezeleftborder, .e-grid.e-rtl .e-fixedfreeze.e-freezeleftborder {
  border-right-color: #0078d6;
}
.e-grid.e-rtl .e-filterbarcell.e-fixedfreeze.e-freezerightborder, .e-grid.e-rtl .e-fixedfreeze.e-freezerightborder {
  border-left-color: #0078d6;
}
.e-grid .e-leftfreeze.e-freezeleftborder {
  border-right-color: #0078d6;
}
.e-grid .e-rightfreeze.e-freezerightborder {
  border-left-color: #0078d6;
}
.e-grid .e-filterbarcell.e-fixedfreeze.e-freezeleftborder,
.e-grid .e-fixedfreeze.e-freezeleftborder {
  border-left-color: #0078d6;
}
.e-grid .e-filterbarcell.e-fixedfreeze.e-freezerightborder,
.e-grid .e-fixedfreeze.e-freezerightborder {
  border-right-color: #0078d6;
}
.e-grid .e-xlsel-top-border {
  border-top-color: #0078d6;
}
.e-grid .e-xlsel-left-border {
  border-left-color: #0078d6;
}
.e-grid .e-xlsel-right-border {
  border-right-color: #0078d6;
}
.e-grid .e-xlsel-bottom-border {
  border-bottom-color: #0078d6;
}
.e-grid .e-leftfreeze:not(.e-summarycell, .e-indentcell, .e-updatedtd, .e-selectionbackground, .e-columnselection, .e-groupcaption, .e-cellselectionbackground),
.e-grid .e-rightfreeze:not(.e-summarycell, .e-updatedtd, .e-selectionbackground, .e-columnselection, .e-groupcaption, .e-cellselectionbackground),
.e-grid .e-fixedfreeze:not(.e-summarycell, .e-selectionbackground, .e-updatedtd, .e-columnselection, .e-groupcaption, .e-cellselectionbackground) {
  background-color: #fff;
}
.e-grid .e-gridcontent .e-groupcaptionrow .e-leftfreeze.e-groupcaption,
.e-grid .e-gridcontent .e-groupcaptionrow .e-leftfreeze.e-recordplusexpand,
.e-grid .e-gridcontent .e-groupcaptionrow .e-leftfreeze.e-recordpluscollapse,
.e-grid .e-gridcontent .e-groupcaptionrow .e-leftfreeze.ee-indentcell {
  background-color: #fff;
}
.e-grid .e-gridfooter .e-leftfreeze,
.e-grid .e-gridfooter .e-rightfreeze,
.e-grid .e-gridfooter .e-fixedfreeze {
  background-color: #f6f6f6;
}
.e-grid .e-frozenheader.e-frozen-right-header > .e-table,
.e-grid .e-frozencontent.e-frozen-right-content > .e-table,
.e-grid .e-rowcell .e-frozen-default-cursor,
.e-grid .e-gridheader .e-headercell .e-frozen-default-cursor,
.e-grid .e-gridheader .e-filterbarcell .e-frozen-default-cursor {
  border-left-color: #0078d6;
}
.e-grid .e-frozenheader.e-frozen-right-header.e-frozenborderdisabled > .e-table,
.e-grid .e-frozencontent.e-frozen-right-content.e-frozenborderdisabled > .e-table {
  border-left-color: #fff;
}
.e-grid.e-rtl .e-frozenheader > .e-table, .e-grid.e-rtl .e-frozencontent > .e-table, .e-grid.e-rtl .e-frozenheader .e-virtualtable > .e-table, .e-grid.e-rtl .e-frozencontent .e-virtualtable > .e-table {
  border-left-color: #0078d6;
}
.e-grid.e-rtl .e-frozenheader.e-frozen-right-header > .e-table, .e-grid.e-rtl .e-frozencontent.e-frozen-right-content > .e-table {
  border-right-color: #0078d6;
}
.e-grid.e-gridhover .e-row tr:not(.e-disable-gridhover):not(.e-editedrow):not(.e-detailrow):hover .e-rowcell:not(.e-cellselectionbackground):not(.e-active):not(.e-updatedtd):not(.e-indentcell), .e-grid.e-gridhover .e-row:not(.e-disable-gridhover):not(.e-editedrow):not(.e-detailrow):hover .e-rowcell:not(.e-cellselectionbackground):not(.e-active):not(.e-updatedtd):not(.e-indentcell), .e-grid.e-gridhover .e-row:not(.e-disable-gridhover):not(.e-detailrow):hover .e-detailrowcollapse:not(.e-cellselectionbackground):not(.e-active):not(.e-updatedtd):not(.e-indentcell), .e-grid.e-gridhover .e-row:not(.e-disable-gridhover):not(.e-detailrow):hover .e-rowdragdrop:not(.e-cellselectionbackground):not(.e-active):not(.e-updatedtd):not(.e-indentcell), .e-grid.e-rtl .e-gridhover .e-row:not(.e-disable-gridhover):not(.e-detailrow):hover .e-rowdragdrop:not(.e-cellselectionbackground):not(.e-active):not(.e-updatedtd):not(.e-indentcell), .e-grid.e-gridhover .e-row:not(.e-disable-gridhover):not(.e-detailrow):hover .e-detailrowexpand:not(.e-cellselectionbackground):not(.e-active):not(.e-updatedtd):not(.e-indentcell) {
  background-color: #f4f4f4;
  color: #333;
}
.e-grid .e-row:hover .e-rowdragdrop {
  cursor: move;
}
.e-grid .e-notallowedcur .e-row:hover .e-rowdragdrop {
  cursor: not-allowed;
}
.e-grid .e-sortnumber {
  background-color: #bbbdc0;
  color: #333;
}
.e-grid.e-gridhover .e-frozenhover {
  background-color: #f4f4f4;
  color: #333;
}
.e-grid .e-col-menu.e-filter-popup {
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
}
.e-grid td.e-active .e-icon-rowdragicon::before {
  color: #333;
}
.e-grid td.e-active {
  background: #d1ebff;
  color: #333;
}
.e-grid .e-columnselection {
  background-color: #d1ebff;
}
.e-grid td.e-cellselectionbackground {
  background: #d1ebff;
  color: #333;
}
.e-grid .e-filterbarcell,
.e-grid .e-filterbarcelldisabled {
  background-color: #fff;
  background-image: none;
  border-color: #eaeaea;
}
.e-grid .e-filtered::before {
  color: #0078d6;
}
.e-grid .e-gridpopup .e-content {
  background-color: #fff;
  border-color: #eaeaea;
}
.e-grid .e-gridpopup span:hover,
.e-grid .e-gridpopup .e-spanclicked {
  border-color: #333;
}
.e-grid .e-gridpopup .e-downtail::before,
.e-grid .e-gridpopup .e-downtail {
  border-top-color: #eaeaea;
}
.e-grid .e-gridpopup .e-downtail::after {
  border-top-color: #fff;
}
.e-grid .e-gridpopup .e-uptail::before,
.e-grid .e-gridpopup .e-uptail {
  border-bottom-color: #eaeaea;
}
.e-grid .e-gridpopup .e-uptail::after {
  border-bottom-color: #fff;
}
.e-grid .e-cloneproperties {
  background-color: #d1ebff;
  border-color: #eaeaea;
  color: #333;
}
.e-grid .e-rhelper {
  background-color: #333;
  cursor: col-resize;
  opacity: 1;
}
.e-grid.e-device .e-rcursor::before {
  border-right-color: #333;
  opacity: 1;
}
.e-grid.e-device .e-rcursor::after {
  border-left-color: #333;
  opacity: 1;
}
.e-grid.e-resize-lines .e-headercell .e-rhandler, .e-grid.e-resize-lines .e-headercell .e-rsuppress {
  border-right: 1px solid #eaeaea;
}
.e-grid.e-resize-lines .e-frozen-right-header .e-headercell .e-rhandler, .e-grid.e-resize-lines .e-frozen-right-header .e-headercell .e-rsuppress {
  border-left: 1px solid #eaeaea;
}
.e-grid.e-resize-lines .e-focused .e-rhandler {
  border: 0 none;
}
.e-grid.e-resize-lines .e-headercell.e-stackedheadercell:not(.e-freezerightborder, .e-freezeleftborder) {
  border-bottom: 1px solid #eaeaea;
  border-right: 1px solid #eaeaea;
}
.e-grid.e-resize-lines .e-frozen-right-header .e-headercell.e-stackedheadercell {
  border-left: 1px solid #eaeaea;
}
.e-grid.e-rtl.e-resize-lines .e-headercell.e-stackedheadercell, .e-grid.e-rtl.e-resize-lines .e-headercell .e-rhandler, .e-grid.e-rtl.e-resize-lines .e-headercell .e-rsuppress {
  border-left: 1px solid #eaeaea;
  border-right-width: 0;
}
.e-grid.e-rtl.e-resize-lines .e-frozen-right-header .e-headercell.e-stackedheadercell, .e-grid.e-rtl.e-resize-lines .e-frozen-right-header .e-headercell .e-rhandler, .e-grid.e-rtl.e-resize-lines .e-frozen-right-header .e-headercell .e-rsuppress {
  border-left-width: 0;
  border-right: 1px solid #eaeaea;
}
.e-grid.e-resize-lines .e-filterbarcell, .e-grid.e-rtl.e-resize-lines .e-filterbarcell {
  border-top: 1px solid #eaeaea;
}
.e-grid .e-cloneproperties.e-draganddrop {
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  opacity: 0.95;
  overflow: visible;
}
.e-grid .e-row .e-dragstartrow::before, .e-grid .e-row .e-selectionbackground .e-dragstartrow::before {
  color: #0078d6;
}
.e-grid .e-griddragarea {
  background-color: #d1ebff;
  border-color: #d1ebff;
  color: #333;
}
.e-grid .e-groupdroparea {
  background-color: #0078d6;
  border-top-color: #eaeaea;
  color: #fff;
}
.e-grid .e-groupdroparea.e-hover {
  background-color: #0078d6;
}
.e-grid .e-groupdroparea.e-grouped {
  background-color: #0078d6;
}
.e-grid .e-groupheadercell {
  background-color: #b7e0ff;
  border-color: #b7e0ff;
  border-radius: 0;
  color: #333;
}
.e-grid .e-groupheadercell:hover {
  background-color: #d1ebff;
  border-color: #d1ebff;
}
.e-grid .e-ungroupbutton:hover {
  color: #333;
  opacity: 1;
}
.e-grid .e-ungroupbutton {
  opacity: 1;
}
.e-grid .e-groupcaption,
.e-grid .e-indentcell,
.e-grid .e-recordplusexpand,
.e-grid .e-recordpluscollapse {
  background-color: #fff;
  color: #333;
}
.e-grid .e-grouptopleftcell {
  background-color: #fff;
  border-color: #eaeaea;
}
.e-grid .e-stackedheadercell {
  border-bottom-color: #eaeaea;
}
.e-grid .e-verticallines tr th {
  border-color: #eaeaea;
}
.e-grid td.e-updatedtd {
  background-color: #d7f9c7;
  color: #333;
}
.e-grid .e-gridcontent table tbody .e-normaledit .e-rowcell {
  border-top-color: #eaeaea;
}
.e-grid .e-gridcontent table tbody .e-normaledit .e-dragindentcell,
.e-grid .e-gridcontent table tbody .e-normaledit .e-detailrowcollapse {
  border-top: 1px solid #eaeaea;
}
.e-grid .e-ccdlg .e-footer-content {
  border-color: rgba(0, 0, 0, 0.12);
  opacity: 1;
}
.e-grid .e-ccdlg .e-cc-searchdiv {
  border-color: #212121;
}
.e-grid .e-ccdlg .e-cc-searchdiv.e-input-focus {
  border-color: #0078d6;
}
.e-grid .e-cloneproperties.e-draganddrop .e-rowcell {
  color: #333;
}
.e-grid .e-cloneproperties.e-draganddrop .e-rowcell.e-focused {
  box-shadow: none;
}
.e-grid .e-cloneproperties.e-draganddrop table,
.e-grid .e-cloneproperties.e-draganddrop table .e-selectionbackground {
  background-color: #fff;
  height: 30px;
}
.e-grid.e-rtl .e-verticallines tr th:first-child:not(.e-firstcell) {
  border-color: #eaeaea;
}
.e-grid.e-rtl.e-default .e-gridheader .e-headercell.e-firstheader, .e-grid.e-rtl.e-default.e-horizontallines .e-headercell.e-firstheader, .e-grid.e-rtl.e-default.e-verticallines .e-gridheader .e-headercell.e-firstheader, .e-grid.e-rtl.e-default.e-verticallines .e-headercell.e-stackedheadercell, .e-grid.e-rtl.e-default.e-verticallines tr th:last-child, .e-grid.e-rtl.e-default.e-verticallines .e-gridheader th.e-grouptopleftcell, .e-grid.e-rtl.e-default.e-verticallines .e-gridheader th.e-grouptopleftcell.e-lastgrouptopleftcell, .e-grid.e-rtl.e-default.e-bothlines .e-gridheader .e-headercell.e-firstheader, .e-grid.e-rtl.e-default.e-bothlines .e-gridheader th.e-grouptopleftcell.e-lastgrouptopleftcell {
  border-color: #eaeaea;
}
.e-grid.e-rtl .e-tableborder {
  border-left-color: #eaeaea;
}
.e-grid.e-rtl .e-filterbardropdown {
  margin-left: 28px;
}
.e-grid.sf-grid .e-gridfooter .e-summarycontent {
  border-right: 0 #eaeaea solid;
}
.e-grid .e-ftrchk.e-chkfocus,
.e-grid li.e-cclist.e-colfocus {
  background-color: #f4f4f4;
}

.e-tooltip-wrap.e-griderror,
.e-control.e-tooltip-wrap.e-popup.e-griderror {
  background-color: #fcdbe4;
  border-color: #fcdbe4;
}

.e-tooltip-wrap.e-griderror .e-arrow-tip-inner.e-tip-top,
.e-tooltip-wrap.e-griderror .e-arrow-tip-outer.e-tip-top {
  border-bottom: 8px solid #fcdbe4;
  color: #fcdbe4;
}

.e-tooltip-wrap.e-griderror .e-arrow-tip-outer.e-tip-bottom,
.e-tooltip-wrap.e-griderror .e-arrow-tip-inner.e-tip-bottom {
  border-top: 8px solid #fcdbe4;
  color: #fcdbe4;
}

.e-tooltip-wrap.e-griderror .e-tip-content,
.e-tooltip-wrap.e-griderror .e-tip-content label {
  color: #f44336;
}

.e-dropitemscount {
  background-color: #0078d6;
  color: #fff;
}

.e-filterbaroperator.e-dropdownlist {
  position: absolute;
}

.e-filterbardropdown:not(.e-rtl) {
  margin-right: 28px;
}

.e-headercontent .e-filterbar .e-filterbarcell .e-fltrinputdiv .e-input-group .e-filterbardropdown {
  border: transparent;
}
.e-headercontent .e-filterbar .e-filterbarcell .e-fltrinputdiv .e-input-group .e-filterbardropdown:active,
.e-headercontent .e-filterbar .e-filterbarcell .e-fltrinputdiv .e-input-group .e-filterbardropdown.e-input-focus {
  box-shadow: none;
}