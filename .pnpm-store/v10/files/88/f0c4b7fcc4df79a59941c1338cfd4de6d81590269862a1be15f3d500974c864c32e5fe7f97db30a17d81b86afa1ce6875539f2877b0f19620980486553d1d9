@include export-module('pdfviewer-fabric-icons') {
  .e-pdfviewer {
    .e-pv-icon::before {
      font-family: 'e-icons';
    }

    .e-pv-icon-search::before {
      font-family: 'e-icons';
      font-size: 12px;
    }

    .e-pv-open-document-icon::before {
      content: '\ebdd';
    }

    .e-pv-download-document-icon::before {
      content: '\ebe4';
    }

    .e-pv-print-document-icon::before {
      content: '\ebf9';
    }

    .e-pv-first-page-navigation-icon::before {
      content: '\ebde';
    }

    .e-pv-previous-page-navigation-icon::before,
    .e-pv-prev-search-icon::before {
      content: '\ebdf';
    }

    .e-pv-next-page-navigation-icon::before,
    .e-pv-next-search-icon::before {
      content: '\ebe0';
    }

    .e-pv-last-page-navigation-icon::before {
      content: '\ebe1';
    }

    .e-pv-zoom-out-icon::before {
      content: '\ebe2';
      line-height: $pv-toolbaritem-btn-line-height;
    }

    .e-pv-zoom-in-icon::before {
      content: '\ebe3';
      line-height: $pv-toolbaritem-btn-line-height;
    }

    .e-pv-thumbnail-view-icon::before {
      content: '\ec00';
    }

    .e-pv-thumbnail-view-disable-icon::before {
      color: $pv-thumbnail-icon-disable-color;
      content: '\ec00';
    }

    .e-pv-thumbnail-view-selection-icon::before {
      color: $pv-thumbnail-icon-selection-color;
      content: '\ec00';
    }

    .e-pv-bookmark-icon::before {
      content: '\ebe5';
    }

    .e-pv-bookmark-disable-icon::before {
      color: $pv-thumbnail-icon-disable-color;
      content: '\ebe5';
    }

    .e-pv-bookmark-selection-icon::before {
      color: $pv-thumbnail-icon-selection-color;
      content: '\ebe5';
    }

    .e-pv-title-close-icon::before,
    .e-pv-annotation-tools-close-icon::before,
    .e-pv-annotation-popup-close::before {
      content: '\ebe8';
      font-size: $pv-title-close-font-size;
    }

    .e-pv-resize-icon::before {
      content: '\e84b';
      font-size: 10px;
    }

    .e-pv-text-select-tool-icon::before {
      content: '\ebf6';
    }

    .e-pv-pan-tool-icon::before {
      content: '\ebf3';
    }

    .e-pv-text-search-icon::before {
      content: '\ebe6';
    }

    .e-pv-search-icon::before {
      content: '\ebe6';
      font-family: 'e-icons';
    }

    .e-pv-search-close::before {
      content: '\ebe8';
      font-family: 'e-icons';
    }

    .e-pv-formdesigner-icon::before {
      content: '\e90c';
    }

    .e-pv-annotation-icon::before {
      content: '\ebff';
    }

    .e-pv-annotation-color-icon::before {
      content: '\ebf2';
    }

    .e-pv-annotation-stroke-icon::before {
      content: '\e668';
    }

    .e-pv-annotation-opacity-icon::before {
      content: '\ebf4';
    }

    .e-pv-annotation-thickness-icon::before {
      content: '\ebfe';
    }

    .e-pv-annotation-delete-icon::before {
      content: '\ebf5';
    }

    .e-pv-undo-icon::before {
      content: '\ebed';
    }

    .e-pv-redo-icon::before {
      content: '\ebfa';
    }

    .e-pv-more-icon::before {
      content: '\ebef';
    }

    .e-pv-backward-icon::before {
      content: '\e962';
    }

    .e-pv-notification-icon {
      background-image: $pv-corrupted-notification-background-image;
      background-repeat: $pv-corrupted-notification-background-repeat;
      background-size: $pv-corrupted-notification-background-size;
      height: $pv-corrupted-notification-height;
    }

    .e-pv-notification-icon-rtl {
      background-image: $pv-corrupted-notification-background-image;
      background-position: $pv-corrupted-notification-background-position;
      background-repeat: $pv-corrupted-notification-background-repeat;
      background-size: $pv-corrupted-notification-background-size;
      height: $pv-corrupted-notification-height;
    }

    .e-pv-textbox-icon::before {
      content: '\e89f';
    }

    .e-pv-password-icon::before {
      content: '\e91a';
    }

    .e-pv-checkbox-icon::before {
      content: '\e90b';
    }

    .e-pv-radiobutton-icon::before {
      content: '\e90f';
    }

    .e-pv-dropdown-icon::before {
      content: '\e90e';
    }

    .e-pv-listbox-icon::before {
      content: '\e89e';
    }

    .e-pv-annotation-calibrate-icon::before {
      content: '\e678';
    }
  }

  .e-pv-download-document-icon.e-menu-icon::before {
    content: '\ebe4';
  }

  .e-pv-bookmark-icon.e-menu-icon::before {
    content: '\ebe5';
  }

  .e-pv-highlight-icon::before {
    content: '\ebee';
    font-family: 'e-icons';
  }

  .e-pv-underline-icon::before {
    content: '\ebf0';
    font-family: 'e-icons';
  }

  .e-pv-strikethrough-icon::before {
    content: '\ebf1';
    font-family: 'e-icons';
  }

  .e-pv-copy-icon::before {
    content: '\e70a';
    font-family: 'e-icons';
  }

  .e-pv-stamp-icon::before {
    content: '\ec01';
  }

  .e-pv-shape-line-icon::before {
    content: '\e668';
    font-family: 'e-icons';
  }

  .e-pv-shape-arrow-icon::before {
    content: '\e669';
    font-family: 'e-icons';
  }

  .e-pv-shape-rectangle-icon::before {
    content: '\e670';
    font-family: 'e-icons';
  }

  .e-pv-shape-circle-icon::before {
    content: '\e671';
    font-family: 'e-icons';
  }

  .e-pv-shape-pentagon-icon::before {
    content: '\e672';
    font-family: 'e-icons';
  }

  .e-pv-annotation-shape-icon::before {
    content: '\ebfc';
  }

  .e-pv-cut-icon::before {
    content: '\e33b';
    font-family: 'e-icons';
  }

  .e-pv-paste-icon::before {
    content: '\e33d';
    font-family: 'e-icons';
  }

  .e-pv-delete-icon::before {
    content: '\ebf5';
    font-family: 'e-icons';
  }

  .e-pv-properties-fill-color-icon::before {
    content: '\ebf2';
    font-family: 'e-icons';
  }

  .e-pv-properties-stroke-color-icon::before {
    content: '\e668';
    font-family: 'e-icons';
  }

  .e-pv-comment-icon::before {
    content: '\e680';
    font-family: 'e-icons';
  }

  .e-pv-property-icon::before {
    content: '\e199';
    font-family: 'e-icons';
  }

  .e-pv-comment-selection-icon::before {
    color: $pv-thumbnail-icon-selection-color;
    content: '\e680';
    font-family: 'e-icons';
  }

  .e-pv-comment-panel-icon::before {
    content: '\eb8b';
    font-family: 'e-icons';
  }

  .e-pv-accepted-icon::before {
    color: $pv-status-icon-color;
    content: '\e682';
    font-family: 'e-icons';
    font-size: $pv-status-icon-font-size;
    padding: $pv-accepted-icon-padding;
    position: $pv-status-icon-position;
  }

  .e-pv-rejected-icon::before {
    color: $pv-status-icon-color;
    content: '\e683';
    font-family: 'e-icons';
    font-size: $pv-status-icon-font-size;
    padding: $pv-rejected-icon-padding;
    position: $pv-status-icon-position;
  }

  .e-pv-completed-icon::before {
    color: $pv-status-icon-color;
    content: '\e614';
    font-family: 'e-icons';
    font-size: $pv-status-icon-font-size;
    padding: $pv-status-icon-padding;
    position: $pv-status-icon-position;
  }

  .e-pv-cancelled-icon::before {
    color: $pv-status-icon-color;
    content: '\e60a';
    font-family: 'e-icons';
    font-size: $pv-status-icon-font-size;
    padding: $pv-status-icon-padding;
    position: $pv-status-icon-position;
  }

  .e-pv-scale-ratio-icon::before {
    content: '\e678';
    font-family: 'e-icons';
  }

  .e-pv-calibrate-distance-icon::before {
    content: '\e673';
    font-family: 'e-icons';
  }

  .e-pv-calibrate-perimeter-icon::before {
    content: '\e674';
    font-family: 'e-icons';
  }

  .e-pv-calibrate-area-icon::before {
    content: '\e675';
    font-family: 'e-icons';
  }

  .e-pv-calibrate-radius-icon::before {
    content: '\e676';
    font-family: 'e-icons';
  }

  .e-pv-calibrate-volume-icon::before {
    content: '\e677';
    font-family: 'e-icons';
  }

  .e-menu-wrapper.e-custom-scroll.e-lib.e-keyboard.e-pv-stamp {
    background-color: transparent;
  }

  .e-btn-icon .e-pv-properties-stroke-color-icon .e-pv-icon {
    height: $pv-stroke-color-height;
  }

  .e-menu-icon.e-pv-stamp-icon.e-pv-icon,
  .e-pv-annotation-stamp-container .e-menu-wrapper ul .e-menu-item .e-caret {
    margin-top: $pv-stamp-icon-margin;
  }

  .e-menu-wrapper.e-custom-scroll.e-lib.e-keyboard.e-pv-stamp ul {
    padding: 0;
  }

  .e-pv-freetext-icon::before {
    content: '\ebe7';
    font-family: 'e-icons';
  }

  .e-pv-annotation-textalign-icon::before {
    content: '\ebeb';
    font-family: 'e-icons';
  }

  .e-pv-annotation-textprop-icon::before {
    content: '\eb04';
    font-family: 'e-icons';
  }

  .e-pv-annotation-textcolor-icon::before {
    content: '\e34c';
    font-family: 'e-icons';
  }

  .e-pv-left-align-icon::before {
    content: '\ebeb';
    font-family: 'e-icons';
  }

  .e-pv-right-align-icon::before {
    content: '\eb82';
    font-family: 'e-icons';
  }

  .e-pv-center-align-icon::before {
    content: '\eb9e';
    font-family: 'e-icons';
  }

  .e-pv-justfiy-align-icon::before {
    content: '\eb67';
    font-family: 'e-icons';
  }

  .e-pv-bold-icon::before {
    content: '\eb77';
    font-family: 'e-icons';
  }

  .e-pv-italic-icon::before {
    content: '\eb99';
    font-family: 'e-icons';
  }

  .e-pv-strikeout-icon::before {
    content: '\eb7f';
    font-family: 'e-icons';
  }

  .e-pv-underlinetext-icon::before {
    content: '\ebf0';
    font-family: 'e-icons';
  }

  .e-pv-superscript-icon::before {
    content: '\e352';
    font-family: 'e-icons';
  }

  .e-pv-subscript-icon::before {
    content: '\e357';
    font-family: 'e-icons';
  }

  .e-pv-handwritten-icon::before {
    content: '\e739';
    font-family: 'e-icons';
  }

  .e-pv-inkannotation-icon::before {
    content: '\ebec';
    font-family: 'e-icons';
  }

  .e-pv-delete::before {
    content: '\eb00';
    font-family: 'e-icons';
  }

  .e-pv-eye-icon::before {
    content: '\e345';
    cursor: $pv-eye-icon-hover-cursor;
    font-family: 'e-icons';
    font-size: $pv-eye-icon-font-size;
    padding: $pv-eye-icon-padding;
  }

  .e-pv-eye-slash-icon::before {
    content: '\e721';
    cursor: $pv-eye-icon-hover-cursor;
    font-family: 'e-icons';
    font-size: $pv-eye-icon-font-size;
    padding: $pv-eye-icon-padding;
  }
}
