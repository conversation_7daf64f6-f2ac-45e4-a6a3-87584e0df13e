:root {
  --color-sf-black: 0, 0, 0;
  --color-sf-white: 255, 255, 255;
  --color-sf-primary: 208, 188, 255;
  --color-sf-primary-container: 79, 55, 139;
  --color-sf-secondary: 204, 194, 220;
  --color-sf-secondary-container: 74, 68, 88;
  --color-sf-tertiary: 239, 184, 200;
  --color-sf-tertiary-container: 99, 59, 72;
  --color-sf-surface: 28, 27, 31;
  --color-sf-surface-variant: 73, 69, 79;
  --color-sf-background: var(--color-sf-surface);
  --color-sf-on-primary: 55, 30, 115;
  --color-sf-on-primary-container: 234, 221, 255;
  --color-sf-on-secondary: 51, 45, 65;
  --color-sf-on-secondary-container: 232, 222, 248;
  --color-sf-on-tertiary: 73, 37, 50;
  --color-sf-on-tertiary-containe: 255, 216, 228;
  --color-sf-on-surface: 230, 225, 229;
  --color-sf-on-surface-variant: 202, 196, 208;
  --color-sf-on-background: 230, 225, 229;
  --color-sf-outline: 147, 143, 153;
  --color-sf-outline-variant: 68, 71, 70;
  --color-sf-shadow: 0, 0, 0;
  --color-sf-surface-tint-color: 208, 188, 255;
  --color-sf-inverse-surface: 230, 225, 229;
  --color-sf-inverse-on-surface: 49, 48, 51;
  --color-sf-inverse-primary: 103, 80, 164;
  --color-sf-scrim: 0, 0, 0;
  --color-sf-error: 242, 184, 181;
  --color-sf-error-container: 140, 29, 24;
  --color-sf-on-error: 96, 20, 16;
  --color-sf-on-error-container: 249, 222, 220;
  --color-sf-success: 83, 202, 23;
  --color-sf-success-container: 22, 62, 2;
  --color-sf-on-success: 13, 39, 0;
  --color-sf-on-success-container: 183, 250, 150;
  --color-sf-info: 71, 172, 251;
  --color-sf-info-container: 0, 67, 120;
  --color-sf-on-info: 0, 51, 91;
  --color-sf-on-info-container: 173, 219, 255;
  --color-sf-warning: 245, 180, 130;
  --color-sf-warning-container: 123, 65, 0;
  --color-sf-on-warning: 99, 52, 0;
  --color-sf-on-warning-container: 255, 220, 193;
  --color-sf-spreadsheet-gridline: 231, 224, 236;
  --color-sf-shadow-focus-ring1: 0 0 0 1px #000000, 0 0 0 3px #ffffff;
  --color-sf-success-text: 0, 0, 0;
  --color-sf-warning-text: 0, 0, 0;
  --color-sf-info-text: 0, 0, 0;
  --color-sf-danger-text: 0, 0, 0;
  --color-sf-diagram-palette-background: var(--color-sf-inverse-surface);
  --color-sf-content-text-color-alt2: var(--color-sf-on-secondary);
}

.e-documenteditor .e-close::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7e7";
  font-family: "e-icons";
  font-size: 14px;
}
.e-documenteditor .e-de-op-search-icon::before {
  content: "\e754";
  font-family: "e-icons";
}
.e-documenteditor .e-arrow-up::before {
  content: "\e87a";
  font-family: "e-icons";
}
.e-documenteditor .e-arrow-down::before {
  content: "\e70d";
  font-family: "e-icons";
}
.e-documenteditor .e-de-op .e-de-op-close-icon {
  height: 20px;
}
.e-documenteditor .e-de-op-close-icon::before {
  content: "\e7e7";
  font-family: "e-icons";
}
.e-documenteditor .e-de-op-search-close-icon::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7e7";
  font-family: "e-icons";
}
.e-documenteditor .e-de-new-cmt::before {
  content: "\e805";
  font-family: "e-icons";
}
.e-documenteditor .e-de-menu-icon::before {
  content: "\e770";
  font-family: "e-icons";
}
.e-documenteditor .e-de-cmt-mark-icon::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e733";
  font-family: "e-icons";
  font-size: 13px;
}
.e-documenteditor .e-de-multi-cmt-mark::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e8bc";
  font-family: "e-icons";
  font-size: 14px;
}
.e-documenteditor .e-de-cmt-post::before {
  content: "\e71d";
  font-family: "e-icons";
}
.e-documenteditor .e-de-cmt-rply-icon::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e85e";
  font-family: "e-icons";
}
.e-documenteditor .e-de-cmt-cancel::before {
  content: "\e7e7";
  font-family: "e-icons";
}
.e-documenteditor .e-de-cmt-delete::before {
  content: "\e820";
  font-family: "e-icons";
}
.e-documenteditor .e-de-cmt-reopen::before {
  content: "\e782";
  font-family: "e-icons";
}
.e-documenteditor .e-de-nav-up::before {
  content: "\e7dd";
  font-family: "e-icons";
}
.e-documenteditor .e-de-nav-right-arrow::before {
  content: "\e748";
  font-family: "e-icons";
}
.e-documenteditor .e-de-nav-left-arrow::before {
  content: "\e765";
  font-family: "e-icons";
}
.e-documenteditor .e-de-save-icon::before {
  content: "\e774";
  font-family: "e-icons";
}
.e-documenteditor .e-de-cancel-icon::before {
  content: "\e7e7";
  font-family: "e-icons";
}

.e-de-ctn-title-print::before {
  content: "\e75d";
}

.e-de-acceptchange::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7a8";
  font-family: "e-icons";
}

.e-de-rejectchange::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e815";
  font-family: "e-icons";
}

.e-de-ctn-title-download::before {
  content: "\e7a1";
}

.e-de-table-properties-alignment:hover {
  border-color: rgba(var(--color-sf-primary));
}

.e-de-table-properties-alignment {
  border: 1px solid transparent;
}

.e-de-tablecell-alignment {
  border: 1px solid transparent;
}

.e-de-tablecell-alignment:hover {
  border-color: rgba(var(--color-sf-primary));
}

.e-de-table-left-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7f6";
  font-size: 42px;
}

.e-de-table-center-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7f1";
  font-size: 42px;
}

.e-de-table-right-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e703";
  font-size: 42px;
}

.e-de-tablecell-top-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e775";
  font-family: "e-icons";
  font-size: 42px;
}

.e-de-tablecell-center-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7e5";
  font-family: "e-icons";
  font-size: 42px;
}

.e-de-tablecell-bottom-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7d9";
  font-family: "e-icons";
  font-size: 42px;
}

.e-de-table-border-setting {
  border: 1px solid rgba(var(--color-sf-outline-variant));
  height: 52px;
  left: 3px;
  position: relative;
  top: 3px;
  width: 52px;
}

.e-de-table-border-setting-genral {
  border: 1px solid rgba(var(--color-sf-outline-variant));
  height: 60px;
  width: 60px;
}

.e-de-table-border-preview-genral {
  border: 1px solid rgba(var(--color-sf-on-surface-variant));
  height: 24px;
  width: 24px;
}

.e-de-table-border-inside-setting:hover {
  border: 1px solid rgba(var(--color-sf-primary));
}

.e-de-table-border-preview {
  height: 24px;
  width: 24px;
}

.e-de-table-border-inside-preview:hover {
  border: 1px solid rgba(var(--color-sf-primary));
}

.e-de-table-border-inside-setting-click {
  border: 1px solid rgba(var(--color-sf-primary));
}

.e-de-table-border-inside-preview-click {
  border: 1px solid rgba(var(--color-sf-primary));
}

.e-de-table-border-none-setting::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7eb";
  font-size: 46px;
  position: absolute;
}

.e-de-table-border-box-setting::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e834";
  font-size: 46px;
  left: 2px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-all-setting::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7e8";
  font-family: "e-icons";
  font-size: 46px;
  left: 2px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-custom-setting::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7d5";
  font-size: 46px;
  left: 2px;
  position: absolute;
  top: 2px;
}

.e-de-para-border-none-setting::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e890";
  font-size: 46px;
  position: absolute;
}

.e-de-para-border-box-setting::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e891";
  font-size: 46px;
  left: 2px;
  position: absolute;
  top: 2px;
}

.e-de-para-border-shadow-setting::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e892";
  font-size: 46px;
  left: 2px;
  position: absolute;
  top: 2px;
}

.e-de-para-border-custom-setting::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e88f";
  font-size: 46px;
  left: 2px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-toptop-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7e0";
  font-family: "e-icons";
  font-size: 16px;
  left: 5px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-topcenter-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e83b";
  font-size: 16px;
  left: 5px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-topbottom-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e766";
  font-size: 16px;
  left: 5px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-diagionalup-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e79d";
  font-size: 16px;
  left: 5px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-diagionaldown-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e784";
  font-size: 16px;
  left: 5px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-bottomleft-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e806";
  font-size: 16px;
  left: 5px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-bottomcenter-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e792";
  font-size: 16px;
  left: 5px;
  position: absolute;
  top: 2px;
}

.e-de-table-border-bottomright-alignment::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7ab";
  font-size: 16px;
  left: 5px;
  position: absolute;
  top: 2px;
}

.e-de-columns-presets-genral {
  height: 62px;
  width: 62px;
  margin-right: 33px;
  margin-bottom: 12px;
}

.e-de-columns-padding-alignment {
  padding-top: 24px;
}

.e-de-column-dlg-preview-div {
  border: 1px solid rgba(var(--color-sf-outline-variant));
  height: 120px;
  width: 120px;
}

.e-de-padding-col-prev {
  padding-left: 15px;
}

.e-width-space-div {
  width: 320px;
}

.e-de-columns-presets-genral.e-de-rtl {
  margin-left: 33px;
}

.e-de-padding-col-prev.e-de-rtl {
  padding-right: 15px;
}

.e-de-column-dlg-preview-div.e-de-rtl {
  border: 1px solid rgba(var(--color-sf-outline-variant));
  height: 120px;
  width: 120px;
}

.e-menu-item .e-de-cmt-add::before {
  content: "\e82c";
}

.e-menu-item .e-de-cut::before {
  content: "\e7fb";
}

.e-menu-item .e-de-spellcheck::before {
  content: "\e7f0";
}

.e-menu-item .e-de-copy::before {
  content: "\e77c";
}

.e-menu-item .e-de-paste::before {
  content: "\e70b";
}

.e-menu-item .e-de-continue-numbering::before {
  content: "\e718";
}

.e-menu-item .e-de-restart-at::before {
  content: "\e715";
}

.e-menu-item .e-de-insertlink::before {
  content: "\e757";
}

.e-menu-item .e-de-open-hyperlink::before {
  content: "\e797";
}

.e-menu-item .e-de-copy-hyperlink::before {
  content: "\e745";
}

.e-menu-item .e-de-open-properties::before {
  content: "\e77e";
}

.e-menu-item .e-de-edit-hyperlink::before {
  content: "\e722";
}

.e-menu-item .e-de-remove-hyperlink::before {
  content: "\e80c";
}

.e-menu-item .e-de-fonts::before {
  content: "\e76f";
}

.e-menu-item .e-de-paragraph::before {
  content: "\e7b8";
}

.e-menu-item .e-de-table::before {
  content: "\e7d1";
}

.e-menu-item .e-de-insertabove::before {
  content: "\e836";
}

.e-menu-item .e-de-insertbelow::before {
  content: "\e801";
}

.e-menu-item .e-de-insertleft::before {
  content: "\e78b";
}

.e-menu-item .e-de-insertright::before {
  content: "\e70e";
}

.e-menu-item .e-de-delete-table::before {
  content: "\e811";
}

.e-menu-item .e-de-deleterow::before {
  content: "\e7f2";
}

.e-menu-item .e-de-deletecolumn::before {
  content: "\e714";
}

.e-de-bold::before {
  content: "\e737";
  font-family: "e-icons";
}

.e-de-italic::before {
  content: "\e75a";
  font-family: "e-icons";
}

.e-de-underline::before {
  content: "\e82f";
  font-family: "e-icons";
}

.e-de-indent::before {
  content: "\e72a";
  font-family: "e-icons";
}

.e-de-outdent::before {
  content: "\e810";
  font-family: "e-icons";
}

.e-de-align-left::before {
  content: "\e7b8";
  font-family: "e-icons";
}

.e-de-align-center::before {
  content: "\e813";
  font-family: "e-icons";
}

.e-de-align-right::before {
  content: "\e719";
  font-family: "e-icons";
}

.e-de-justify::before {
  content: "\e721";
  font-family: "e-icons";
}

.e-de-single-spacing::before {
  content: "\e771";
  font-family: "e-icons";
}

.e-de-double-spacing::before {
  content: "\e7c4";
  font-family: "e-icons";
}

.e-de-one-point-five-spacing::before {
  content: "\e725";
  font-family: "e-icons";
}

.e-de-before-spacing::before {
  content: "\e7b5";
  font-family: "e-icons";
}

.e-de-after-spacing::before {
  content: "\e767";
  font-family: "e-icons";
}

.e-de-icon-bullet-list-dot::before {
  content: "\e747";
  font-family: "e-icons";
  font-size: 8px;
  line-height: 28px;
}

.e-de-icon-bullet-list-circle::before {
  content: "\e7d0";
  font-family: "e-icons";
  font-size: 8px;
  line-height: 28px;
}

.e-de-icon-bullet-list-square::before {
  content: "\e7be";
  font-family: "e-icons";
  font-size: 8px;
  line-height: 28px;
}

.e-de-icon-bullet-list-tick::before {
  content: "\e7fc";
  font-family: "e-icons";
  font-size: 12px;
}

.e-de-icon-bullet-list-flower::before {
  content: "\e79b";
  font-family: "e-icons";
  font-size: 12px;
}

.e-de-icon-bullet-list-arrow::before {
  content: "\e763";
  font-family: "e-icons";
  font-size: 12px;
}

.e-de-icon-bullet-list-none::before {
  content: "\e7f3";
  font-family: "e-icons";
  font-size: 24px;
}

.e-de-icon-autofit::before {
  content: "\e74a";
  font-family: "e-icons";
}

.e-de-icon-fixed-columnwidth::before {
  content: "\e785";
  font-family: "e-icons";
}

.e-de-icon-auto-fitwindow::before {
  content: "\e759";
  font-family: "e-icons";
}

.e-item .e-de-paste-text::before {
  content: "\e70f";
  font-family: "e-icons";
}

.e-item .e-de-paste-source::before {
  content: "\e842";
  font-family: "e-icons";
}

.e-item .e-de-paste-merge::before {
  content: "\e752";
  font-family: "e-icons";
}

.e-btn-icon .e-de-paste::before,
.e-icon-btn .e-de-paste::before {
  content: "\e70b";
  font-family: "e-icons";
}

.e-item .e-de-paste-column::before {
  content: "\e885";
}

.e-item .e-de-paste-row::before {
  content: "\e884";
}

.e-item .e-de-paste-overwrite-cells::before {
  content: "\e886";
}

.e-item .e-de-paste-nested-table::before {
  content: "\e883";
}

.e-item .e-de-paste-merge-table::before {
  content: "\e882";
}

.e-de-preset-container {
  width: 95px;
}

.e-de-preset-container.e-de-rtl {
  width: 85px;
}

.e-de-dlg-container {
  padding-bottom: 16px;
}

.e-de-dlg-row {
  display: -ms-flexbox;
  display: flex;
}

.e-de-svg-border-color {
  stroke: rgba(var(--color-sf-on-surface));
}

.e-de-svg-border-fill-color {
  fill: rgba(var(--color-sf-on-surface));
}

.e-de-container-row {
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 16px;
}

.e-de-subcontainer-left {
  padding-right: 10px;
  width: 50%;
}

.e-de-subcontainer-right {
  padding-left: 10px;
  width: 50%;
}

.e-de-dlg-tab-first-child {
  padding-top: 10px;
}

.e-de-dlg-heading {
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 6px;
}

.e-rtl .e-de-subcontainer-left {
  padding-left: 10px;
  padding-right: 0;
}
.e-rtl .e-de-subcontainer-right {
  padding-left: 0;
  padding-right: 10px;
  width: 50%;
}

.e-bigger .e-de-dlg-container {
  padding-bottom: 20px;
}
.e-bigger .e-de-container-row {
  padding-bottom: 20px;
}
.e-bigger .e-de-subcontainer-left {
  padding-right: 10px;
  width: 50%;
}
.e-bigger .e-de-subcontainer-right {
  padding-left: 10px;
  width: 50%;
}
.e-bigger .e-de-dlg-tab-first-child {
  padding-top: 10px;
}
.e-bigger .e-rtl .e-de-subcontainer-left {
  padding-left: 10px;
  padding-right: 0;
}
.e-bigger .e-rtl .e-de-subcontainer-right {
  padding-left: 0;
  padding-right: 10px;
  width: 50%;
}

.e-de-blink-cursor {
  border-left: 1px solid rgba(var(--color-sf-black));
  pointer-events: none;
  position: absolute;
  z-index: 3;
}

.e-de-cursor-animation {
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-name: FadeInFadeOut;
}

@keyframes FadeInFadeOut {
  from {
    opacity: 1;
  }
  13% {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  63% {
    opacity: 1;
  }
  to {
    opacity: 1;
  }
}
.e-de-text-target {
  border: 0;
  height: 1px;
  opacity: 0;
  outline-style: none;
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  top: -10000px;
  width: 625px;
}

.e-de-txt-form .e-de-txt-field {
  display: block;
}
.e-de-txt-form .e-de-num-field {
  display: none;
}
.e-de-txt-form .e-de-date-field {
  display: none;
}
.e-de-txt-form .e-de-ddl-field {
  display: none;
}

.e-de-num-form .e-de-txt-field {
  display: none;
}
.e-de-num-form .e-de-num-field {
  display: block;
}
.e-de-num-form .e-de-date-field {
  display: none;
}
.e-de-num-form .e-de-ddl-field {
  display: none;
}

.e-de-date-form .e-de-txt-field {
  display: none;
}
.e-de-date-form .e-de-num-field {
  display: none;
}
.e-de-date-form .e-de-date-field {
  display: block;
}
.e-de-date-form .e-de-ddl-field {
  display: none;
}

.e-de-ddl-form .e-de-txt-field {
  display: none;
}
.e-de-ddl-form .e-de-num-field {
  display: none;
}
.e-de-ddl-form .e-de-date-field {
  display: none;
}
.e-de-ddl-form .e-de-ddl-field {
  display: block;
}

.e-documenteditor .e-de-op-close-button {
  left: 267px;
  position: absolute;
  top: 8px;
}
.e-documenteditor .e-de-op-close-button.e-de-rtl {
  left: 14px;
}

/* stylelint-disable */
.e-de-background {
  background-color: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.05), rgba(var(--color-sf-primary), 0.05)), rgba(var(--color-sf-surface));
}

.e-de-column-label {
  font-size: 13;
  font-weight: 500;
  padding-left: 20px;
  padding-right: 20px;
}

.e-de-column-label.e-de-rtl {
  font-size: 13;
  font-weight: 500;
  padding-left: 50px;
  padding-right: 50px;
}

.e-de-ff-sub-header {
  display: block;
  font-size: 12px;
  font-weight: 400;
  margin-top: 6px;
}

.e-de-check-exactnumbr-width {
  width: 130px !important;
}

.e-de-ff-dlg-heading {
  display: block;
  font-weight: 600;
  margin-bottom: 6px;
  font-size: 14px;
}

.e-de-ff-dlg-heading-small {
  display: block;
  font-weight: 600;
  margin-bottom: 4px;
}

.e-de-ff-dlg-drpdwn-heading {
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.e-de-ff-dlg-check {
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: -2px;
}

.e-de-div-seperate-dlg {
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-bottom: 12px;
  width: 100%;
}

.e-de-ff-radio-div {
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-right: 16px;
}

.e-de-ff-radio-div.e-de-rtl {
  margin-left: 16px;
  margin-right: 0;
}

.e-de-ff-radio-scnd-div {
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-bottom: 0;
  margin-right: 16px;
}

.e-de-ff-radio-scnd-div.e-de-rtl {
  margin-left: 16px;
  margin-right: 0;
}

.e-de-ff-dlg-lft-hlf {
  margin-right: 12px;
  width: 50.5%;
}

.e-de-ff-dlg-lft-hlf.e-de-rtl {
  margin-left: 12px;
  margin-right: 0;
}

.e-de-ff-chck-exact {
  margin-left: 15px;
  margin-top: -6px;
}

.e-de-ff-chck-exact.e-de-rtl {
  margin-left: 0;
  margin-right: 15px;
}

.e-de-ff-dlg-rght-hlf {
  width: 47.5%;
}

.e-de-ff-drpdwn-dlg-scndiv {
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 168px;
  margin-bottom: 4px;
  width: 100%;
}

.e-de-cmt-avatar {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  width: 95%;
}

.e-de-ff-cmt-avatar {
  -ms-flex-align: center;
      align-items: center;
  border-radius: 50%;
  color: rgba(var(--color-sf-white));
  display: -ms-flexbox;
  display: flex;
  font-size: 12px;
  height: 24px;
  -ms-flex-pack: center;
      justify-content: center;
  letter-spacing: 0;
  line-height: 12px;
  opacity: 100%;
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 24px;
  z-index: 1;
}

.e-de-ff-drpdwn-listview {
  margin-right: 12px;
  width: 100%;
}

.e-de-ff-drpdwn-listview.e-de-rtl {
  margin-left: 12px;
  margin-right: 0;
}

.e-de-ff-drpdwn-mvup {
  margin-right: 8px;
}

.e-de-ff-drpdwn-mvup.e-de-rtl {
  margin-left: 8px;
  margin-right: 0;
}

.e-de-drp-dwn-frst-div {
  margin-bottom: 8px;
  width: 47.5%;
}

.e-de-result-list-block .e-de-search-result-hglt {
  background: transparent;
  border-bottom: 2px solid rgba(var(--color-sf-primary));
  cursor: default;
  padding: 12px 1px 12px 5px;
}

.e-de-result-list-block .e-de-op-search-txt .e-de-op-search-word-text {
  color: rgba(var(--color-sf-primary));
}

.e-de-search-result-item {
  cursor: default;
  padding: 12px 1px 12px 5px;
  word-break: break-word;
}

.e-de-search-result-item:hover {
  border-bottom: 1px solid rgba(var(--color-sf-primary));
  cursor: default;
}

.e-de-search-result-item:focus {
  border-bottom: 2px solid rgba(var(--color-sf-primary));
  cursor: default;
  padding: 12px 1px 12px 5px;
}

.e-de-search-tab-content .e-input-group .e-de-op-search-icon:focus {
  border: 1px solid rgba(var(--color-sf-on-surface-variant));
}

.e-de-op-search-icon:hover {
  background: rgba(var(--color-sf-outline-variant));
}

.e-de-search-tab-content .e-input-group .e-de-op-search-close-icon:focus {
  border: 1px solid rgba(var(--color-sf-on-surface-variant));
  border-right-width: 0;
}

.e-de-op-search-close-icon:hover {
  background: rgba(var(--color-sf-outline-variant));
}

.e-spin-down:focus {
  border: 1px solid rgba(var(--color-sf-outline-variant));
  border-right-width: 0;
}

.e-spin-down:hover {
  background: rgba(var(--color-sf-outline-variant));
}

.e-spin-up:focus {
  border: 1px solid rgba(var(--color-sf-outline-variant));
  border-right-width: 0;
}

.e-spin-up:hover {
  background: rgba(var(--color-sf-outline-variant));
}

.e-de-toc-dlg-heading {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.e-de-toc-dlg-main-heading {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.e-content-placeholder.e-documenteditor.e-placeholder-documenteditor {
  background-size: 100%;
}

.e-de-toc-reset-button {
  margin-top: 10px;
}

.e-de-toc-reset-button.e-de-rtl {
  margin-right: 0;
}

.e-de-toc-modify-button {
  margin-left: 156px;
  margin-top: 10px;
}

.e-de-toc-modify-button.e-de-rtl {
  margin-left: 0;
  margin-right: 156px;
}

.e-de-toc-dlg-container {
  height: 454px;
  width: 550px;
}

.e-de-toc-dlg-sub-container {
  margin-bottom: 8px;
}

.e-de-toc-list-view {
  border: 1px solid rgba(var(--color-sf-outline));
  border-radius: 3px;
  font-size: 12px;
  height: 186px;
  overflow-y: scroll;
}

.e-de-toc-list-view.e-de-rtl {
  margin-left: 0;
}

.e-de-toc-dlg-sub-heading {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin: 5px 15px 5px 15px;
}

.e-de-toc-dlg-style-label {
  margin-left: 42px;
  margin-top: 35px;
}

.e-de-toc-dlg-style-label .e-de-rtl {
  margin-left: 0;
  margin-right: 42px;
}

.e-de-pagesetup-dlg-container {
  height: auto;
  width: 380px;
}

.e-de-page-setup-ppty-tab {
  border: 0;
}

.e-de-page-setup-dlg-sub-container {
  margin-bottom: 12px;
}

.e-de-page-setup-dlg-left-sub-container {
  float: left;
  position: relative;
  top: 0;
}

.e-de-page-setup-dlg-left-sub-container.e-de-rtl {
  float: right;
}

.e-de-page-setup-dlg-right-sub-container {
  float: right;
  position: relative;
  top: 0;
}

.e-de-page-setup-dlg-right-sub-container.e-de-rtl {
  float: left;
}

.e-de-page-setup-dlg-sub-header {
  display: block;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 2px;
  margin-top: 16px;
}

.e-de-page-setup-dlg-sub-title-header {
  display: block;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 2px;
  margin-top: 12px;
}

.e-de-page-setup-dlg-sub-container-port {
  height: auto;
  margin-bottom: 0;
}

.e-de-page-setup-dlg-sub-label {
  font-size: 14px;
  font-weight: 600;
}

.e-de-page-setup-dlg-orientation-prop {
  margin-top: 6px;
}

.e-de-page-setup-dlg-sub-size-container {
  height: 73px;
  margin-bottom: 12px;
}

.e-de-page-setup-dlg-layout-sub-container {
  height: auto;
  margin-bottom: 12px;
  position: relative;
  top: 12px;
}

.e-de-page-setup-dlg-first-page-prop {
  margin-bottom: 8px;
}

.e-de-page-setup-dlg-first-page-prop .e-label,
.e-de-page-setup-dlg-odd-or-even-prop .e-label {
  font-size: 12px;
}

.e-de-page-setup-dlg-first-page-prop .e-frame,
.e-de-page-setup-dlg-odd-or-even-prop .e-frame {
  height: 18px;
  line-height: 1.5;
  width: 18px;
}

.e-de-page-setup-dlg-left-layout-container {
  float: left;
  position: relative;
  top: 0;
}

.e-de-page-setup-dlg-left-layout-container.e-de-rtl {
  float: right;
}

.e-de-page-setup-dlg-right-layout-container {
  float: right;
  position: relative;
  top: 0;
}

.e-de-page-setup-dlg-right-layout-container.e-de-rtl {
  float: left;
}

.e-de-dlg-footer .e-btn {
  margin-left: 10px;
}

.e-de-hyperlink-dlg-title {
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 3px;
  margin-top: 0;
}

.e-de-hyperlink .e-de-hyperlink-dlg-input {
  height: 32px;
  margin-bottom: 8px;
  width: 240px;
}

.e-de-font-dlg-header {
  display: -ms-flexbox;
  display: flex;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 4px;
}

.e-de-font-dlg-header-effects,
.e-de-font-dlg-header-font-color {
  display: -ms-flexbox;
  display: flex;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 0;
}

.e-de-font-dlg-main-header {
  color: rgba(var(--color-sf-on-surface-variant));
  font-size: 14px;
  font-weight: 400;
  margin-right: 17px;
}

.e-de-font-dlg-cb-right {
  margin-left: 12px;
}

.e-de-font-dlg-cb-right.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
}

.e-de-font-dlg-cb-right-div {
  margin-left: 20px;
}

.e-de-dropdown {
  margin-right: 20px;
}

.e-de-restrict-pane {
  border-right: 1px solid rgba(var(--color-sf-outline-variant));
  padding-left: 12px;
  padding-top: 12px;
  padding-right: 12px;
  position: relative;
  width: 300px;
}

.e-de-op {
  border-right: 1px solid rgba(var(--color-sf-outline-variant));
  padding-left: 12px;
  padding-right: 12px;
  position: relative;
  width: 300px;
}

.e-de-op.e-de-rtl {
  padding-left: 0;
  padding-right: 12px;
}

.e-de-op-header {
  color: rgba(var(--color-sf-on-surface));
  font-family: inherit;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 1px;
  padding-top: 12px;
}

.e-de-op-header.e-de-rtl {
  direction: rtl;
  text-align: right;
}

.e-de-op-tab {
  border: 0;
  height: auto;
}

.e-de-op-icon {
  color: rgba(var(--color-sf-on-surface-variant));
  height: 20px;
  width: 20px;
}

.e-de-op-close-icon {
  color: rgba(var(--color-sf-on-surface-variant));
}

.e-de-op-nav-btn {
  height: 20px;
  width: 20px;
}

.e-de-op-search-txt {
  border-bottom: 1px solid rgba(var(--color-sf-outline-variant));
  color: rgba(var(--color-sf-on-surface-variant));
  font-size: 14px;
}

.e-de-op-search-txt .e-de-op-search-word {
  color: rgba(var(--color-sf-primary));
}

.e-de-op-more-less {
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-top: 16px;
}

.e-de-op-replacetabcontentdiv {
  margin-top: 16px;
}

label[for*=_wholeWord_e-de-ltr] {
  left: 35px;
}

label[for*=_wholeWord_e-de-rtl] {
  right: 35px;
}

.e-de-cell-dia-label-common {
  display: inline-block;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 4px;
  margin-top: 0;
  width: 150px;
}

.e-de-cell-dia-options-label {
  font-weight: 600;
}

.e-de-table-border-heading {
  font-size: 14px;
  font-weight: 500;
  padding-bottom: 16px;
}

.e-de-table-setting-heading {
  font-size: 12px;
  font-weight: 500;
  padding-bottom: 4px;
}

.e-de-layout-setting-heading {
  font-size: 12px;
  font-weight: 500;
  padding-bottom: 4px;
}

.e-de-table-setting-labels-heading {
  font-size: 13;
  font-weight: 500;
  margin-left: 62px;
}

.e-de-table-element-subheading {
  font-size: 13;
  font-weight: 500;
}

.e-de-border-dlg-preview-div {
  border: 1px solid rgba(0, 0, 0, 0.54);
  width: 80px;
  height: 80px;
}

.e-de-border-dlg-preview-inside-divs {
  opacity: 0.54;
}

.e-de-tablecell-dia-align-div {
  border: 1px solid rgba(var(--color-sf-outline));
  display: inline-block;
  height: 60px;
  margin-right: 11px;
  width: 60px;
}

.e-de-tablecell-dia-align-div.e-de-rtl {
  margin-left: 11px;
  margin-right: 0;
}

.e-de-table-dia-align-label {
  display: inline-block;
  font-size: 12px;
  margin-left: 62px;
  margin-top: 4px;
}

.e-de-table-dialog-separator-line {
  background-color: rgba(var(--color-sf-outline));
  bottom: 59px;
  display: none;
  height: 1px;
  left: 1px;
  margin-top: 5px;
  position: absolute;
  width: 100%;
}

.e-de-table-alignment-active {
  border: 1px solid rgba(var(--color-sf-primary));
}

.e-de-table-dialog-options-label {
  font-size: 12px;
  font-weight: 600;
  padding-bottom: 8px;
}

.e-de-table-dialog-size-label {
  padding-top: 16px !important;
  font-weight: 600;
  font-size: 12px;
}

.e-de-list-ddl-header {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  margin-top: 12px;
}

.e-de-list-ddl-header-list-level {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.e-de-tbl-dlg-footer {
  padding-top: 23px;
}

.e-de-row-ht-top,
.e-de-cell-ht-top {
  padding: 0 12px;
  width: 160px;
}

.e-de-ht-wdth-type {
  margin-top: -22px;
  width: 120px;
}

.e-de-row-ht-top.e-de-rtl,
.e-de-cell-ht-top.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
}

.e-de-cell-width-top {
  margin-left: 20px;
  margin-top: -23px;
}

.e-de-cell-width-top.e-de-rtl {
  margin-left: 0;
  margin-right: 20px;
}

.e-de-tbl-dlg-border-btn {
  float: right;
  margin-top: 24px;
}

.e-de-tbl-dlg-border-btn.e-de-rtl {
  float: left;
  margin-right: 0;
}

.e-de-table-border-setting.e-de-rtl {
  right: 5px;
}

.e-de-tbl-dlg-op-btn {
  left: 440px;
  position: absolute;
  top: 285px;
}

.e-de-insert-table-dlg-sub-header {
  display: block;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 4px;
  margin-top: 0;
}

.e-de-insert-footnote-dlg-sub-header,
.e-de-insert-footnote-dlg-header {
  display: block;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 4px;
  margin-top: 0;
}

.e-de-insert-footnote-dlg-header {
  margin-bottom: 12px;
}

.e-de-insert-table-dlg-input {
  margin-bottom: 16px;
}

.e-de-list-ddl-subheader,
.e-de-list-ddl-subheaderbottom {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  margin-top: 12px;
}

.e-de-list-dlg-subdiv {
  float: right;
  margin-top: 40px;
  position: relative;
}

.e-de-list-dlg-subdiv.e-de-rtl {
  float: left;
  margin-top: -121px;
}

.e-de-list-dlg-div {
  float: right;
  margin-top: 36px;
  position: relative;
}

.e-de-list-dlg-div.e-de-rtl {
  float: left;
  margin-top: -125px;
}

.e-de-ok-button {
  margin-right: 8px;
}

.e-de-ok-button.e-de-rtl {
  margin-left: 8px;
}

.e-de-options-setter {
  left: 339px;
}

.e-de-op-close-icon:hover {
  color: rgba(0, 0, 0, 0.75);
}

.e-de-tooltip {
  background-color: rgba(var(--color-sf-surface));
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.26);
  color: rgba(var(--color-sf-on-surface));
  cursor: text;
  display: table;
  max-width: 200px;
  padding: 5px;
  word-wrap: break-word;
}

.e-de-form-popup {
  background-color: rgba(var(--color-sf-surface));
  box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.26);
  color: rgba(var(--color-sf-on-surface));
  cursor: text;
  max-width: 350px;
  min-width: 300px;
  padding: 16px;
  position: absolute;
  width: fit-content;
  word-wrap: break-word;
}

.e-de-save,
.e-de-cancel {
  margin-left: 8px;
}

.e-btn.e-de-op-icon-btn {
  background-color: transparent;
  border-color: transparent;
}

.e-documenteditor .e-de-op-close-button {
  left: 250px;
  position: absolute;
  top: 8px;
}

.e-de-restrict-pane {
  color: rgba(var(--color-sf-on-surface));
}

.e-de-op.e-de-rtl .e-de-search-tab-content {
  margin-left: 12px;
  margin-right: 0;
}

.e-documenteditor .e-de-op-close-button.e-de-rtl {
  right: 255px;
}

.e-de-table-measure-lbl {
  font-size: 12px;
  font-weight: 400;
  display: block;
  margin-bottom: 4px;
}

.e-de-tbl-indent-lbl {
  font-weight: 400;
  font-size: 12px;
  position: relative;
  display: block;
  margin-bottom: 0;
  top: 6px;
}

.e-btn.e-de-op-close-button:hover {
  background-color: transparent;
  border-color: transparent;
  color: rgba(0, 0, 0, 0.75);
}

.e-btn.e-de-op-close-button:focus {
  background-color: transparent;
  border-color: transparent;
  color: rgba(0, 0, 0, 0.75);
}

.e-btn.e-de-op-close-button:active {
  background-color: transparent;
  border-color: transparent;
  color: rgba(var(--color-sf-on-surface-variant), 0.5);
}

.e-documenteditor .e-input {
  font-size: 14px;
}

.e-de-dlg-target .e-footer-content .e-control.e-btn.e-flat:not(.e-icon-btn) {
  height: 32px;
}

.e-de-tbl-dlg-border-btn .e-control.e-btn.e-flat:not(.e-icon-btn) {
  height: auto;
}

.e-de-op-result-container {
  margin-top: 12px;
}

.e-de-restrict-pane,
.e-de-op {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.05), rgba(var(--color-sf-primary), 0.05)), rgba(var(--color-sf-surface));
}

.e-de-restrict-pane .e-tab-header .e-toolbar-items,
.e-de-op .e-tab-header .e-toolbar-items {
  margin-bottom: 0;
  margin-top: 0;
}

.e-de-font-dlg-color {
  border: 1px linear-gradient(0deg, rgba(var(--color-sf-primary), 0.05), rgba(var(--color-sf-primary), 0.05)), rgba(var(--color-sf-surface));
  border-radius: 4px;
  font-size: 12px;
  height: 16px;
  margin-left: 15px;
  width: 25px;
}

.e-de-icon-table-row-above {
  top: 10px;
}

.e-de-icon-table-row-below {
  top: 49px;
}

.e-de-icon-table-column-left {
  top: 89px;
}

.e-de-icon-table-column-right {
  top: 127px;
}

.e-de-icon-table-delete {
  top: 10px;
}

.e-de-icon-table-row-delete {
  top: 49px;
}

.e-de-icon-table-column-delete {
  top: 89px;
}

.e-de-list-bullet-none {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-dot {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-circle {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-square {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-flower {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-arrow {
  height: 40px;
  width: 40px;
}

.e-de-list-bullet-tick {
  height: 40px;
  width: 40px;
}

.e-de-bullet:hover {
  background: rgba(0, 0, 0, 0.12);
}

.e-de-list-numbered-none {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-number-dot {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-number-brace {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-up-roman {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-up-letter {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-low-letter-brace {
  height: 80px;
  width: 80px;
}

.e-de-numbered-low-letter-dot {
  height: 80px;
  width: 80px;
}

.e-de-list-numbered-low-roman {
  height: 80px;
  width: 80px;
}

.e-de-numbered:hover {
  background: rgba(0, 0, 0, 0.12);
}

.e-de-list-multilevel-none {
  height: 80px;
  width: 80px;
}

.e-de-list-multilevel-list-normal {
  height: 80px;
  width: 80px;
}

.e-de-list-multilevel-list-multilevel {
  height: 80px;
  width: 80px;
}

.e-de-list-multilevel-list-bullets {
  height: 80px;
  width: 80px;
}

.e-de-multilevel-list:hover {
  background: rgba(0, 0, 0, 0.12);
}

.e-de-list-dialog-open:hover {
  background: rgba(0, 0, 0, 0.12);
}

.e-de-cell-options {
  left: 336px;
  top: 272px;
}

.e-de-cell-options.e-de-rtl {
  left: 123px;
}

.e-de-font-color-label {
  margin-bottom: 8px;
  margin-right: 12px;
  margin-top: 12px;
}

.e-de-font-content-label {
  width: 125px;
}

.e-de-font-color-margin {
  margin-right: 8px;
  margin-top: 5px;
}

.e-de-font-color-margin.e-de-rtl {
  margin-left: 8px;
  margin-right: 0;
}

.e-de-font-content-checkbox-label {
  margin-left: 46px;
}

.e-de-font-content-checkbox-label-rtl {
  margin-right: 41px;
}

.e-bigger .e-de-font-content-checkbox-label {
  margin-left: 64px;
}
.e-bigger .e-de-font-content-checkbox-label-rtl {
  margin-right: 64px;
}
.e-bigger .e-de-font-content-label {
  width: 154px;
}

.e-de-font-checkbox {
  margin-top: 8px;
}

.e-de-font-checkbox-transform {
  margin-left: 58px;
}

.e-de-font-checkbox-transform.e-de-rtl {
  margin-left: 0;
  margin-right: 58px;
}

.e-de-font-checkbox-transform-label {
  margin-bottom: 8px;
  margin-top: 8px;
}

.e-de-font-checkbox.e-de-rtl {
  margin-left: 0;
  margin-right: 58px;
}

.e-de-font-checkbox.e-de-rtl {
  margin-left: 0;
  margin-right: 58px;
}

.e-de-font-clr-div {
  margin-top: 12px;
}

.e-de-font-dlg-padding {
  margin-top: 12px;
}

.e-de-table-container-div {
  margin-top: 11px;
}

.e-de-table-header-div {
  padding-top: 0;
}

.e-de-table-subheader-div {
  float: right;
  margin-right: 140px;
  margin-top: -40px;
}

.e-de-table-subheader-div.e-de-rtl {
  float: left;
  margin-left: 140px;
  margin-right: 0;
}

.e-de-table-cell-header-div {
  padding-top: 0;
}

.e-de-table-cell-subheader-div {
  top: 57px;
}

.e-de-cell-margin-header {
  left: -26px;
  top: 274px;
}

.e-de-font-dlg-display {
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.e-de-tbl-margin-sub-header {
  margin-top: 10px;
}

.e-de-tbl-btn-separator {
  width: 60%;
}

.e-de-op-msg {
  color: rgba(var(--color-sf-on-surface));
  top: 79px;
}

.e-de-save-dlg-file-name {
  height: 25px;
  margin-bottom: 8px;
}

.e-de-save-dlg-format-type {
  height: 25px;
  margin-bottom: 8px;
  padding-top: 1px;
}

.e-de-search-tab-content {
  margin-top: 12px;
  width: 275px;
}

.e-de-font-dlg {
  width: max-content;
  padding: 8px;
}

.e-de-hyperlink {
  width: 240px;
}

.e-de-table-border-shading-dlg .e-de-dlg-row .e-de-dlg-row label {
  margin-left: 10px;
}

.e-de-table-setting-heading {
  line-height: 22px;
  letter-spacing: 0.3000000119px;
}

.e-de-rp-btn-enforce {
  border-radius: 4px !important;
}

.e-de-track-toolbar .e-de-track-pane-drop-btn, #e-de-menu-option {
  background: none;
}

.e-bigger .e-de-hyperlink {
  width: 320px;
}

.e-de-insert-table {
  height: auto;
  width: auto;
}

.e-de-insert-footnote {
  height: auto;
  width: 280px;
}

.e-de-insert-spellchecker {
  height: 360px;
  width: 513px;
  font-weight: 400;
}

.e-bigger .e-de-insert-spellchecker {
  width: 600px;
}

.e-de-dlg-spellcheck-listview {
  border: 1px solid rgba(var(--color-sf-outline-variant));
  border-radius: 2px !important;
  height: 122px !important;
  margin-right: 12px;
  margin-top: 8px;
  position: relative;
  float: left;
  width: 100%;
}

.e-de-dlg-spellcheck-listview.e-de-rtl {
  float: right;
}

.e-de-spellcheck-error-container {
  height: 140px;
  margin-bottom: 16px;
  display: -ms-flexbox;
  display: flex;
}

.e-de-spellcheck-suggestion-container {
  height: 140px;
  margin-bottom: 16px;
  display: -ms-flexbox;
  display: flex;
}

.e-dlg-spellcheck-listitem {
  font-size: 15px !important;
}

.e-de-spellcheck-btncontainer {
  margin-top: 8px;
  position: relative;
  width: 100%;
  float: right;
}

.e-de-spellcheck-btncontainer.e-de-rtl {
  float: left;
}

.e-de-spellcheck-btn {
  margin-bottom: 10px;
  width: 100%;
}

.e-de-dlg-spellchecker-subheader {
  margin-top: 8px;
  font-size: 15px;
}

.e-de-dlg-spellchecker-subheaderbtm {
  font-size: 15px;
}

.e-de-list-dlg {
  height: 405px;
  width: 400px;
}

.e-de-save-dlg {
  height: 135px;
  width: 230px;
}

.e-de-table-properties-dlg {
  padding: 0px 8px;
  width: 430px;
}

.e-de-table-border-shading-dlg {
  width: 440px;
  height: 400px;
}

.e-de-table-cell-margin-dlg {
  height: auto;
  width: 412px;
  padding: 0px 8px;
}

.e-de-table-options-dlg {
  height: auto;
  width: 395px;
}

.e-de-table-border-none {
  position: absolute;
  top: 30px;
}

.e-de-table-border-box {
  position: absolute;
  top: 95px;
}

.e-de-table-border-all {
  position: absolute;
  top: 165px;
}

.e-de-table-border-custom {
  position: absolute;
  top: 235px;
}

.e-de-table-shading-preview {
  top: 385px;
}

.e-de-font-label span.e-label {
  color: rgba(var(--color-sf-on-surface));
}

.e-de-font-content-label:hover .e-label,
.e-css.e-de-font-content-label:hover .e-label {
  color: rgba(var(--color-sf-on-surface));
}

.e-de-font-label:hover .e-label,
.e-css.e-de-font-label:hover .e-label {
  color: rgba(var(--color-sf-on-surface));
}

.e-de-op-dlg-footer {
  margin-top: 18px;
}

.e-de-op-dlg-footer .e-btn {
  padding-left: 6px;
  padding-right: 6px;
}

.e-de-search-tab-content .e-input-group .e-de-search-input {
  width: 204px;
}

.e-de-op-replacewith {
  width: 98%;
}

.e-de-table-ppty-tab {
  border: 0;
}

.e-de-list-format-info {
  border-radius: 50% !important;
  cursor: default;
  font-size: 12px !important;
  height: 15px;
  line-height: 1px;
  padding: 3px 0 0 0 !important;
  text-transform: lowercase;
  width: 16px;
}

.e-button-custom {
  height: 32px;
  width: 100%;
}

.e-bigger .e-button-custom {
  height: 37px;
  width: 100%;
}

.e-styles-listview,
.e-bookmark-listview {
  border: 1px solid rgba(var(--color-sf-outline));
  border-radius: 4px;
  height: 150px;
  overflow-y: scroll;
}

.e-bookmark-gotobutton,
.e-bookmark-addbutton,
.e-styles-addbutton,
.e-bookmark-deletebutton {
  margin-bottom: 12px;
}

.e-bookmark-list {
  float: left;
  margin-right: 12px;
  width: 250px;
}

.e-bookmark-list.e-de-rtl {
  margin-left: 12px;
  margin-right: 0;
}

.e-bookmark-textboxdiv {
  margin-bottom: 8px;
}

.e-bookmark-listview .e-list-item {
  font-size: 13px;
  height: 30px;
  line-height: 27px;
}

.e-bookmark-common {
  display: -ms-flexbox;
  display: flex;
}

.e-bookmark-button {
  position: relative;
  top: 0;
}

.e-font {
  float: left;
}

.e-font-rtl {
  float: right;
}

.e-de-table-border-toptop-alignment,
.e-de-table-border-topcenter-alignment,
.e-de-table-border-topbottom-alignment,
.e-de-table-border-diagionalup-alignment,
.e-de-table-border-diagionaldown-alignment,
.e-de-table-border-bottomleft-alignment,
.e-de-table-border-bottomcenter-alignment,
.e-de-table-border-bottomright-alignment {
  left: 48%;
  position: relative;
  top: 59%;
  transform: translate(-50%, -50%);
}

.e-de-style-properties,
.e-de-style-formatting {
  font-size: 14px;
  font-weight: 600;
  color: rgba(var(--color-sf-on-surface-variant));
}

.e-de-style-formatting {
  margin-bottom: 8px;
}

.e-de-style-paragraph-indent-group-button .e-btn,
.e-de-style-paragraph-group-button .e-btn,
.e-de-style-font-group-button .e-btn {
  box-shadow: none;
}

.e-de-table-options-dlg-div {
  height: auto;
  position: relative;
  margin-bottom: 14px;
  width: 504px;
}

.e-de-style-paragraph-indent-group-button .e-btn.e-active,
.e-de-style-paragraph-group-button .e-btn.e-active,
.e-de-style-font-group-button .e-btn.e-active {
  background-color: rgba(var(--color-sf-primary-container));
  border-color: rgba(var(--color-sf-primary-container));
  box-shadow: none;
  color: rgba(var(--color-sf-on-surface));
}

.e-de-style-properties {
  margin-bottom: 12px;
}

.e-de-style-nametype-div {
  margin-bottom: 20px;
}

.e-de-style-based-para-div {
  margin-bottom: 12px;
}

.e-de-style-name,
.e-de-style-styletype,
.e-de-style-style-based-on,
.e-de-style-style-paragraph {
  font-weight: 400;
  margin-bottom: 8px;
  font-size: 12px;
  width: 180px;
}

.e-de-style-left-div {
  margin-right: 20px;
}

.e-de-style-left-div.e-de-rtl {
  margin-left: 20px;
  margin-right: 0;
}

.e-de-style-font-color-picker,
.e-de-style-icon-button-size,
.e-de-style-icon-button-first-size,
.e-de-style-icon-button-last-size {
  height: 31px;
}

.e-bigger .e-de-style-font-color-picker,
.e-bigger .e-de-style-icon-button-size,
.e-bigger .e-de-style-icon-button-first-size,
.e-bigger .e-de-style-icon-button-last-size {
  height: 35px;
}
.e-bigger .e-de-style-bold-button-size {
  margin-left: 8px;
}

.e-bigger .e-dlg-container .e-de-dlg-target .e-de-style-dialog .e-de-style-font-group-button button,
.e-bigger .e-dlg-container .e-de-dlg-target .e-de-style-dialog .e-style-paragraph button {
  height: 40px !important;
  width: 44px !important;
}
.e-bigger .e-dlg-container .e-de-dlg-target .e-de-style-dialog .e-de-style-font-color-picker button {
  height: 40px !important;
  width: auto !important;
}

.e-bigger .e-dlg-container .e-de-dlg-target .e-de-style-dialog .e-de-style-font-group-button button,
.e-bigger .e-dlg-container .e-de-dlg-target .e-de-style-dialog .e-style-paragraph button {
  height: 40px !important;
  width: 44px !important;
}
.e-bigger .e-dlg-container .e-de-dlg-target .e-de-style-dialog .e-de-style-font-color-picker button {
  height: 40px !important;
  width: auto !important;
}

.e-bigger .e-rtl .e-de-style-font-group-button button,
.e-bigger .e-rtl .e-de-style-paragraph-indent-group-button button,
.e-bigger .e-rtl .e-de-style-paragraph-group-button button {
  height: 40px !important;
  width: 44px !important;
}
.e-bigger .e-de-style-font-group-button button,
.e-bigger .e-de-style-paragraph-indent-group-button button,
.e-bigger .e-de-style-paragraph-group-button button {
  height: 40px !important;
  width: 44px !important;
}

.e-de-style-font-group-button button,
.e-de-style-paragraph-indent-group-button button,
.e-de-style-paragraph-group-button button {
  width: 36px;
  border-radius: 4px;
  margin-right: 5px;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
}

.e-de-style-bold-button-size {
  margin-left: 8px;
  margin-right: 8px;
  height: 31px;
}

.e-de-style-format-dropdwn .e-btn-icon {
  margin-left: 8px;
}

.e-de-style-font-color-picker,
.e-de-style-icon-button-size {
  margin-right: 8px;
}

.e-de-style-icon-button-first-size {
  margin-left: 8px;
  margin-right: 3px;
}

.e-de-style-icon-button-last-size {
  margin-right: 8px;
}

.e-de-style-font-color-picker {
  margin-left: 8px;
}

.e-style-font-fmaily-right {
  margin-right: 8px;
}

.e-style-font {
  margin-left: 20px;
  margin-right: 20px;
}

.e-de-style-dlg-name-input {
  height: 32px;
}

.e-style-list {
  margin-left: 20px;
}

.e-de-style-dialog .e-de-style-only-this-document {
  margin-top: 25px;
}

.e-de-style-format-dropdwn {
  width: 135px;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
  border-radius: 2px;
}

.e-de-style-options-div,
.e-de-style-options-div > div:first-child {
  margin-bottom: 12px !important;
}

.e-de-op-replace-messagediv {
  color: rgba(var(--color-sf-on-surface));
  top: auto;
}

.e-de-font-content-label .e-label,
.e-de-font-dlg-cb-right .e-label,
.e-de-font-checkbox .e-label {
  font-size: 14px;
}

.e-de-font-content-label .e-frame,
.e-de-font-dlg-cb-right .e-frame,
.e-de-font-checkbox .e-frame,
.e-de-font-content-label-caps .e-frame,
.e-de-cntr-pane-padding .e-frame {
  height: 18px;
  line-height: 1.5;
  width: 18px;
}

.e-de-op-input-group,
.e-de-op-replacewith {
  height: auto;
}

.e-bigger .e-de-op-input-group,
.e-bigger .e-de-op-replacewith {
  height: 40px;
}

.e-de-hyperlink-bookmark-check {
  margin-top: 8px;
}

.e-de-table-container-div .e-checkbox-wrapper .e-frame,
.e-de-table-header-div .e-checkbox-wrapper .e-frame,
.e-de-table-ppty-options-break .e-checkbox-wrapper .e-frame,
.e-de-table-ppty-options-header-row .e-checkbox-wrapper .e-frame,
.e-de-table-cell-header-div .e-checkbox-wrapper .e-frame,
.e-de-tbl-btn-separator .e-checkbox-wrapper .e-frame,
.e-de-hyperlink-bookmark-check .e-checkbox-wrapper .e-frame,
.e-de-tbl-margin-sub-header .e-frame {
  height: 18px;
  line-height: 1.5;
  width: 18px;
}

.e-de-table-container-div .e-checkbox-wrapper .e-label,
.e-de-table-header-div .e-checkbox-wrapper .e-label,
.e-de-table-ppty-options-break .e-checkbox-wrapper .e-label,
.e-de-table-ppty-options-header-row .e-checkbox-wrapper .e-label,
.e-de-table-cell-header-div .e-checkbox-wrapper .e-label,
.e-de-tbl-btn-separator .e-checkbox-wrapper .e-label,
.e-de-hyperlink-bookmark-check .e-checkbox-wrapper .e-label,
.e-de-tbl-margin-sub-header .e-label {
  font-size: 14px;
}

.e-de-table-container-div .e-checkbox-wrapper .e-label,
.e-de-table-header-div .e-checkbox-wrapper .e-label,
.e-de-table-ppty-options-break .e-checkbox-wrapper .e-label,
.e-de-table-ppty-options-header-row .e-checkbox-wrapper .e-label,
.e-de-table-cell-header-div .e-checkbox-wrapper .e-label,
.e-de-tbl-btn-separator .e-checkbox-wrapper .e-label,
.e-de-hyperlink-bookmark-check .e-checkbox-wrapper .e-label,
.e-de-tbl-margin-sub-header .e-label {
  font-size: 14px;
}

.e-de-table-ppty-dlg-measure-div {
  float: right;
  margin-left: 12px;
  margin-top: -18px;
}

.e-de-table-ppty-dlg-measure-div.e-de-rtl {
  float: left;
  margin-left: 0;
  margin-right: 12px;
}

.e-de-table-ppty-dlg-measure-drop-down-div {
  float: right;
  margin-left: 12px;
  margin-top: 6px;
  width: 120px;
}

.e-de-table-ppty-dlg-measure-drop-down-div.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
}

.e-de-table-ppty-dlg-left-indent-container {
  bottom: 5px;
  left: 46px;
  position: relative;
}

.e-de-table-ppty-dlg-left-indent-container.e-de-rtl {
  right: 46px;
}

.e-de-table-ppty-dlg-row-height-label {
  float: right;
  margin-right: 184px;
  margin-top: -62px;
}

.e-de-table-ppty-dlg-row-height-label.e-de-rtl {
  float: left;
  margin-left: 184px;
  margin-right: 0;
}

.e-de-table-ppty-dlg-preferred-width-div {
  float: right;
  margin-left: 12px;
  margin-top: 6px;
  width: 120px;
}

.e-de-table-ppty-dlg-preferred-width-div.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
  width: 120px;
}

.e-de-table-ppty-options-break {
  margin-bottom: 8px;
}

.e-de-table-cell-subheader-div {
  margin-right: 125px;
  margin-top: -37px;
}

.e-de-table-cell-subheader-div.e-de-rtl {
  margin-left: 125px;
  margin-right: 0;
}

.e-de-table-ppty-dlg-cell-tab-measure-label {
  float: right;
  margin-right: 190px;
  margin-top: -58px;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-table-ppty-dlg-row-header {
  padding-left: 12px;
  padding-right: 12px;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-table-ppty-dlg-cell-header {
  padding-left: 12px;
  padding-right: 12px;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-page-setup-dlg-margin-tab-header {
  padding-left: 15px;
  padding-right: 12px;
}

.e-styles-list {
  float: left;
  margin-right: 12px;
  width: 250px;
}

.e-styles-textboxdiv {
  padding-bottom: 15px;
}

.e-styles-listview .e-list-item {
  font-size: 13px;
  height: 30px;
  line-height: 27px;
}

.e-styles-common {
  padding-top: 5px;
}

.e-styles-button {
  float: right;
}

.e-de-toc-dlg-right-sub-container.e-de-rtl {
  margin-left: 2px;
  margin-right: 2px;
}

.e-de-toc-dlg-styles {
  margin-bottom: 11px;
  margin-left: 38px;
  margin-top: 12px;
}

.e-de-toc-dlg-styles.e-de-rtl {
  margin-left: 0;
  margin-right: 38px;
}

.e-de-toc-dlg-style-input.e-de-rtl {
  margin-left: 0;
  margin-right: 38px;
}

.e-de-toc-table-div .e-de-toc-dlg-toc-level {
  height: 24px;
  margin-left: 36px;
  width: 44px;
}

.e-de-toc-styles-table-div {
  border: 1px solid rgba(var(--color-sf-outline));
  border-radius: 3px;
  margin-top: 10px;
  width: 100%;
  height: 183px;
}

.e-de-toc-dlg-sub-level-heading {
  font-size: 12px;
}

.e-de-toc-table-div {
  height: 143px;
  overflow-y: scroll;
  width: 100%;
}

.e-de-toc-dlg-style-input {
  margin-bottom: 3px;
  margin-left: 38px;
  width: 210px;
}

.e-de-toc-dlg-outline-levels {
  margin-top: 15px;
  width: 150px;
}

.e-bookmark-textboxdiv .e-bookmark-textbox-input {
  height: 32px;
}

.e-styles-dlgfields {
  font-weight: 400;
  margin-bottom: 6px;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-op-find-tab-header {
  padding-left: 15px;
  padding-right: 12px;
}

.e-tab .e-tab-header .e-toolbar-item .e-de-op-replace-tab-header {
  padding-left: 15px;
  padding-right: 12px;
}

.e-de-dlg-target .e-footer-content .e-list-dlg-font {
  margin-left: 1px;
}

.e-bookmark-dlgfields {
  font-weight: 400;
  font-size: 12px;
  margin-bottom: 4px;
}

.e-de-ui-wfloating-menu.e-de-ui-bullets-menu .e-de-ui-wfloating-menuitem-md {
  height: 64px;
  padding: 8px 10px 8px 10px;
  width: 64px;
}

.e-de-ui-wfloating-menu.e-de-ui-bullets-menu .e-de-ui-wfloating-bullet-menuitem-md {
  height: 45px;
  width: 45px;
}

.e-de-bullet-icon-size {
  height: 45px;
  width: 45px;
}

.e-de-ui-list-header-presetmenu {
  cursor: pointer;
  font-size: 11px;
  line-height: 14px;
  min-width: 40px;
  overflow: hidden;
  text-align: left;
  white-space: nowrap;
  width: 100%;
}

.e-de-number-bullet-dlg .e-tab .e-content .e-item {
  padding: 0px;
}

.e-de-number-bullet-dlg {
  width: 300px;
}

.e-de-style-numbered-list,
.e-de-ui-bullet-list-header-presetmenu {
  height: 172px;
}

.e-de-ui-bullet {
  font-size: 42px;
}

.e-de-ui-list-header-presetmenu .e-de-ui-list-line {
  border-bottom: 1px solid rgba(var(--color-sf-on-surface-variant));
  margin-left: 5px;
  width: 100%;
}

.e-de-ui-list-header-presetmenu div span {
  color: rgba(var(--color-sf-on-surface-variant));
  display: inline-block;
  vertical-align: middle;
}

.e-de-ui-wfloating-menu .e-de-ui-wfloating-menuitem,
.e-de-ui-wfloating-menu .e-de-ui-menuitem-none {
  border: 0;
  box-shadow: inset 0 0 0 1px rgba(var(--color-sf-outline));
  cursor: pointer;
  height: 70px;
  margin: 0 8px 8px 0;
  padding: 0;
  width: 70px;
}

.e-de-ui-wfloating-menu {
  padding: 0;
}

.e-de-list-thumbnail .e-de-list-items {
  float: left;
}

.e-de-list-thumbnail .e-de-list-items {
  background: rgba(var(--color-sf-surface));
  border: 1px solid transparent;
  clear: initial;
  display: inline-block;
  height: auto;
  margin: 5px;
  text-align: center;
  width: auto;
}

.e-de-list-items {
  background: rgba(var(--color-sf-surface));
  box-sizing: border-box;
  cursor: pointer;
  list-style: none;
  padding: 7px 10px;
  position: relative;
}

.e-de-list-item-size {
  font-size: 14px;
}

.e-de-ui-wfloating-menu {
  padding: 0;
}

.e-de-table-ppty-dlg-tabs {
  height: 280px;
  position: relative;
}

.e-de-ui-bullet-list-header-presetmenu .e-de-list-thumbnail .e-de-list-active,
.e-de-style-numbered-list .e-de-list-thumbnail .e-de-list-active {
  border-color: rgba(var(--color-sf-primary));
}

.e-de-rp-sub-content-div {
  line-height: 1.5;
  font-size: 12px;
  margin-bottom: 8px;
}

.e-de-bullet-icons {
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
}

.e-de-header-footer-list {
  color: rgba(var(--color-sf-primary));
}

.e-de-ltr-btn-div {
  font-size: 12px;
  width: 100px;
}

.e-de-tbl-rtl-btn-div {
  font-size: 12px;
  margin-right: 14px;
}

.e-de-tbl-rtl-btn-div.e-de-rtl {
  margin-left: 14px;
  margin-right: 0;
}

.e-de-tbl-ltr-btn-div {
  font-size: 12px;
}

.e-de-disabledbutton {
  opacity: 0.4;
  pointer-events: none;
}

.e-bigger .e-de-insert-table {
  height: auto;
  width: auto;
}
.e-bigger .e-de-insert-footnote {
  height: auto;
  width: auto;
}
.e-bigger .e-de-dlg-target .e-footer-content .e-control.e-btn.e-flat:not(.e-icon-btn) {
  height: auto;
}
.e-bigger .e-de-font-dlg {
  width: max-content;
}
.e-bigger .e-para-dlg-sub-height {
  height: 170px;
}
.e-bigger .e-de-toc-table-div .e-de-toc-dlg-toc-level.e-de-rtl {
  margin-right: 36px;
}
.e-bigger .e-de-font-content-label-width {
  width: 90px;
}
.e-bigger .e-de-toc-label {
  margin-left: 0px;
}
.e-bigger .e-de-toc-label-rtl {
  margin-left: 0px;
  margin-right: 0px;
}
.e-bigger .e-de-outline-rtl {
  width: 173px;
}
.e-bigger .e-de-rp-header {
  font-size: 16px;
  width: 75%;
}

.e-de-restrict-format {
  margin-top: 16px;
}

.e-de-rp-format {
  font-size: 14px;
  margin-bottom: 12px;
  opacity: 0.65;
  font-weight: 400;
}

.e-de-rp-checkbox {
  font-size: 12px;
}

.e-de-rp-border {
  margin-bottom: 12px;
  margin-top: 12px;
}

.e-de-rp-header {
  font-size: 15px;
  font-weight: 400;
  opacity: 0.87;
  color: rgba(var(--color-sf-on-surface));
  width: 75%;
}

.e-de-rp-user .e-checkbox-wrapper {
  width: auto;
}

.e-de-rp-nav-btn,
.e-de-rp-btn-enforce {
  background: "";
  font-size: 13px;
  opacity: 0.87;
  border-radius: 2px;
  box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.15), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.e-bigger .e-de-rp-btn-enforce {
  padding-left: 10px;
  padding-right: 10px;
}

.e-de-rp-nav-btn {
  width: auto;
}

.e-de-rp-btn-stop-enforce {
  background: "";
  font-size: 13px;
  opacity: 0.87;
  border-radius: 2px;
  box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.15), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.e-de-rp-sub-div {
  border-bottom: 1px solid rgba(var(--color-sf-outline-variant));
  padding: 12px;
}

.e-de-restrict-pane {
  padding-left: 0;
  padding-top: 0;
  padding-right: 0;
}

.e-de-rp-whole-header {
  padding: 12px;
}

.e-de-rp-user {
  background: rgba(var(--color-sf-surface));
  border: 1px solid rgba(var(--color-sf-outline-variant));
  border-radius: 2px;
  font-size: 12px;
  height: 110px;
}

.e-de-rp-enforce {
  padding: 12px;
}

.e-de-rp-enforce-nav {
  margin: 0 12px 12px 12px;
}

.e-de-enforce-dlg-title {
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 3px;
}

.e-de-enforce .e-de-enforce-dlg-input {
  height: 32px;
  width: 300px;
}

.e-de-user-add-btn {
  background: rgba(var(--color-sf-outline-variant));
  border-radius: 2px;
  width: 74px;
}

.e-de-user-dlg .e-de-user-dlg-textbox-input {
  margin-right: 24px;
  width: 304px;
}

.e-de-user-dlg-list.e-de-rtl .e-de-user-dlg-textbox-input {
  margin-right: 0;
  margin-left: 24px;
}

.e-de-user-dlg-list {
  margin-bottom: 12px;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.e-de-user-listview {
  border: 1px solid rgba(var(--color-sf-outline-variant));
  border-radius: 2px;
  height: 106px;
}

.e-de-user-dlg-user {
  margin-bottom: 8px;
}

.e-user-delete {
  float: left;
}

.e-de-unprotect-dlg-title {
  font-size: 12px;
  margin-bottom: 4px;
}

.e-de-rp-stop-div1 {
  opacity: 0.87;
  padding: 12px 12px 6px 12px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
}

.e-de-rp-stop-div2 {
  padding: 12px 12px 24px;
}

.e-de-rp-close-icon {
  float: right;
  position: relative;
  top: -5px;
  right: -12px;
}

.e-de-restrict-pane {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
  width: 280px;
}

.e-de-rp-nav-lbl {
  font-size: 13px;
  margin: 0 12px;
}

.e-documenteditor-optionspane {
  height: 100%;
}

.e-de-cmt-pane {
  background-color: inherit;
  color: rgba(var(--color-sf-on-surface));
}

.e-de-cp-whole-header {
  padding: 12px;
}

.e-de-cp-header {
  font-size: 13px;
  font-weight: 500;
  opacity: 0.87;
  width: 75%;
}

.e-de-cmt-container {
  height: auto;
  padding: 0 8px;
  overflow: auto;
}

.e-de-cmt-sub-container {
  background-color: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.05), rgba(var(--color-sf-primary), 0.05)), rgba(var(--color-sf-surface));
  margin: 0;
  padding: 8px;
}

.e-de-cmt-view {
  cursor: default;
}

.e-de-cmt-sub-container.e-de-cmt-selection {
  border-left: 3px solid rgba(var(--color-sf-primary));
  padding-left: 10px;
}

.e-de-tc-outer {
  border-bottom: 1px solid #d8d8d8;
}

.e-de-tc-pane {
  color: rgba(var(--color-sf-on-surface));
}

.e-de-trckchanges-inner {
  cursor: default;
  margin: 0;
  padding-left: 8px;
  padding-right: 16px;
  padding-bottom: 8px;
}

.e-de-trckchanges-inner:hover {
  border-left: 2px solid rgba(var(--color-sf-primary));
  cursor: default;
  margin: 0;
  padding-left: 6px;
  padding-right: 16px;
  padding-bottom: 8px;
}

.e-de-trckchanges-inner.e-de-trckchanges-inner-select {
  border-left: 3px solid rgba(var(--color-sf-primary));
  cursor: default;
  margin: 0;
  padding-left: 5px;
  padding-right: 16px;
  padding-bottom: 8px;
}

.e-de-tc-no-chng {
  color: rgba(var(--color-sf-on-surface));
  font-size: 14px;
  opacity: 65%;
  margin: 60px 0;
  text-align: center;
}

.e-tc-btn-bg-clr.e-btn:not(:hover):not(.e-active) {
  background-color: inherit;
  border-color: transparent;
  color: inherit;
}

.e-tc-nvgte.e-icon-btn {
  text-align: center;
  vertical-align: middle;
}

.e-de-track-span-user {
  display: -ms-inline-flexbox;
  display: inline-flex;
  overflow: hidden;
  width: 110px;
}

.e-de-track-span-view {
  display: -ms-inline-flexbox;
  display: inline-flex;
  overflow: hidden;
  width: 50px;
}

.e-btn.e-outline.e-de-track-accept-button {
  min-width: 66px;
  padding: 5px 4px;
  text-transform: initial;
}

.e-btn.e-outline.e-de-track-accept-button:hover {
  min-width: 66px;
  padding: 5px 4px;
  text-transform: initial;
}

.e-btn.e-outline.e-de-track-reject-button {
  min-width: 64px;
  padding: 5px 6px;
  margin-left: 8px;
  text-transform: initial;
}

.e-btn.e-outline.e-de-track-reject-button:hover {
  min-width: 64px;
  padding: 5px 6px;
  margin-left: 8px;
  text-transform: initial;
}

.e-de-track-chngs-count {
  font-size: 12px;
  margin-left: 6px;
  opacity: 0.87;
  padding-top: 7px;
  white-space: nowrap;
}

.e-de-track-insert {
  color: rgba(var(--color-sf-success));
  font-size: 13px;
  opacity: 100%;
}

.e-de-track-delete {
  color: rgba(var(--color-sf-error));
  font-size: 13px;
  opacity: 100%;
}

.e-de-track-toolbar {
  border-bottom: 1px solid #d8d8d8;
  border-top: 1px solid #d8d8d8;
  padding-bottom: 3px;
}

.e-de-track-pane-drop-btn {
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 10px !important;
  font-size: 13px;
  font-weight: 400;
}

.e-toolbar-item.e-de-track-toolbar-overlay.e-template.e-overlay {
  opacity: 1;
  font-weight: 400;
  font-size: 14px;
}

.e-de-track-date {
  font-size: 12px;
  margin-top: 4px;
  opacity: 0.67;
}

.e-de-track-usernme-div {
  -ms-flex-pack: justify;
      justify-content: space-between;
  padding-top: 8px;
}

.e-de-track-user-nme {
  font-size: 14px;
  font-weight: 500;
  padding-left: 8px;
}

.e-de-track-chngs-text {
  line-height: 19.5px;
  overflow: hidden;
  word-wrap: break-word;
  text-overflow: ellipsis;
  font-size: 14px;
  min-height: 20px;
  padding-top: 8px;
  margin-bottom: 16px;
}

.e-de-track-chng-table {
  border-collapse: collapse;
  border-spacing: 0px;
  opacity: 70%;
  width: 100%;
}

.e-de-tc-tble-cell {
  border: 1px solid;
  height: 20px;
}

.e-de-tc-shrink-img {
  height: 30px;
  margin: 0 4px;
  vertical-align: middle;
  width: 50px;
}

.e-de-tc-field {
  background-color: rgb(206, 205, 205);
  margin: 0 2px;
}

.e-de-tc-pmark {
  font-size: 14px;
  font-family: Roboto-Regular;
  font-weight: 600;
  color: rgb(1, 22, 119);
  margin: 0 2px;
}

.e-de-cmt-sub-container:not(.e-de-cmt-selection):not(.e-de-cmt-reply):hover {
  border-left: 2px solid rgba(var(--color-sf-primary));
  padding-left: 11px;
}

.e-de-cmt-author {
  -ms-flex-align: center;
      align-items: center;
  margin-bottom: 8px;
}

.e-de-cmt-author-name {
  font-size: 14px;
  font-weight: 400;
  padding-left: 8px;
  width: 90%;
}

.e-de-cp-option.e-btn.e-icon-btn {
  background-color: inherit;
  border: none;
  color: inherit;
  float: right;
  height: 18px;
  padding: 0px;
  width: 18px;
}

.e-de-cp-option.e-btn.e-icon-btn span {
  margin-top: 0px;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.05), rgba(var(--color-sf-primary), 0.05)), rgba(var(--color-sf-surface));
}

.e-de-cmt-view:hover .e-de-cp-option {
  display: block;
}

.e-de-cmt-readonly {
  font-size: 14px;
  padding-top: 4px;
  word-break: break-word;
}

.e-de-cmt-date {
  font-size: 12px;
  margin-top: 8px;
  opacity: 0.67;
}

.e-de-cmt-sub-container.e-de-cmt-reply {
  border: none;
  border-top: 1px solid rgba(var(--color-sf-outline-variant));
  border-radius: 0px;
  margin: 12px 8px 0 8px;
  padding: 0px;
}
.e-de-cmt-sub-container.e-de-cmt-reply .e-de-cmt-view {
  margin-top: 12px;
}

.e-de-cmt-textarea.e-input {
  color: rgba(var(--color-sf-on-surface));
  font-size: 13px;
  line-height: normal;
  min-height: 30px;
  overflow: hidden;
  padding-top: 7px;
  resize: none;
}

.e-bigger .e-de-cmt-textarea.e-input {
  padding-top: 4px;
}

.e-de-cmt-action-button {
  text-align: right;
  margin-top: 8px;
}

.e-de-cmt-post-btn.e-btn.e-flat,
.e-de-cmt-cancel-btn.e-btn.e-flat {
  height: 32px;
  margin-left: 8px;
  width: 38px;
}

.e-de-cmt-resolved .e-de-cmt-author-name,
.e-de-cmt-resolved .e-de-cmt-readonly,
.e-de-cmt-resolved e-de-cmt-date {
  opacity: 0.67;
}
.e-de-cmt-resolved .e-de-cmt-rply-view {
  display: none;
}
.e-de-cmt-resolved .e-de-cmt-resolve-btn {
  display: block;
  margin-top: 8px;
  text-align: right;
}

.e-de-cmt-sub-container.e-de-cmt-resolved.e-de-cmt-selection {
  border-left: 3px solid rgba(var(--color-sf-primary)), 0.5;
}

.e-de-cmt-sub-container.e-de-cmt-resolved:not(.e-de-cmt-selection):not(.e-de-cmt-reply):hover {
  border-left: 2px solid rgba(var(--color-sf-primary)), 0.5;
}

.e-de-lock-mark {
  cursor: default;
  color: rgba(var(--color-sf-on-surface-variant));
  font-size: 13px;
  height: 13px;
  pointer-events: all;
  width: 13px;
}

.e-de-cmt-mark {
  cursor: default;
  color: rgba(var(--color-sf-on-surface-variant));
  font-size: 13px;
  height: 13px;
  pointer-events: all;
  width: 13px;
}
.e-de-cmt-mark :hover {
  color: rgba(var(--color-sf-primary));
}

.e-de-cmt-mark.e-de-cmt-mark-selected,
.e-de-cmt-mark.e-de-cmt-mark-hover {
  color: rgba(var(--color-sf-primary));
}

.e-de-cmt-no-cmt {
  margin-left: 6px;
  margin-top: 24px;
  font-size: 13px;
}

.e-de-cmt-drawer-cnt {
  font-size: 13px;
  height: 20px;
  margin-top: 8px;
}

.e-de-cmt-drawer {
  cursor: pointer;
  float: right;
  opacity: 0.54;
  margin-right: 4px;
  margin-top: 4px;
}

.e-de-cmt-rply-view {
  margin-top: 12px;
}

.e-de-cmt-resolve-btn {
  display: none;
}

.e-rtl .e-de-cmt-pane {
  border-left: none;
  border-right: 1px solid rgba(var(--color-sf-outline-variant));
}
.e-rtl .e-de-rp-close-icon {
  float: left;
  right: 12px;
}
.e-rtl .e-de-cp-option.e-btn.e-icon-btn {
  float: left;
}
.e-rtl .e-de-cmt-sub-container.e-de-cmt-selection {
  border-left: 1px solid rgba(var(--color-sf-outline-variant));
  border-right: 3px solid rgba(var(--color-sf-primary));
  padding-left: 8px;
  padding-right: 10px;
}
.e-rtl .e-de-cmt-sub-container:not(.e-de-cmt-selection):not(.e-de-cmt-reply):hover {
  border-left: 1px solid rgba(var(--color-sf-outline-variant));
  border-right: 2px solid rgba(var(--color-sf-primary));
  padding-left: 8px;
  padding-right: 11px;
}
.e-rtl .e-de-cmt-sub-container.e-de-cmt-resolved.e-de-cmt-selection {
  border-right: 3px solid rgba(var(--color-sf-primary)), 0.5;
}
.e-rtl .e-de-cmt-sub-container.e-de-cmt-resolved:not(.e-de-cmt-selection):not(.e-de-cmt-reply):hover {
  border-right: 2px solid rgba(var(--color-sf-primary)), 0.5;
}
.e-rtl .e-de-cmt-action-button {
  text-align: left;
}
.e-rtl .e-de-cmt-no-cmt {
  margin-left: auto;
  margin-right: 6px;
}
.e-rtl .e-de-cmt-drawer {
  float: left;
  margin-left: 8px;
  margin-right: auto;
}
.e-rtl .e-de-cmt-resolve-btn {
  text-align: left;
}

.e-bigger .e-de-cmt-post-btn.e-btn.e-flat,
.e-bigger .e-de-cmt-cancel-btn.e-btn.e-flat {
  height: auto;
  margin-left: 12px;
  width: 50px;
}
.e-bigger .e-de-cp-whole-header {
  height: 56px;
  padding: 16px;
  padding-right: 8px;
}
.e-bigger .e-de-rp-close-icon {
  float: right;
  position: relative;
  top: -9px;
  right: -17px;
}
.e-bigger .e-rtl .e-de-cmt-post-btn.e-btn.e-flat,
.e-bigger .e-rtl .e-de-cmt-cancel-btn.e-btn.e-flat {
  margin-right: 12px;
}

.e-de-close-icon.e-btn {
  background-color: transparent !important;
  border-color: transparent !important;
  border-radius: 50%;
  color: rgba(var(--color-sf-on-surface-variant));
  border: none;
}
.e-de-close-icon.e-btn:active {
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: none;
}
.e-de-close-icon.e-btn:hover {
  background-color: transparent !important;
  box-shadow: none;
  color: rgba(var(--color-sf-on-surface));
  cursor: default;
  border: none;
}

.e-btn.e-de-close-icon:hover span {
  cursor: pointer;
  color: rgba(var(--color-sf-on-surface));
}

.e-de-para-dlg-right-sub-container-blazor {
  top: 0;
}

.e-de-rp-mu-btn {
  margin-top: 3px;
}

.e-de-table-dialog-row-height {
  padding-top: 24px;
}

.e-de-tablecell-dialog-alignment-icon {
  width: 54px;
  height: 54px;
  margin: 2px;
}

.e-de-table-dia-indent-from-left {
  right: 45px;
}

.e-de-table-dia-align-div {
  border: 1px solid rgba(var(--color-sf-outline));
  display: inline-block;
  height: 60px;
  margin-right: 11px;
  width: 60px;
}

.e-de-table-dialog-alignment-icon {
  margin: 2px;
  height: 52px;
}

.e-de-table-border-setting-genral {
  margin-right: 11px;
}

.e-de-table-border-clr-left-container {
  padding-right: 19px;
}

.e-de-table-border-clr-heading {
  font-size: 12px;
  font-weight: 500;
  padding-bottom: 6px;
}

.e-de-table-border-icon-container {
  margin-top: 4px;
  margin-right: 4px;
}

.e-de-table-border-preview-container {
  padding-left: 50px;
}

.e-de-table-dlg-alignment-heading {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 3px;
}

.e-rtl .e-de-cmt-author-name {
  padding-left: 0%;
  padding-right: 8px;
}
.e-rtl .e-de-ok-button {
  margin-right: 0;
  margin-left: 8px;
}
.e-rtl .e-de-table-dia-align-div.e-de-rtl {
  margin-left: 11px;
  margin-right: 0;
}
.e-rtl .e-de-table-border-clr-left-container {
  padding-right: 0;
  padding-left: 19px;
}
.e-rtl .e-de-table-border-preview-container {
  padding-right: 50px;
  padding-left: 0;
}
.e-rtl .e-de-table-border-setting-genral {
  margin-right: 0;
  margin-left: 11px;
}
.e-rtl .e-de-table-border-icon-container {
  margin-right: 0;
  margin-left: 4px;
}
.e-rtl .e-de-table-border-preview {
  width: 23px;
}
.e-rtl .e-de-table-setting-labels-heading {
  margin-left: 0;
  margin-right: 10px;
}

.e-bigger .e-de-cmt-author-name {
  padding-left: 12px;
}
.e-bigger .e-de-cmt-rply-view {
  margin-top: 16px;
}
.e-bigger .e-de-cmt-sub-container {
  padding: 12px;
}
.e-bigger .e-de-cmt-sub-container.e-de-cmt-selection {
  padding-left: 14px;
}
.e-bigger .e-de-cmt-sub-container:not(.e-de-cmt-selection):not(.e-de-cmt-reply):hover {
  padding-left: 15px;
}
.e-bigger .e-de-cmt-sub-container.e-de-cmt-reply {
  padding: 0%;
}
.e-bigger .e-de-rp-nav-btn {
  width: auto;
}
.e-bigger .e-de-rp-whole-header {
  padding: 12px;
}
.e-bigger .e-de-rp-sub-div {
  border-bottom: 1px solid rgba(var(--color-sf-outline-variant));
  padding: 12px;
}
.e-bigger .e-de-rp-enforce {
  padding: 12px;
}
.e-bigger .e-de-enforce .e-de-enforce-dlg-input:not(.e-de-enforce .e-de-enforce-dlg-input:last-child) {
  width: 300px;
}
.e-bigger .e-rtl .e-de-cmt-author-name {
  padding-left: 0%;
  padding-right: 12px;
}
.e-bigger .e-rtl .e-de-cmt-sub-container.e-de-cmt-selection {
  padding-left: 12px;
  padding-right: 14px;
}
.e-bigger .e-rtl .e-de-cmt-sub-container:not(.e-de-cmt-selection):not(.e-de-cmt-reply):hover {
  padding-left: 12px;
  padding-right: 15px;
}
.e-bigger .e-rtl .e-de-rp-close-icon {
  float: left;
  right: 17px;
}

.e-bigger {
  /* stylelint-disable */
  /* stylelint-enable */
}
.e-bigger .e-de-para-dlg-container .e-checkbox-wrapper .e-label,
.e-bigger .e-de-table-options-dlg .e-checkbox-wrapper .e-label {
  font-size: 14px;
}
.e-bigger .e-de-para-dlg-container .e-input-group {
  width: 192px !important;
}
.e-bigger .e-de-para-dlg-heading {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}
.e-bigger .e-de-para-dlg-container .e-checkbox-wrapper .e-label,
.e-bigger .e-de-table-options-dlg .e-checkbox-wrapper .e-label {
  font-size: 12px;
}
.e-bigger .e-de-para-dlg-container {
  height: auto;
  width: auto;
}
.e-bigger .e-de-para-dlg-cs-check-box {
  margin-bottom: 0;
  margin-top: 12px;
}
.e-bigger .e-de-para-dlg-spacing-div {
  margin-left: 20px;
}
.e-bigger .e-de-para-dlg-spacing-div.e-de-rtl {
  margin-left: 0;
  margin-right: 20px;
}
.e-bigger .e-de-para-dlg-sub-container .e-input-group {
  margin-bottom: 4px;
}
.e-bigger .e-de-para-dlg-sub-container {
  margin-bottom: 20px;
}
.e-bigger .e-de-para-dlg-right-sub-container {
  top: 0;
}
.e-bigger .e-de-dlg-sub-header {
  display: block;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 10px;
}
.e-bigger .e-de-rtl-btn-div {
  font-size: 12px;
  margin-right: 24px;
  width: 100px;
}
.e-bigger .e-de-rtl-btn-div.e-de-rtl {
  margin-left: 20px;
  margin-right: 0;
}

.e-de-para-dlg-heading {
  color: rgba(var(--color-sf-on-surface-variant));
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  opacity: 87%;
}

.e-de-para-dlg-container .e-checkbox-wrapper .e-label,
.e-de-table-options-dlg .e-checkbox-wrapper .e-label {
  font-size: 12px;
}

.e-de-para-dlg-container {
  padding: 8px;
  height: auto;
  width: auto;
}

.e-de-para-dlg-cs-check-box {
  margin-bottom: 0;
  margin-top: 12px;
}

.e-de-para-dlg-spacing-div {
  margin-left: 40px;
}

.e-de-para-dlg-spacing-div.e-de-rtl {
  margin-left: 0;
  margin-right: 40px;
}

.e-de-para-dlg-sub-container .e-input-group {
  margin-bottom: 4px;
}

.e-de-para-dlg-sub-container {
  margin-bottom: 20px;
}

.e-de-para-dlg-right-sub-container {
  top: 0;
}

.e-de-dlg-sub-header {
  display: block;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 10px;
}

.e-de-rtl-btn-div {
  font-size: 12px;
  margin-right: 14px;
  width: 200px;
}

.e-de-rtl-btn-div.e-de-rtl {
  margin-left: 14px;
  margin-right: 0;
}

.e-para-dlg-sub-height {
  height: 145px;
}

.e-de-ctnr-close::before {
  color: rgba(var(--color-sf-on-surface-variant));
  content: "\e7e7";
  font-size: 16px;
}

.e-de-ctnr-linespacing::before {
  content: "\e78d";
}

.e-de-ctnr-undo::before {
  content: "\e713";
}

.e-de-ctnr-find::before {
  content: "\e754";
}

.e-de-ctnr-lock::before {
  content: "\e7ff";
}

.e-de-ctnr-italic::before {
  content: "\e75a";
}

.e-de-selected-spellcheck-item::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e774";
  font-family: "e-icons";
  font-size: 10px;
}

.e-de-selected-underline-item::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e774";
  font-family: "e-icons";
  font-size: 10px;
}

.e-de-ctnr-link::before {
  content: "\e757";
}

.e-de-ctnr-table::before {
  content: "\e7d1";
}

.e-de-ctnr-download::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7a1";
}

.e-de-ctnr-justify::before {
  content: "\e721";
}

.e-de-ctnr-tableofcontent::before {
  content: "\e73d";
}

.e-de-ctnr-pagenumber::before {
  content: "\e77d";
}

.e-de-ctnr-highlight::before {
  content: "\e739";
}

.e-de-ctnr-new::before {
  content: "\e805";
}

.e-de-ctnr-paste::before {
  content: "\e70b";
}

.e-de-ctnr-bold::before {
  content: "\e737";
}

.e-de-ctnr-subscript::before {
  content: "\e80a";
}

.e-de-ctnr-aligncenter::before {
  content: "\e813";
}

.e-de-ctnr-fontcolor::before {
  content: "\e79f";
}

.e-de-ctnr-change-case::before {
  content: "\e7f5";
}

.e-de-ctnr-pagesetup::before {
  content: "\e794";
}

.e-de-ctnr-strokestyle::before {
  content: "\eb62";
}

.e-de-ctnr-strikethrough::before {
  content: "\e758";
}

.e-de-ctnr-image::before {
  content: "\e786";
}

.e-de-ctnr-redo::before {
  content: "\e755";
}

.e-de-ctnr-bookmark::before {
  content: "\e750";
}

.e-de-ctnr-increaseindent::before {
  content: "\e810";
}

.e-de-ctnr-header::before {
  content: "\e704";
}

.e-de-ctnr-backgroundcolor::before {
  content: "\eb6b";
}

.e-de-ctnr-open::before {
  content: "\e760";
}

.e-de-ctnr-underline::before {
  content: "\e82f";
}

.e-de-ctnr-superscript::before {
  content: "\e7a7";
}

.e-de-ctnr-alignleft::before {
  content: "\e7b8";
}

.e-de-ctnr-numbering::before {
  content: "\e7cb";
}

.e-de-ctnr-bullets::before {
  content: "\e77e";
}

.e-de-ctnr-borders::before {
  content: "\e893";
}

.e-de-ctnr-decreaseindent::before {
  content: "\e72a";
}

.e-de-ctnr-showhide::before {
  content: "\e71a";
}

.e-de-ctnr-print::before {
  content: "\e75d";
}

.e-de-ctnr-alignright::before {
  content: "\e719";
}

.e-de-ctnr-footer::before {
  content: "\e7bb";
}

.e-de-ctnr-clearall::before {
  content: "\e7cc";
}

.e-de-ctnr-outsideborder::before {
  content: "\e7ad";
}

.e-de-ctnr-allborders::before {
  content: "\e7d1";
}

.e-de-ctnr-insideborders::before {
  content: "\e78f";
}

.e-de-ctnr-leftborders::before {
  content: "\e806";
}

.e-de-ctnr-insideverticalborder::before {
  content: "\e792";
}

.e-de-ctnr-rightborder::before {
  content: "\e7ab";
}

.e-de-ctnr-topborder::before {
  content: "\e7e0";
}

.e-de-ctnr-insidehorizondalborder::before {
  content: "\e83b";
}

.e-de-ctnr-bottomborder::before {
  content: "\e766";
}

.e-de-ctnr-strokesize::before {
  content: "\e7bf";
}

.e-de-ctnr-highlightcolor::before {
  content: "\e739";
}

.e-de-ctnr-mergecell::before {
  content: "\e71e";
}

.e-de-ctnr-insertleft::before {
  content: "\e78b";
}

.e-de-ctnr-insertright::before {
  content: "\e70e";
}

.e-de-ctnr-insertabove::before {
  content: "\e836";
}

.e-de-ctnr-insertbelow::before {
  content: "\e801";
}

.e-de-ctnr-deleterows::before {
  content: "\e7f2";
}

.e-de-ctnr-deletecolumns::before {
  content: "\e714";
}

.e-de-ctnr-aligntop::before {
  content: "\e707";
}

.e-de-ctnr-alignbottom::before {
  content: "\e7a0";
}

.e-de-ctnr-aligncenter-table::before {
  content: "\e74f";
}

.e-de-ctnr-cellbg-clr-picker::before {
  content: "\e783";
}

.e-de-ctnr-bullet-none::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7f3";
  font-size: 20px;
  line-height: 28px;
}

.e-de-ctnr-bullet-dot::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e747";
  font-size: 8px;
  line-height: 28px;
}

.e-de-ctnr-bullet-circle::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7d0";
  font-size: 8px;
  line-height: 28px;
}

.e-de-ctnr-bullet-square::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7be";
  font-size: 8px;
  line-height: 28px;
}

.e-de-ctnr-bullet-flower::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e79b";
  line-height: 28px;
}

.e-de-ctnr-bullet-arrow::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e763";
  line-height: 28px;
}

.e-de-ctnr-bullet-tick::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7fc";
  line-height: 28px;
}

.e-de-selected-item::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e774";
}

.e-de-ctnr-break::before {
  content: "\e7bc";
}

.e-de-ctnr-page-break::before {
  content: "\e742";
}

.e-de-ctnr-section-break::before {
  content: "\e762";
}

.e-de-ctnr-upload::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e712";
}

.e-de-flip {
  transform: scaleX(-1);
}

.e-de-cnt-cmt-add::before {
  content: "\e82c";
}

.e-de-cnt-track::before {
  content: "\e80b";
}

.e-de-printlayout::before {
  content: "\e73a";
}

.e-de-weblayout::before {
  content: "\e7d3";
}

.e-de-textform::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e830";
  font-family: "e-icons";
}

.e-de-formproperties::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e83e";
  font-family: "e-icons";
}

.e-de-clearform::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7f8";
  font-family: "e-icons";
}

.e-de-dropdownform::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7a6";
  font-family: "e-icons";
}

.e-de-formfield::before {
  content: "\e7cd";
  font-family: "e-icons";
}

.e-de-checkbox-form::before {
  color: var(--color-sf-on-surface-variant);
  content: "\e7e4";
  font-family: "e-icons";
}

.e-de-arrow-up::before {
  content: "\e776";
  font-family: "e-icons";
}

.e-de-arrow-down::before {
  content: "\e729";
  font-family: "e-icons";
}

.e-de-update-field::before {
  content: "\e828";
  font-family: "e-icons";
}

.e-de-footnote::before {
  content: "\e7af";
  font-family: "e-icons";
}

.e-de-endnote::before {
  content: "\e7af";
  font-family: "e-icons";
}

.e-de-e-paragraph-mark::before {
  content: "\e844";
  font-family: "e-icons";
}

.e-de-e-paragraph-style-mark::before {
  content: "\e844";
  font-family: "e-icons";
}

.e-de-e-character-style-mark::before {
  content: "\e8bf";
  font-family: "e-icons";
}

.e-de-e-linked-style-mark::before {
  content: "\e8c0";
  font-family: "e-icons";
}

.e-de-ctnr-columns::before {
  content: "\e8b4";
  font-family: "e-icons";
}

.e-de-ctnr-page-size::before {
  content: "\e89a";
  font-family: "e-icons";
}

.e-de-ctnr-page-break-column::before {
  content: "\e8b4";
  font-family: "e-icons";
}

.e-de-ctnr-page-break-text-wrapping::before {
  content: "\e972";
  font-family: "e-icons";
}

.e-de-ctnr-section-break-continuous::before {
  content: "\e8b5";
  font-family: "e-icons";
}

.e-de-ctnr-section-break-even-page::before {
  content: "\e8b2";
  font-family: "e-icons";
}

.e-de-ctnr-section-break-odd-page::before {
  content: "\e8b3";
  font-family: "e-icons";
}

.e-de-ctnr-columns-one::before {
  content: "\e8b9";
  font-family: "e-icons";
  font-size: 60px;
}

.e-de-ctnr-columns-two::before {
  content: "\e8ba";
  font-family: "e-icons";
  font-size: 60px;
}

.e-de-ctnr-columns-three::before {
  content: "\e8bb";
  font-family: "e-icons";
  font-size: 60px;
}

.e-de-ctnr-columns-right::before {
  content: "\e8b7";
  font-family: "e-icons";
  font-size: 60px;
}

.e-de-ctnr-columns-left::before {
  content: "\e8b8";
  font-family: "e-icons";
  font-size: 60px;
}

.e-de-toolbar {
  height: 100%;
}

.e-documenteditorcontainer {
  display: block;
}

.e-de-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text .e-de-text-wrap {
  height: 0;
  line-height: 0;
}

.e-de-ctnr-file-picker {
  left: -110em;
  position: fixed;
}

.e-de-ctnr-rtl {
  direction: rtl;
}

.e-de-ctnr-hglt-btn {
  border: 0.5px solid transparent;
  display: inline-block;
  height: 25px;
  margin: 3px;
  width: 25px;
}

.e-color-selected,
.e-de-ctnr-hglt-btn:hover {
  border-color: rgba(var(--color-sf-white));
  outline: rgba(var(--color-sf-black)) 0.5px solid;
}

.e-hglt-no-color {
  height: 30px;
  padding-top: 1px;
  width: 157px;
}
.e-hglt-no-color:hover {
  background-color: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  cursor: pointer;
}

.e-de-ctnr-hglt-no-color {
  font-size: 12px;
  font-weight: normal;
  left: 40px;
  padding-top: 11px;
  position: absolute;
  top: 100px;
}

/* stylelint-disable */
.e-de-scrollbar-hide::-webkit-scrollbar {
  width: 0;
}

.e-de-scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* stylelint-enable */
/* stylelint-disable */
.e-de-toolbar {
  height: 100%;
}

.e-de-prop-pane .e-de-ctnr-group-btn.e-btn-group:not(.e-outline) {
  box-shadow: none;
  height: 32px;
}
.e-de-prop-pane .e-de-ctnr-group-btn.e-btn-group:not(.e-outline):focus {
  box-shadow: none;
}
.e-de-prop-pane .e-de-ctnr-group-btn.e-btn-group:not(.e-outline):active {
  box-shadow: none;
}
.e-de-prop-pane .e-de-ctnr-group-btn.e-btn-group:not(.e-outline):hover {
  box-shadow: none;
}

.e-de-ctnr-group-btn-middle button {
  border-radius: 0px;
}

.e-de-review-pane button.e-de-close-icon {
  background: transparent;
  box-shadow: none;
}

.e-de-op-more-less > div:last-child label {
  left: 35px;
}

.e-bigger .e-de-ctn .e-de-bzr-button {
  line-height: 17px;
  padding-top: 12px;
}

.e-de-ctn .e-de-bzr-button {
  box-shadow: none !important;
}

.e-bigger .e-de-ctn .e-de-bzr-button {
  box-shadow: none !important;
}

.e-de-char-fmt-btn-left button:not(:first-child) {
  border: 1px solid transparent;
  border-left: 1px solid rgba(var(--color-sf-outline-variant));
}

.e-de-ctnr-group-btn .e-de-prop-font-button {
  position: relative;
  border: 1px solid transparent rgba(var(--color-sf-outline-variant));
  border-right-width: 1px;
}
.e-de-ctnr-group-btn .e-de-prop-font-last-button {
  position: relative;
  border: 1px solid transparent rgba(var(--color-sf-outline-variant));
  border-left-width: 1px;
}

.e-de-ctnr-group-btn .e-de-prop-indent-button {
  position: relative;
  border: 1px solid transparent rgba(var(--color-sf-outline-variant));
  border-right-width: 1px;
}
.e-de-ctnr-group-btn .e-de-prop-indent-last-button {
  position: relative;
  border: 1px solid transparent rgba(var(--color-sf-outline-variant));
  border-left-width: 1px;
}

.e-de-grp-btn-ctnr .e-de-ctnr-group-btn-middle {
  margin-bottom: -1px;
}
.e-de-grp-btn-ctnr .e-de-ctnr-group-btn-middle > * {
  border-radius: 0px;
}

.e-bigger .e-de-grp-btn-ctnr .e-de-ctnr-group-btn-middle > * {
  border-radius: 0px;
}
.e-bigger .e-de-grp-btn-ctnr .e-de-ctnr-group-btn-top > * {
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}
.e-bigger .e-de-grp-btn-ctnr .e-de-ctnr-group-btn-bottom > * {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.de-tbl-fill-clr .e-dropdown-btn.e-btn {
  box-shadow: none;
}

.e-de-prop-pane .e-de-ctnr-group-btn button,
.e-de-ctn .e-de-status-bar button {
  box-shadow: none;
  height: 32px;
}
.e-de-prop-pane .e-de-ctnr-group-btn button:focus,
.e-de-ctn .e-de-status-bar button:focus {
  box-shadow: none;
}
.e-de-prop-pane .e-de-ctnr-group-btn button:active,
.e-de-ctn .e-de-status-bar button:active {
  box-shadow: none;
}
.e-de-prop-pane .e-de-ctnr-group-btn button:hover,
.e-de-ctn .e-de-status-bar button:hover {
  box-shadow: none;
}

.e-de-statusbar-pageweb {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border: 0;
  box-shadow: none;
  float: right;
  height: 33px;
  width: 33px;
}
.e-de-statusbar-pageweb:hover {
  box-shadow: none;
}

.e-split-btn-wrapper.e-de-prop-splitbutton,
.e-split-btn-wrapper.e-de-btn-hghlclr,
.e-btn.de-split-button {
  box-shadow: none;
}

.e-de-statusbar-pageweb .e-de-printlayout,
.e-de-statusbar-pageweb .e-de-weblayout {
  font-size: 16px;
}

.e-de-statusbar-pageweb .e-de-printlayout:hover,
.e-de-statusbar-pageweb .e-de-weblayout:hover {
  font-size: 16px;
}

.e-bigger .e-btn.e-de-statusbar-pageweb {
  padding: 0;
}
.e-bigger .e-de-statusbar-pageweb .e-de-printlayout,
.e-bigger .e-de-statusbar-pageweb .e-de-weblayout {
  font-size: 18px;
}

.e-listview .e-list-icon {
  height: 24px;
  width: 16px;
  margin-right: 12px;
}

.e-de-listview-icon {
  height: auto;
  width: auto;
  line-height: 22px;
  margin-right: 12px;
}

.e-de-linespacing {
  margin-top: 8px;
}

.e-de-statusbar-zoom {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border: 0;
  color: rgba(var(--color-sf-on-secondary-container));
  float: right;
  font-weight: 400;
  height: 33px;
}

.e-de-pagenumber-text {
  border: none !important;
}

.e-de-prop-pane .e-de-ctnr-group-btn.e-btn-group button {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
}

.e-de-font-clr-picker button, .e-de-prop-font-colorpicker button {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface)) !important;
}

.e-de-style-font-color-picker .e-de-style-icon-button-size button:first-child {
  margin-right: 0px;
}

.e-de-ctnr-group-btn .e-btn-group button,
.e-documenteditorcontainer.e-lib .e-split-btn-wrapper button,
.e-documenteditorcontainer.e-lib .e-colorpicker-wrapper .e-split-btn-wrapper .e-split-colorpicker.e-split-btn,
.e-de-ctnr-segment button,
.e-de-char-fmt-btn-right button,
.e-de-border-size-button,
.e-de-cell-div button,
.e-de-insert-del-cell button,
.e-de-align-text button {
  border-radius: 4px;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
}

.e-de-char-fmt-btn-right button:not(:first-child),
.e-de-char-fmt-btn-left button:not(:first-child),
.e-de-align-text button:not(:first-child) {
  border-left: 1.7px solid rgba(var(--color-sf-outline-variant));
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
}

.e-de-insert-del-cell button:not(:first-child):not(.e-rtl) {
  border-left: 1.7px solid rgba(var(--color-sf-outline-variant));
}

.e-de-ctnr-segment-rtl .e-de-char-fmt-btn-left .e-de-prop-font-last-button,
.e-de-ctnr-segment-rtl .e-de-char-fmt-btn-left .e-de-prop-indent-last-button,
.e-de-ctnr-segment-rtl .e-de-char-fmt-btn-right .e-de-prop-font-last-button,
.e-de-ctnr-segment-rtl .e-de-char-fmt-btn-right .e-de-prop-indent-last-button {
  border-left: 0;
}

.e-rtl .e-de-char-fmt-btn-right button:not(:last-child),
.e-rtl .e-de-char-fmt-btn-left button:not(:last-child),
.e-rtl .e-de-insert-del-cell button:not(:last-child),
.e-rtl .e-de-align-text button:not(:last-child) {
  border-right: 1.7px solid rgba(var(--color-sf-outline-variant));
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
}

.e-de-char-fmt-btn-right.e-rtl button:not(:first-child),
.e-de-char-fmt-btn-left.e-rtl button:not(:first-child),
.e-de-insert-del-cell .e-rtl button:not(:first-child),
.e-de-align-text .e-rtl button:not(:first-child) {
  border-right: 1px solid rgba(var(--color-sf-outline-variant)) !important;
}

.e-de-grp-btn-ctnr .e-de-ctnr-group-btn .e-de-prop-font-button {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
  border: 1px solid rgba(var(--color-sf-outline-variant)) !important;
}
.e-de-grp-btn-ctnr .e-de-ctnr-group-btn-top {
  margin-bottom: -1px;
}

.e-de-font-clr-picker > *,
.de-split-button > div:first-child {
  margin-right: 8px;
}

.e-de-pagenumber-input {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.12), rgba(var(--color-sf-primary), 0.12)), rgba(var(--color-sf-surface));
  border: none !important;
  border-radius: 2px;
  color: rgba(var(--color-sf-on-secondary-container));
  padding: 0px;
  text-align: center;
  width: 22px;
}

.e-btn-pageweb-toggle {
  background-color: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
  box-shadow: none;
  outline: rgba(var(--color-sf-white)) 0 solid;
  outline-offset: 0;
}

.e-btn-pageweb-spellcheck {
  border: 0;
  box-shadow: none;
  float: right;
  margin-left: calc(100% - 395px);
}

.e-bigger .e-btn-pageweb-spellcheck {
  margin-left: calc(100% - 430px);
}

.e-de-ctn-title {
  background-color: rgba(var(--color-sf-primary));
  border-bottom: none !important;
  color: rgba(var(--color-sf-on-primary));
  font-size: 14px !important;
}
.e-de-ctn-title button {
  font-size: 14px !important;
  height: 36px !important;
  margin: 0 !important;
}
.e-de-ctn-title button .e-btn-icon {
  font-size: 16px !important;
}

.e-bigger .e-de-ctn-title {
  font-size: 16px !important;
  height: 40px !important;
}
.e-bigger .e-de-ctn-title button {
  font-size: 16px !important;
  height: auto !important;
  margin: 0 !important;
}
.e-bigger .e-de-ctn-title button .e-btn-icon {
  font-size: 18px !important;
}

.e-de-tool-ctnr-properties-pane {
  display: -ms-flexbox;
  display: flex;
  height: calc(100% - 117px);
  width: 100%;
}

.e-de-ctnr-properties-pane {
  display: -ms-flexbox;
  display: flex;
  height: calc(100% - 42px);
  width: 100%;
}

.e-de-statusbar-separator {
  border-left: 1px solid rgba(var(--color-sf-outline-variant));
  height: 16px;
  margin-left: 7.5px;
  margin-right: 7.5px;
  margin-top: 6px;
}

.e-bigger .e-de-statusbar-separator {
  border-left: 1px solid rgba(var(--color-sf-outline-variant));
  height: 20px;
  margin-left: 7.5px;
  margin-right: 7.5px;
  margin-top: 8px;
}

.e-de-statusbar-spellcheck {
  border-radius: 2px;
  font-weight: 400;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
}

.e-de-ctn {
  background-color: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border: 1px solid rgba(var(--color-sf-outline-variant));
  height: 100%;
  position: relative;
  width: 100%;
}

.e-bigger .e-de-statusbar-spellcheck {
  border-radius: 2px;
}

.e-de-ctnr-toolbar {
  display: -ms-flexbox;
  display: flex;
  height: 85px;
  width: 100%;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
}

.e-de-tlbr-wrapper .e-de-toolbar.e-toolbar,
.e-de-tlbr-wrapper .e-de-ctnr-properties-pane-btn {
  border: 0;
}

.e-de-pane {
  border-left: 1px solid rgba(var(--color-sf-outline-variant));
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.05), rgba(var(--color-sf-primary), 0.05)), rgba(var(--color-sf-surface));
}

.e-de-pane-rtl {
  border-right: 1px solid rgba(var(--color-sf-outline-variant));
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.05), rgba(var(--color-sf-primary), 0.05)), rgba(var(--color-sf-surface));
}

.e-de-tool-ctnr-properties-pane,
.e-de-ctnr-properties-pane {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.05), rgba(var(--color-sf-primary), 0.05)), rgba(var(--color-sf-surface));
  border-bottom: 1px solid rgba(var(--color-sf-outline-variant));
  border-top: 1px solid rgba(var(--color-sf-outline-variant));
}

.e-de-ctnr-segment {
  margin-bottom: 8px;
}

.e-de-ctnr-segment > div:first-child:not(.e-rtl),
.e-de-ctnr-segment-list > div:last-child:not(.e-rtl),
.e-de-ctnr-segment > button:first-child:not(.e-rtl) {
  margin-right: 12px;
}

.e-de-ctnr-segment.e-de-ctnr-segment-rtl > div:first-child,
.e-de-ctnr-segment-list.e-de-ctnr-segment-list-rtl > div:last-child,
.e-de-ctnr-segment.e-de-ctnr-segment-rtl > button:first-child {
  margin-left: 12px;
  margin-right: 0;
}

.e-de-tlbr-wrapper {
  background-color: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  height: 85px;
  width: calc(100% - 78px);
}

.e-de-ctnr-prop-label {
  color: rgba(var(--color-sf-on-secondary-container));
  display: inline-block;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
  margin-bottom: 8px;
  opacity: 0.87;
}

.e-de-table-prop-label {
  margin-left: 12px;
}

.e-de-table-prop-label.e-de-rtl {
  margin-left: 0;
  margin-right: 12px;
}

.e-de-toolbar.e-toolbar {
  border-radius: 0;
}

.e-de-ctnr-toolbar .e-toolbar-item.e-de-toolbar-btn-first {
  margin-left: 0;
  margin-right: 4px;
}

.e-de-ctnr-toolbar.e-de-ctnr-rtl .e-toolbar-item.e-de-toolbar-btn-first {
  margin-left: 4px;
  margin-right: 0;
}

.e-bigger .e-de-ctnr-toolbar .e-toolbar-item.e-de-toolbar-btn-first {
  margin-left: 0;
  margin-right: 8px;
}

.e-bigger .e-de-ctnr-toolbar.e-de-ctnr-rtl .e-toolbar-item.e-de-toolbar-btn-first {
  margin-left: 8px;
  margin-right: 0;
}

.e-de-ctnr-toolbar .e-toolbar-item.e-de-toolbar-btn-last {
  margin-left: 4px;
  margin-right: 0;
}

.e-de-ctnr-toolbar.e-de-ctnr-rtl .e-toolbar-item.e-de-toolbar-btn-last {
  margin-left: 0;
  margin-right: 4px;
}

.e-bigger .e-de-ctnr-toolbar .e-toolbar-item.e-de-toolbar-btn-last {
  margin-left: 8px;
  margin-right: 0;
}

.e-bigger .e-de-ctnr-toolbar.e-de-ctnr-rtl .e-toolbar-item.e-de-toolbar-btn-last {
  margin-left: 0;
  margin-right: 8px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items {
  height: 85px;
}
.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-separator {
  height: 59px;
  margin: 0 8px;
}
.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-start {
  margin-left: 8px !important;
  margin-right: 4px;
}
.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-middle {
  margin-left: 4px;
  margin-right: 4px;
}
.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-end {
  margin-left: 4px;
  margin-right: 8px;
}
.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-image-focus :focus {
  background-color: rgba(var(--color-sf-primary-container));
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn {
  -ms-flex-direction: column;
      flex-direction: column;
  height: calc(100% - 10px);
  padding: 0;
  padding-bottom: 10px;
}
.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus {
  padding: 0;
  padding-bottom: 10px;
}
.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active {
  padding: 0;
  padding-bottom: 10px;
}
.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover {
  padding: 0;
  padding-bottom: 10px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text {
  padding: 0;
}

.e-de-overlay {
  height: 100%;
  opacity: 0.5;
  pointer-events: none;
  -ms-touch-action: none;
      touch-action: none;
  width: 100%;
}

.e-de-ctnr-properties-pane-btn {
  width: 78px;
}

.e-de-pane-enable-clr.e-de-ctnr-properties-pane-btn .e-btn {
  color: rgba(var(--color-sf-primary));
}

.e-de-pane-disable-clr.e-de-ctnr-properties-pane-btn .e-btn {
  color: rgba(var(--color-sf-on-secondary-container));
}

.e-de-ctnr-properties-pane-btn .e-btn {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border-radius: 0;
  box-shadow: none;
  color: rgba(var(--color-sf-primary));
  min-height: 100%;
  min-width: 100%;
}
.e-de-ctnr-properties-pane-btn .e-btn:focus {
  box-shadow: none;
}
.e-de-ctnr-properties-pane-btn .e-btn:active {
  box-shadow: none;
}
.e-de-ctnr-properties-pane-btn .e-btn:hover {
  box-shadow: none;
}

.e-de-showhide-btn {
  border: 0;
  height: 85px;
}

.e-de-showhide-btn-rtl {
  border: 0;
  height: 85px;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item:not(.e-separator) {
  padding: 0;
}

.e-de-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text .e-de-text-wrap {
  line-height: 0.8;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
  display: table;
  font-size: 12px;
  margin: 0 6.5px;
  padding: 0;
  white-space: normal;
}

.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item button.e-btn.e-tbtn-txt .e-icons.e-btn-icon,
.e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 0 !important;
  min-height: 16px;
}

.e-de-hdr-ftr-frst-div {
  margin-bottom: 12px;
}

.e-de-hdr-ftr-top-div {
  margin-bottom: 12px;
}

.e-de-cntr-pane-padding {
  padding: 16px;
}

.e-de-prop-pane {
  height: 100%;
  min-height: 200px;
  overflow: auto;
  width: 256px;
}

.e-de-review-pane {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.05), rgba(var(--color-sf-primary), 0.05)), rgba(var(--color-sf-surface));
  border-left: 1px solid rgba(var(--color-sf-outline-variant));
  height: 100%;
  min-height: 200px;
  overflow: auto;
  width: 380px;
}

.e-bigger .e-de-cntr-pane-padding {
  padding: 16px;
}
.e-bigger .e-de-prop-pane {
  height: 100%;
  min-height: 200px;
  overflow: auto;
  width: 306px;
}

.e-de-property-div-padding {
  border-bottom: 1px solid rgba(var(--color-sf-outline-variant));
  padding: 12px;
}

.e-de-ctnr-dropdown-ftr {
  border-top: 1px solid rgba(var(--color-sf-outline-variant));
  color: rgba(var(--color-sf-on-secondary-container));
  cursor: pointer;
  display: block;
  font-size: 12px;
  line-height: 40px;
  text-indent: 1.2em;
}

.e-de-char-fmt-btn-left > button,
.e-de-insert-del-cell button {
  width: 36px;
}

.e-de-char-fmt-btn-right > button {
  width: 36px;
}

.e-de-panel-left-width {
  width: 151px;
}

.e-bigger .e-de-panel-left-width {
  width: 169px;
}
.e-bigger .e-de-char-fmt-btn-left > button,
.e-bigger .e-de-insert-del-cell button {
  width: 44px;
}

.e-de-panel-right-width {
  width: 73px;
}

.e-de-cntr-highlight-pane {
  border: 1px solid rgba(var(--color-sf-outline-variant));
}

.e-de-btn-hghlclr > button:first-child {
  padding: 1px !important;
}

.e-de-ctnr-hglt-color {
  font-size: 12px;
  font-weight: 400;
  height: 20px !important;
  width: 20px !important;
}

.e-de-font-clr-picker > div div button,
.e-de-font-clr-picker > div button,
.e-de-font-clr-picker > button {
  width: 40px;
}

.e-de-floating-menu.e-de-bullets-menu .e-de-floating-menuitem-md {
  height: 55px;
  padding: 4px !important;
  width: 60px;
}

.e-de-floating-menu.e-de-bullets-menu .e-de-floating-menuitem-md:hover {
  border: 3px solid rgba(var(--color-sf-primary));
  padding: 2px !important;
}

.e-de-floating-menu.e-de-bullets-menu .e-de-floating-bullet-menuitem-md {
  height: 38px;
  padding: 4px !important;
  width: 38px;
}

.e-de-floating-menu.e-de-bullets-menu .e-de-floating-bullet-menuitem-md:hover {
  border: 3px solid rgba(var(--color-sf-primary));
  padding: 2px !important;
}

.e-de-list-header-presetmenu {
  cursor: pointer;
  font-size: 11px;
  line-height: 14px;
  overflow: hidden;
  text-align: left;
  min-width: 50px;
  white-space: nowrap;
  width: 100%;
}

.e-de-bullet-list-header-presetmenu {
  cursor: pointer;
  font-size: 14px;
  left: -11px;
  line-height: 0;
  min-width: 50px;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  width: 100%;
}

.e-rtl .e-de-bullet-list-header-presetmenu {
  cursor: pointer;
  font-size: 14px;
  left: 10px;
  line-height: 0;
  min-width: 50px;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  width: 100%;
}

.e-de-bullet {
  font-size: 42px;
}

.e-de-list-header-presetmenu .e-de-list-line {
  border-bottom: 1px solid rgba(var(--color-sf-on-surface-variant)) !important;
  margin-left: 5px;
  width: 100%;
}

.e-de-toc-optionsdiv {
  margin-bottom: 11.5px;
  margin-left: 5.5px;
  margin-top: 15.5px;
}

.e-de-toc-optionsdiv.e-de-rtl {
  margin-right: 5.5px;
  margin-left: 0;
}

.e-de-list-header-presetmenu div span {
  display: inline-block;
  vertical-align: middle;
}

.e-de-floating-menu .e-de-floating-menuitem,
.e-de-floating-menu .e-de-menuitem-none {
  cursor: pointer;
  height: 70px;
  padding: 0 !important;
  margin: 0 5px 5px 0 !important;
  width: 70px;
}

.e-de-list-thumbnail .e-de-list-items {
  float: left;
}

.e-de-list-thumbnail .e-de-list-items {
  border: 1px solid rgba(var(--color-sf-outline-variant));
  border-radius: 4px;
  clear: initial;
  display: inline-block;
  height: auto;
  margin: 5px;
  padding: 2px;
  text-align: center;
  width: auto;
}

.e-de-list-items {
  cursor: pointer;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  box-sizing: border-box;
  list-style: none;
  padding: 7px 10px 7px 10px;
  position: relative;
}

.e-de-list-item-size {
  font-size: 14px;
}

.e-de-floating-menuitem.e-de-floating-menuitem-md.e-de-list-items.e-de-list-item-size.de-list-item-selected,
.e-de-floating-menuitem.e-de-floating-bullet-menuitem-md.e-de-list-items.e-de-list-item-size.de-list-item-selected {
  border: 3px solid rgba(var(--color-sf-primary));
  padding: 2px !important;
}

.e-de-floating-menu {
  padding: 10px 4px 5px 10px !important;
}

.e-de-list-container {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  box-sizing: border-box;
  display: inline-block;
  line-height: normal;
  margin: 0;
  outline: 0;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  padding: 10px 0;
  position: absolute;
  width: auto;
  z-index: 10020;
}

.e-de-ctnr-list {
  font-size: 12px;
  vertical-align: top;
}

.e-de-image-property {
  padding-left: 32px;
}

.e-de-img-prty-span {
  color: rgba(var(--color-sf-on-secondary-container));
  left: 12px;
  position: absolute;
  top: 8px;
}

.e-btn-toggle {
  background-color: rgba(var(--color-sf-primary-container)) !important;
  outline: none;
  outline-offset: 0;
  box-shadow: none !important;
}
.e-btn-toggle:hover {
  background-color: rgba(var(--color-sf-primary-container)) !important;
  outline: none;
  outline-offset: 0;
  box-shadow: none !important;
}

.e-de-ctnr-group-btn-top > button:first-child {
  border-radius: 0;
  border-top-left-radius: 4px;
}

.e-de-ctnr-group-btn-top.e-de-rtl > button:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 4px;
}

.e-de-ctnr-group-btn-top > button:last-child {
  border-radius: 0;
  border-top-right-radius: 4px;
}

.e-de-ctnr-group-btn-top.e-de-rtl > button:last-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 0;
}

.e-de-ctnr-group-btn-middle > button {
  border-radius: 0;
  border-top: 0;
  border-bottom: 0;
}

.e-de-ctnr-group-btn-bottom > button:first-child {
  border-radius: 0;
  border-bottom-left-radius: 4px;
}

.e-de-ctnr-group-btn-bottom.e-de-rtl > button:first-child {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 4px;
}

.e-de-ctnr-group-btn-bottom > button:last-child {
  border-radius: 0;
  border-bottom-right-radius: 4px;
}

.e-de-ctnr-group-btn-bottom.e-de-rtl > button:last-child {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 4px;
}

.e-de-toc-template1 {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border: 1px solid rgba(var(--color-sf-outline-variant));
  color: rgba(var(--color-sf-on-secondary-container));
  height: 130px;
  width: 95px;
  margin-left: 80px;
}

.e-de-toc-template1.e-de-rtl {
  margin-left: 0;
  margin-right: 78px;
}

.e-de-toc-template1-content1 {
  font-size: 10px;
  height: 16px;
  margin-left: 8px;
  margin-top: 6px;
  width: 80px;
}

.e-de-toc-template1-content2 {
  font-size: 8px;
  height: 9px;
  margin-left: 23px;
  margin-top: 6px;
  width: 66px;
}

.e-de-toc-template1-content3 {
  font-size: 7px;
  height: 8px;
  margin-left: 30px;
  margin-top: 6px;
  width: 59px;
}

.e-de-prop-sub-label {
  color: rgba(var(--color-sf-on-surface));
  font-size: 12px;
  margin-bottom: 4px;
  font-weight: 600;
}

.e-de-toc-checkbox1 {
  height: 16px;
  margin-top: 16px;
}

.e-de-toc-checkbox2 {
  height: 16px;
  margin-top: 16px;
}

.e-de-toc-checkbox3 {
  height: 16px;
  margin-top: 16px;
}

.e-de-status-bar {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}

.e-de-ctnr-pg-no {
  color: rgba(var(--color-sf-on-secondary-container));
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 14px;
  height: 100%;
  padding-top: 7px;
}
.e-de-ctnr-pg-no span {
  border: 1px solid transparent;
}

.e-de-ctnr-pg-no-spellout {
  color: rgba(var(--color-sf-on-secondary-container));
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 14px;
  height: 100%;
  padding-top: 7px;
  width: calc(100% - 169px);
}
.e-de-ctnr-pg-no-spellout span {
  border: 1px solid transparent;
}

.e-bigger .e-de-ctnr-pg-no-spellout {
  color: rgba(var(--color-sf-on-secondary-container));
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-size: 16px;
  height: 100%;
  padding-top: 10px;
  width: calc(100% - 180px);
}

.e-de-statusbar-zoom-spell {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border: 0;
  color: rgba(var(--color-sf-on-secondary-container));
  float: right;
  height: 34px;
  margin-left: calc(100% - 395px);
}

.e-bigger .e-de-statusbar-zoom-spell {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border: 0;
  color: rgba(var(--color-sf-on-secondary-container));
  float: right;
  height: 34px;
  margin-left: calc(100% - 430px);
}

.e-de-btn-cancel {
  margin-left: 10px;
}

.e-de-btn-cancel-rtl {
  margin-left: 0;
  margin-right: 10px;
}

.e-de-prop-header-label {
  color: rgba(var(--color-sf-on-secondary-container));
  display: inline-block;
  font-size: 12px;
  font-weight: bold;
  letter-spacing: 0.05px;
  opacity: 0.87;
}

.e-de-prop-separator-line {
  border-bottom: 1px solid rgba(var(--color-sf-outline-variant));
}

.e-de-status-bar > div label {
  font-weight: 600;
}

.e-de-stylediv {
  padding-left: 12px;
}

.e-de-stylediv-rtl {
  padding-left: 0;
  padding-right: 12px;
}

.e-de-border-size-button {
  height: 32px;
  margin-top: 16px;
  width: 104px;
}

.e-de-color-picker {
  height: 32px;
  width: 104px;
}

.e-de-cell-text-box {
  margin-right: 12px;
}

.e-de-pane-rtl .e-de-cell-text-box {
  margin-left: 12px;
  margin-right: 0;
}

.e-de-prop-fill-label {
  margin-right: 16px;
}

.e-de-prop-fill-label.e-de-rtl {
  margin-left: 16px;
  margin-right: 0;
}

.e-de-grp-btn-ctnr .e-de-ctnr-group-btn {
  height: 36px !important;
}

.e-bigger .e-de-grp-btn-ctnr .e-de-ctnr-group-btn {
  height: 42px !important;
}

.e-de-grp-btn-ctnr .e-de-ctnr-group-btn > button {
  width: 36px;
  height: 36px;
}

.e-bigger .e-de-grp-btn-ctnr .e-de-ctnr-group-btn > button {
  height: 42px;
  width: 44px;
}

.e-de-border-clr-picker .e-split-btn-wrapper > button:first-child {
  width: 64px;
}

.e-documenteditor-optionspane {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.11), rgba(var(--color-sf-primary), 0.11)), rgba(var(--color-sf-surface));
}

.e-rtl .e-listview .e-list-icon {
  height: 24px;
  width: 16px;
  margin-left: 12px;
}
.e-rtl .e-de-listview-icon {
  height: auto;
  width: auto;
  line-height: 22px;
  margin-left: 12px;
}

.e-bigger .de-split-button > div:first-child {
  margin-right: 16px;
}
.e-bigger .e-de-border-clr-picker .e-split-btn-wrapper > button:first-child {
  width: 60px;
}
.e-bigger .e-de-prop-fill-label {
  margin-left: 0;
  margin-right: 9.8px;
}
.e-bigger .e-de-prop-fill-label.e-de-rtl {
  margin-left: 9.8px;
  margin-right: 0px;
}
.e-bigger .e-rtl .e-de-cell-text-box {
  margin-left: 16px;
  margin-right: 0;
}
.e-bigger .e-de-color-picker {
  height: 39px;
  width: 100px;
}
.e-bigger .e-de-border-size-button {
  height: 39px;
  margin-top: 16px;
  width: 100px;
}
.e-bigger .e-de-stylediv {
  padding-left: 16px;
}
.e-bigger .e-de-stylediv-rtl {
  padding-right: 16px;
}
.e-bigger .e-de-tool-ctnr-properties-pane {
  display: -ms-flexbox;
  display: flex;
  height: calc(100% - 128px);
  min-height: 200px;
  width: 100%;
}
.e-bigger .e-de-ctnr-properties-pane {
  display: -ms-flexbox;
  display: flex;
  height: calc(100% - 46px);
  width: 100%;
}
.e-bigger .e-de-ctn {
  background-color: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border: 1px solid rgba(var(--color-sf-outline-variant));
  height: 100%;
  position: relative;
  width: 100%;
}
.e-bigger .e-de-ctnr-toolbar {
  display: -ms-flexbox;
  display: flex;
  height: 90px;
  width: 100%;
}
.e-bigger .e-de-tlbr-wrapper .e-de-toolbar.e-toolbar,
.e-bigger .e-de-tlbr-wrapper .e-de-ctnr-properties-pane-btn {
  border: 0;
}
.e-bigger .e-de-pane {
  border-left: 1px solid rgba(var(--color-sf-outline-variant));
}
.e-bigger .e-de-pane-rtl {
  border-right: 1px solid rgba(var(--color-sf-outline-variant));
}
.e-bigger .e-de-ctnr-segment {
  margin-bottom: 8px;
}
.e-bigger .e-de-ctnr-segment > div:first-child:not(.e-rtl),
.e-bigger .e-de-ctnr-segment-list > div:last-child:not(.e-rtl),
.e-bigger .e-de-ctnr-segment > button:first-child:not(.e-rtl) {
  margin-right: 16px;
}
.e-bigger .e-de-ctnr-segment.e-de-ctnr-segment-rtl > div:first-child,
.e-bigger .e-de-ctnr-segment-list.e-de-ctnr-segment-list-rtl > div:last-child,
.e-bigger .e-de-ctnr-segment.e-de-ctnr-segment-rtl > button:first-child {
  margin-left: 16px;
  margin-right: 0;
}
.e-bigger .e-de-tlbr-wrapper {
  background-color: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  height: 90px;
  width: calc(100% - 78px);
}
.e-bigger .e-de-ctnr-prop-label {
  color: rgba(var(--color-sf-on-secondary-container));
  display: inline-block;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.05px;
  margin-bottom: 8px;
  opacity: 0.87;
}
.e-bigger .e-de-table-prop-label {
  margin-left: 14.5px;
}
.e-bigger .e-de-table-prop-label.e-de-rtl {
  margin-left: 0;
  margin-right: 14.5px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items {
  height: 90px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-separator {
  margin: 0 8px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-start {
  margin-left: 8px !important;
  margin-right: 8px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-middle {
  margin-left: 8px;
  margin-right: 8px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-toolbar-btn-end {
  margin-left: 8px;
  margin-right: 8px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item.e-de-image-focus :focus {
  background-color: rgba(var(--color-sf-primary-container));
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn,
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control {
  padding: 0;
  padding-bottom: 10px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:focus,
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control:focus {
  padding: 0;
  padding-bottom: 10px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:active,
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control:active {
  padding: 0;
  padding-bottom: 10px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn:hover,
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-control:hover {
  padding: 0;
  padding-bottom: 10px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn .e-tbar-btn-text {
  padding: 0 !important;
}
.e-bigger .e-de-overlay {
  height: 100%;
  opacity: 0.5;
  pointer-events: none;
  -ms-touch-action: none;
      touch-action: none;
}
.e-bigger .e-de-ctnr-properties-pane-btn {
  width: 78px;
}
.e-bigger .e-de-ctnr-properties-pane-btn .e-btn {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border-radius: 0;
  box-shadow: none;
  min-height: 100%;
  min-width: 100%;
}
.e-bigger .e-de-ctnr-properties-pane-btn .e-btn:focus {
  box-shadow: none;
}
.e-bigger .e-de-ctnr-properties-pane-btn .e-btn:active {
  box-shadow: none;
}
.e-bigger .e-de-ctnr-properties-pane-btn .e-btn:hover {
  box-shadow: none;
}
.e-bigger .e-de-showhide-btn {
  border: 0;
  height: 90px;
}
.e-bigger .e-de-showhide-btn-rtl {
  border: 0;
  height: 90px;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item:not(.e-separator) {
  padding: 0;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
  display: table;
  font-size: 12px !important;
  margin: 0 6px;
  padding: 0;
  white-space: normal;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item button.e-btn.e-tbtn-txt .e-icons.e-btn-icon,
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn.e-btn.e-tbtn-txt .e-icons.e-btn-icon {
  padding: 0;
}
.e-bigger .e-de-ctnr-group-btn.e-btn-group:not(.e-outline) {
  box-shadow: none;
  height: 40px;
}
.e-bigger .e-de-ctnr-group-btn.e-btn-group:not(.e-outline):focus {
  box-shadow: none;
}
.e-bigger .e-de-ctnr-group-btn.e-btn-group:not(.e-outline):active {
  box-shadow: none;
}
.e-bigger .e-de-ctnr-group-btn.e-btn-group:not(.e-outline):hover {
  box-shadow: none;
}
.e-bigger .e-de-status-bar button {
  height: 38px;
  box-shadow: none;
}
.e-bigger .e-de-status-bar button:focus {
  box-shadow: none;
}
.e-bigger .e-de-status-bar button:active {
  box-shadow: none;
}
.e-bigger .e-de-status-bar button:hover {
  box-shadow: none;
}
.e-bigger .e-de-ctnr-group-btn button {
  box-shadow: none;
  height: 40px;
}
.e-bigger .e-de-ctnr-group-btn button:focus {
  box-shadow: none;
}
.e-bigger .e-de-ctnr-group-btn button:active {
  box-shadow: none;
}
.e-bigger .e-de-ctnr-group-btn button:hover {
  box-shadow: none;
}
.e-bigger .e-de-property-div-padding {
  border-bottom: 0.5px solid rgba(var(--color-sf-outline-variant));
  padding: 16px;
}
.e-bigger .e-de-font-clr-picker > div button,
.e-bigger .e-de-font-clr-picker > button {
  width: auto;
}
.e-bigger .e-de-ctnr-dropdown-ftr {
  border-top: 1px solid rgba(var(--color-sf-outline-variant));
  color: rgba(var(--color-sf-on-secondary-container));
  cursor: pointer;
  display: block;
  font-size: 12px;
  line-height: 40px;
  text-indent: 1.2em;
}
.e-bigger .e-de-char-fmt-btn > button {
  width: 38.5px;
}
.e-bigger .e-de-btn-hghlclr > button:first-child {
  padding: 0 6px !important;
}
.e-bigger .e-de-ctnr-hglt-color {
  font-size: 12px;
  font-weight: 400;
  height: 24px !important;
  width: 24px !important;
}
.e-bigger .e-de-ctnr-list {
  font-size: 12px;
  vertical-align: top;
}
.e-bigger .e-de-image-property {
  padding-left: 32px;
}
.e-bigger .e-de-img-prty-span {
  color: rgba(var(--color-sf-on-secondary-container));
  left: 10px;
  position: absolute;
  top: 12px;
}
.e-bigger .e-btn-toggle {
  background-color: rgba(var(--color-sf-primary-container)) !important;
  box-shadow: none !important;
  outline: none;
  outline-offset: 0;
}
.e-bigger .e-btn-toggle:hover {
  background-color: rgba(var(--color-sf-primary-container)) !important;
  outline: none;
  outline-offset: 0;
  box-shadow: none !important;
}
.e-bigger .e-de-toc-template1 {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  border: 1px solid rgba(var(--color-sf-outline-variant));
  color: rgba(var(--color-sf-on-secondary-container));
  height: 129px;
  margin-left: 78px;
  width: 94px;
}
.e-bigger .e-de-toc-template1-content1 {
  font-size: 10px;
  height: 11px;
  margin-left: 5.4px;
  margin-top: 6.7px;
  width: 80px;
}
.e-bigger .e-de-toc-template1-content2 {
  font-size: 8px;
  height: 9px;
  margin-left: 20.4px;
  margin-top: 5.7px;
  width: 66px;
}
.e-bigger .e-de-toc-template1-content3 {
  font-size: 7px;
  height: 8px;
  margin-left: 28.4px;
  margin-top: 6.7px;
  width: 59px;
}
.e-bigger .e-de-toc-optionsdiv {
  margin-bottom: 11.5px;
  margin-left: 5.5px;
  margin-top: 15.5px;
}
.e-bigger .e-de-toc-optionsdiv.e-de-rtl {
  margin-right: 5.5px;
  margin-left: 0;
}
.e-bigger .e-de-prop-sub-label {
  font-size: 13px;
  margin-bottom: 8.5px;
}
.e-bigger .e-de-btn-cancel {
  margin-left: 10px;
}
.e-bigger .e-de-status-bar {
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}
.e-bigger .e-de-statusbar-zoom {
  border: 0;
  color: rgba(var(--color-sf-on-secondary-container));
  float: right;
  height: 40px;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text {
  line-height: 25px;
  padding: 0 5px !important;
}
.e-bigger .e-de-toolbar.e-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text .e-de-text-wrap {
  height: 0;
  line-height: 0;
  line-height: 0.8;
}
.e-bigger .e-listview .e-list-icon {
  height: 24px;
  width: 16px;
  margin-right: 16px;
}
.e-bigger .e-de-listview-icon {
  height: auto;
  width: auto;
  line-height: 22px;
  margin-right: 16px;
}
.e-bigger .e-rtl .e-listview .e-list-icon {
  height: 24px;
  width: 16px;
  margin-left: 16px;
}
.e-bigger .e-rtl .e-de-listview-icon {
  height: auto;
  width: auto;
  line-height: 22px;
  margin-left: 16px;
}

.e-de-ctn .e-de-bzr-button:hover {
  background: linear-gradient(0deg, rgba(var(--color-sf-on-surface), 0.08), rgba(var(--color-sf-on-surface), 0.08)), rgba(var(--color-sf-surface));
  border: none;
  border-radius: 4px;
  color: rgba(var(--color-sf-on-surface));
}

.e-de-ctn .e-de-bzr-button:active {
  box-shadow: none;
  background-color: rgba(var(--color-sf-primary-container));
  border-color: rgba(var(--color-sf-primary-container));
  color: rgba(var(--color-sf-on-surface));
}

.e-de-ctn .e-de-bzr-button {
  font-weight: 400;
  font-size: 14px;
  border: none;
  background: linear-gradient(0deg, rgba(var(--color-sf-primary), 0.08), rgba(var(--color-sf-primary), 0.08)), rgba(var(--color-sf-surface));
  height: calc(100% - 10px);
  line-height: 16px;
  font-size: 12px !important;
}

.e-de-ctn .e-de-bzr-button:hover {
  background: linear-gradient(0deg, rgba(var(--color-sf-on-surface), 0.08), rgba(var(--color-sf-on-surface), 0.08)), rgba(var(--color-sf-surface));
  border: none;
  border-radius: 4px;
  color: rgba(var(--color-sf-on-surface));
}

.e-de-ctn .e-de-bzr-button:active {
  box-shadow: none;
  background-color: rgba(var(--color-sf-primary-container));
  border-color: rgba(var(--color-sf-primary-container));
  color: rgba(var(--color-sf-on-surface));
}

.e-de-ctn .e-de-ctnr-toolbar .e-de-bzr-button .e-btn-icon {
  font-size: 16px;
}
.e-bigger .e-de-ctn .e-de-ctnr-toolbar .e-de-bzr-button .e-btn-icon {
  font-size: 18px;
}
.e-bigger .e-de-ctn .e-de-ctnr-toolbar .e-de-bzr-break.e-de-bzr-button {
  padding-top: 11px !important;
}