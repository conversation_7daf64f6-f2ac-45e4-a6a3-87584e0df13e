import { ViewChangeEventArgs } from '../../document-editor/index';
import { L10n } from '@syncfusion/ej2-base';
import { DocumentEditorContainer } from '../document-editor-container';
/**
 * Represents document editor status bar.
 *
 * @private
 */
export declare class StatusBar {
    private container;
    private statusBarDiv;
    private pageCount;
    private zoom;
    private pageNumberInput;
    private editablePageNumber;
    startPage: number;
    localObj: L10n;
    private spellCheckButton;
    private currentLanguage;
    private allowSuggestion;
    private pageButton;
    private webButton;
    private pageBtn;
    private webBtn;
    private readonly documentEditor;
    private readonly editorPageCount;
    constructor(parentElement: HTMLElement, docEditor: DocumentEditorContainer);
    private initializeStatusBar;
    private addSpellCheckElement;
    private onZoom;
    private onSpellCheck;
    updateZoomContent(): void;
    private setSpellCheckValue;
    private setZoomValue;
    /**
     * Updates page count.
     *
     * @returns {void}
     */
    updatePageCount(): void;
    /**
     * Updates page number.
     *
     * @returns {void}
     */
    updatePageNumber(): void;
    updatePageNumberOnViewChange(args: ViewChangeEventArgs): void;
    private wireEvents;
    private updatePageNumberWidth;
    /**
     * @private
     * @returns {void}
     */
    toggleWebLayout(): void;
    /**
     * @private
     * @returns {void}
     */
    togglePageLayout(): void;
    private addRemoveClass;
    private createButtonTemplate;
    /**
     * @private
     * @returns {void}
     */
    destroy(): void;
}
