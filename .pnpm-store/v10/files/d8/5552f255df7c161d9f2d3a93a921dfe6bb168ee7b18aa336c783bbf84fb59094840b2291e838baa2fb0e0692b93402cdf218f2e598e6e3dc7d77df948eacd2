import { PdfViewer } from '../index';
/**
 * @hidden
 */
export declare class AjaxHandler {
    /**
     * Specifies the URL to which request to be sent.
     *
     * @default null
     */
    url: string;
    /**
     * Specifies the URL to which request to be sent.
     *
     * @default 'POST'
     */
    type: string;
    /**
     * Specifies the responseType to which request response.
     *
     * @default null
     */
    responseType: XMLHttpRequestResponseType;
    /**
     * A boolean value indicating whether the request should be sent asynchronous or not.
     *
     * @default true
     * @private
     */
    mode: boolean;
    /**
     * Specifies the ContentType to which request to be sent
     *
     * @default null
     * @private
     */
    contentType: string;
    private httpRequest;
    private pdfViewer;
    private retryCount;
    private retryStatusCodes;
    private retryTimeout;
    /**
     * Constructor for Ajax class
     * @param  {PdfViewer} pdfviewer - The pdfviewer.
     * @private
     */
    constructor(pdfviewer: PdfViewer);
    /**
     * Send the request to server
     *
     * @param  {object} jsonObj - To send to service
     * @returns {void}
     * @private
     */
    send(jsonObj: object): void;
    /**
     * Clear the http request
     * @returns {void}
     * @private
     */
    clear(): void;
    private resendRequest;
    private sendRequest;
    private addExtraData;
    private stateChange;
    private error;
    /**
     * Specifies callback function to be triggered after XmlHttpRequest is succeeded.
     * The callback will contain server response as the parameter.
     *
     * @event
     * @private
     */
    onSuccess: Function;
    /**
     * Specifies callback function to be triggered after XmlHttpRequest is got failed.
     * The callback will contain server response as the parameter.
     *
     * @event
     * @private
     */
    onFailure: Function;
    /**
     * Specifies callback function to be triggered after XmlHttpRequest is got error.
     * The callback will contain server response as the parameter.
     *
     * @event
     * @private
     */
    onError: Function;
    private successHandler;
    private failureHandler;
    private errorHandler;
    private setCustomAjaxHeaders;
}
