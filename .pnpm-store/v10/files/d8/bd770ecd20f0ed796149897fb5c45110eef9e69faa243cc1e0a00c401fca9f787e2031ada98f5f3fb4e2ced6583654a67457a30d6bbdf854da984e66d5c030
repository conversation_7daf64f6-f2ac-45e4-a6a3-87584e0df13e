export declare const sectionsProperty: string[];
export declare const fontSubstitutionTableProperty: string[];
export declare const characterFormatProperty: string[];
export declare const paragraphFormatProperty: string[];
export declare const listsProperty: string[];
export declare const abstractListsProperty: string[];
export declare const backgroundProperty: string[];
export declare const stylesProperty: string[];
export declare const commentsProperty: string[];
export declare const revisionsProperty: string[];
export declare const customXmlProperty: string[];
export declare const defaultTabWidthProperty: string[];
export declare const formattingProperty: string[];
export declare const trackChangesProperty: string[];
export declare const protectionTypeProperty: string[];
export declare const enforcementProperty: string[];
export declare const hashValueProperty: string[];
export declare const saltValueProperty: string[];
export declare const cryptProviderTypeProperty: string[];
export declare const cryptAlgorithmClassProperty: string[];
export declare const cryptAlgorithmTypeProperty: string[];
export declare const cryptAlgorithmSidProperty: string[];
export declare const cryptSpinCountProperty: string[];
export declare const doNotUseHTMLParagraphAutoSpacingProperty: string[];
export declare const alignTablesRowByRowProperty: string[];
export declare const formFieldShadingProperty: string[];
export declare const lastParagraphMarkCopiedProperty: string[];
export declare const footnotesProperty: string[];
export declare const endnotesProperty: string[];
export declare const compatibilityModeProperty: string[];
export declare const themeFontLanguagesProperty: string[];
export declare const themesProperty: string[];
export declare const nameProperty: string[];
export declare const basedOnProperty: string[];
export declare const nextProperty: string[];
export declare const linkProperty: string[];
export declare const localeIdProperty: string[];
export declare const localeIdFarEastProperty: string[];
export declare const localeIdBidiProperty: string[];
export declare const boldProperty: string[];
export declare const italicProperty: string[];
export declare const underlineProperty: string[];
export declare const baselineAlignmentProperty: string[];
export declare const strikethroughProperty: string[];
export declare const highlightColorProperty: string[];
export declare const fontSizeProperty: string[];
export declare const fontColorProperty: string[];
export declare const fontFamilyProperty: string[];
export declare const styleNameProperty: string[];
export declare const bidiProperty: string[];
export declare const bdoProperty: string[];
export declare const breakClearTypeProperty: string[];
export declare const fontSizeBidiProperty: string[];
export declare const fontFamilyBidiProperty: string[];
export declare const boldBidiProperty: string[];
export declare const italicBidiProperty: string[];
export declare const allCapsProperty: string[];
export declare const complexScriptProperty: string[];
export declare const fontFamilyAsciiProperty: string[];
export declare const fontFamilyFarEastProperty: string[];
export declare const fontFamilyNonFarEastProperty: string[];
export declare const revisionIdsProperty: string[];
export declare const listIdProperty: string[];
export declare const characterSpacingProperty: string[];
export declare const scalingProperty: string[];
export declare const listLevelNumberProperty: string[];
export declare const leftIndentProperty: string[];
export declare const rightIndentProperty: string[];
export declare const firstLineIndentProperty: string[];
export declare const textAlignmentProperty: string[];
export declare const afterSpacingProperty: string[];
export declare const beforeSpacingProperty: string[];
export declare const spaceAfterAutoProperty: string[];
export declare const spaceBeforeAutoProperty: string[];
export declare const lineSpacingProperty: string[];
export declare const lineSpacingTypeProperty: string[];
export declare const listFormatProperty: string[];
export declare const keepWithNextProperty: string[];
export declare const widowControlProperty: string[];
export declare const keepLinesTogetherProperty: string[];
export declare const outlineLevelProperty: string[];
export declare const contextualSpacingProperty: string[];
export declare const bordersProperty: string[];
export declare const tabsProperty: string[];
export declare const headerDistanceProperty: string[];
export declare const footerDistanceProperty: string[];
export declare const differentFirstPageProperty: string[];
export declare const differentOddAndEvenPagesProperty: string[];
export declare const pageWidthProperty: string[];
export declare const pageHeightProperty: string[];
export declare const leftMarginProperty: string[];
export declare const rightMarginProperty: string[];
export declare const topMarginProperty: string[];
export declare const bottomMarginProperty: string[];
export declare const restartPageNumberingProperty: string[];
export declare const pageStartingNumberProperty: string[];
export declare const endnoteNumberFormatProperty: string[];
export declare const footNoteNumberFormatProperty: string[];
export declare const restartIndexForFootnotesProperty: string[];
export declare const restartIndexForEndnotesProperty: string[];
export declare const initialFootNoteNumberProperty: string[];
export declare const initialEndNoteNumberProperty: string[];
export declare const pageNumberStyleProperty: string[];
export declare const columnsProperty: string[];
export declare const numberOfColumnsProperty: string[];
export declare const equalWidthProperty: string[];
export declare const lineBetweenColumnsProperty: string[];
export declare const breakCodeProperty: string[];
export declare const cellWidthProperty: string[];
export declare const columnSpanProperty: string[];
export declare const rowSpanProperty: string[];
export declare const verticalAlignmentProperty: string[];
export declare const allowBreakAcrossPagesProperty: string[];
export declare const isHeaderProperty: string[];
export declare const heightTypeProperty: string[];
export declare const beforeWidthProperty: string[];
export declare const afterWidthProperty: string[];
export declare const gridBeforeProperty: string[];
export declare const gridBeforeWidthProperty: string[];
export declare const gridBeforeWidthTypeProperty: string[];
export declare const gridAfterProperty: string[];
export declare const gridAfterWidthProperty: string[];
export declare const gridAfterWidthTypeProperty: string[];
export declare const allowAutoFitProperty: string[];
export declare const cellSpacingProperty: string[];
export declare const shadingProperty: string[];
export declare const tableAlignmentProperty: string[];
export declare const preferredWidthProperty: string[];
export declare const preferredWidthTypeProperty: string[];
export declare const horizontalPositionAbsProperty: string[];
export declare const textureProperty: string[];
export declare const backgroundColorProperty: string[];
export declare const foregroundColorProperty: string[];
export declare const shadowProperty: string[];
export declare const hasNoneStyleProperty: string[];
export declare const verticalProperty: string[];
export declare const horizontalProperty: string[];
export declare const diagonalUpProperty: string[];
export declare const diagonalDownProperty: string[];
export declare const lineStyleProperty: string[];
export declare const lineWidthProperty: string[];
export declare const layoutProperty: string[];
export declare const dataFormatProperty: string[];
export declare const yValueProperty: string[];
export declare const chartDataProperty: string[];
export declare const categoryXNameProperty: string[];
export declare const lineProperty: string[];
export declare const foreColorProperty: string[];
export declare const patternProperty: string[];
export declare const layoutXProperty: string[];
export declare const layoutYProperty: string[];
export declare const directionProperty: string[];
export declare const endStyleProperty: string[];
export declare const numberValueProperty: string[];
export declare const markerStyleProperty: string[];
export declare const markerColorProperty: string[];
export declare const markerSizeProperty: string[];
export declare const forwardProperty: string[];
export declare const backwardProperty: string[];
export declare const interceptProperty: string[];
export declare const isDisplayRSquaredProperty: string[];
export declare const isDisplayEquationProperty: string[];
export declare const seriesNameProperty: string[];
export declare const dataLabelProperty: string[];
export declare const errorBarProperty: string[];
export declare const seriesFormatProperty: string[];
export declare const trendLinesProperty: string[];
export declare const dataPointsProperty: string[];
export declare const firstSliceAngleProperty: string[];
export declare const holeSizeProperty: string[];
export declare const isLegendKeyProperty: string[];
export declare const isBubbleSizeProperty: string[];
export declare const isCategoryNameProperty: string[];
export declare const isSeriesNameProperty: string[];
export declare const isValueProperty: string[];
export declare const isPercentageProperty: string[];
export declare const isLeaderLinesProperty: string[];
export declare const showSeriesKeysProperty: string[];
export declare const hasHorizontalBorderProperty: string[];
export declare const hasVerticalBorderProperty: string[];
export declare const hasBordersProperty: string[];
export declare const categoryTypeProperty: string[];
export declare const chartCategoryProperty: string[];
export declare const chartSeriesProperty: string[];
export declare const chartAreaProperty: string[];
export declare const chartTitleAreaProperty: string[];
export declare const plotAreaProperty: string[];
export declare const chartLegendProperty: string[];
export declare const chartPrimaryCategoryAxisProperty: string[];
export declare const chartPrimaryValueAxisProperty: string[];
export declare const chartTitleProperty: string[];
export declare const chartTypeProperty: string[];
export declare const gapWidthProperty: string[];
export declare const overlapProperty: string[];
export declare const chartDataTableProperty: string[];
export declare const textProperty: string[];
export declare const shapeIdProperty: string[];
export declare const alternativeTextProperty: string[];
export declare const visibleProperty: string[];
export declare const belowTextProperty: string[];
export declare const widthProperty: string[];
export declare const heightProperty: string[];
export declare const widthScaleProperty: string[];
export declare const heightScaleProperty: string[];
export declare const lineFormatProperty: string[];
export declare const fillFormatProperty: string[];
export declare const textWrappingStyleProperty: string[];
export declare const textWrappingTypeProperty: string[];
export declare const verticalRelativePercentProperty: string[];
export declare const horizontalRelativePercentProperty: string[];
export declare const heightRelativePercentProperty: string[];
export declare const widthRelativePercentProperty: string[];
export declare const zOrderPositionProperty: string[];
export declare const layoutInCellProperty: string[];
export declare const lockAnchorProperty: string[];
export declare const autoShapeTypeProperty: string[];
export declare const textFrameProperty: string[];
export declare const colorProperty: string[];
export declare const fillProperty: string[];
export declare const textVerticalAlignmentProperty: string[];
export declare const imageStringProperty: string[];
export declare const metaFileImageStringProperty: string[];
export declare const lengthProperty: string[];
export declare const isInlineImageProperty: string[];
export declare const isMetaFileProperty: string[];
export declare const topProperty: string[];
export declare const bottomProperty: string[];
export declare const rightProperty: string[];
export declare const leftProperty: string[];
export declare const getImageHeightProperty: string[];
export declare const getImageWidthProperty: string[];
export declare const hasFieldEndProperty: string[];
export declare const formFieldDataProperty: string[];
export declare const fieldTypeProperty: string[];
export declare const enabledProperty: string[];
export declare const helpTextProperty: string[];
export declare const statusTextProperty: string[];
export declare const textInputProperty: string[];
export declare const checkBoxProperty: string[];
export declare const dropDownListProperty: string[];
export declare const maxLengthProperty: string[];
export declare const defaultValueProperty: string[];
export declare const formatProperty: string[];
export declare const sizeTypeProperty: string[];
export declare const sizeProperty: string[];
export declare const checkedProperty: string[];
export declare const dropDownItemsProperty: string[];
export declare const selectedIndexProperty: string[];
export declare const commentIdProperty: string[];
export declare const commentCharacterTypeProperty: string[];
export declare const authorProperty: string[];
export declare const initialProperty: string[];
export declare const dateProperty: string[];
export declare const doneProperty: string[];
export declare const replyCommentsProperty: string[];
export declare const revisionTypeProperty: string[];
export declare const revisionIdProperty: string[];
export declare const itemIDProperty: string[];
export declare const xmlProperty: string[];
export declare const footnoteTypeProperty: string[];
export declare const symbolCodeProperty: string[];
export declare const symbolFontNameProperty: string[];
export declare const customMarkerProperty: string[];
export declare const inlinesProperty: string[];
export declare const contentControlPropertiesProperty: string[];
export declare const lockContentControlProperty: string[];
export declare const lockContentsProperty: string[];
export declare const tagProperty: string[];
export declare const titleProperty: string[];
export declare const hasPlaceHolderTextProperty: string[];
export declare const multiLineProperty: string[];
export declare const isTemporaryProperty: string[];
export declare const dateCalendarTypeProperty: string[];
export declare const dateStorageFormatProperty: string[];
export declare const dateDisplayLocaleProperty: string[];
export declare const dateDisplayFormatProperty: string[];
export declare const isCheckedProperty: string[];
export declare const uncheckedStateProperty: string[];
export declare const checkedStateProperty: string[];
export declare const contentControlListItemsProperty: string[];
export declare const xmlMappingProperty: string[];
export declare const fontProperty: string[];
export declare const valueProperty: string[];
export declare const displayTextProperty: string[];
export declare const isMappedProperty: string[];
export declare const isWordMlProperty: string[];
export declare const prefixMappingProperty: string[];
export declare const xPathProperty: string[];
export declare const storeItemIdProperty: string[];
export declare const customXmlPartProperty: string[];
export declare const idProperty: string[];
export declare const cellFormatProperty: string[];
export declare const rowFormatProperty: string[];
export declare const cellsProperty: string[];
export declare const rowsProperty: string[];
export declare const descriptionProperty: string[];
export declare const wrapTextAroundProperty: string[];
export declare const positioningProperty: string[];
export declare const tableFormatProperty: string[];
export declare const allowOverlapProperty: string[];
export declare const distanceTopProperty: string[];
export declare const distanceRightProperty: string[];
export declare const distanceLeftProperty: string[];
export declare const distanceBottomProperty: string[];
export declare const verticalOriginProperty: string[];
export declare const verticalPositionProperty: string[];
export declare const horizontalOriginProperty: string[];
export declare const horizontalAlignmentProperty: string[];
export declare const horizontalPositionProperty: string[];
export declare const blocksProperty: string[];
export declare const headerProperty: string[];
export declare const footerProperty: string[];
export declare const evenHeaderProperty: string[];
export declare const evenFooterProperty: string[];
export declare const firstPageHeaderProperty: string[];
export declare const firstPageFooterProperty: string[];
export declare const headersFootersProperty: string[];
export declare const sectionFormatProperty: string[];
export declare const listLevelPatternProperty: string[];
export declare const followCharacterProperty: string[];
export declare const startAtProperty: string[];
export declare const restartLevelProperty: string[];
export declare const levelNumberProperty: string[];
export declare const numberFormatProperty: string[];
export declare const abstractListIdProperty: string[];
export declare const nsidProperty: string;
export declare const levelsProperty: string[];
export declare const overrideListLevelProperty: string[];
export declare const levelOverridesProperty: string[];
export declare const separatorProperty: string[];
export declare const continuationSeparatorProperty: string[];
export declare const continuationNoticeProperty: string[];
export declare const bookmarkTypeProperty: string[];
export declare const propertiesProperty: string[];
export declare const tabJustificationProperty: string[];
export declare const positionProperty: string[];
export declare const deletePositionProperty: string[];
export declare const leaderProperty: string[];
export declare const tabLeaderProperty: string[];
export declare const editRangeIdProperty: string[];
export declare const columnFirstProperty: string[];
export declare const columnLastProperty: string[];
export declare const userProperty: string[];
export declare const groupProperty: string[];
export declare const editableRangeStartProperty: string[];
export declare const spaceProperty: string[];
export declare const fontSchemeProperty: string[];
export declare const fontSchemeNameProperty: string[];
export declare const majorFontSchemeProperty: string[];
export declare const minorFontSchemeProperty: string[];
export declare const fontSchemeListProperty: string[];
export declare const fontTypefaceProperty: string[];
export declare const typefaceProperty: string[];
export declare const panoseProperty: string[];
export declare const typeProperty: string[];
export declare const majorUnitProperty: string[];
export declare const maximumValueProperty: string[];
export declare const minimumValueProperty: string[];
export declare const hasMajorGridLinesProperty: string[];
export declare const hasMinorGridLinesProperty: string[];
export declare const majorTickMarkProperty: string[];
export declare const minorTickMarkProperty: string[];
export declare const tickLabelPositionProperty: string[];
export declare const rgbProperty: string[];
export declare const appearanceProperty: string[];
export declare const lineFormatTypeProperty: string[];
export declare const allowSpaceOfSameStyleInTableProperty: string[];
export declare const weightProperty: string[];
export declare const inlineFormatProperty: string[];
export declare const fontNameProperty: string[];
export declare const isCompressedProperty: string[];
export declare const columnIndexProperty: string[];
export declare const imagesProperty: string[];
export declare const isAfterParagraphMarkProperty: string[];
export declare const isAfterCellMarkProperty: string[];
export declare const isAfterRowMarkProperty: string[];
export declare const gridProperty: string[];
export declare const columnCountProperty: string[];
export declare const isAfterTableMarkProperty: string[];
