/* eslint-disable camelcase */
/*eslint camelcase: ["error", {properties: "never"}]*/
/**
 * @hidden
 */
export var CommonErrors;
(function (CommonErrors) {
    CommonErrors[CommonErrors["na"] = 0] = "na";
    CommonErrors[CommonErrors["value"] = 1] = "value";
    CommonErrors[CommonErrors["ref"] = 2] = "ref";
    CommonErrors[CommonErrors["divzero"] = 3] = "divzero";
    CommonErrors[CommonErrors["num"] = 4] = "num";
    CommonErrors[CommonErrors["name"] = 5] = "name";
    CommonErrors[CommonErrors["null"] = 6] = "null";
})(CommonErrors || (CommonErrors = {}));
/**
 * @hidden
 */
export var FormulasErrorsStrings;
(function (FormulasErrorsStrings) {
    FormulasErrorsStrings[FormulasErrorsStrings["operators_cannot_start_with_expression"] = 0] = "operators_cannot_start_with_expression";
    FormulasErrorsStrings[FormulasErrorsStrings["reservedWord_And"] = 1] = "reservedWord_And";
    FormulasErrorsStrings[FormulasErrorsStrings["reservedWord_Xor"] = 2] = "reservedWord_Xor";
    FormulasErrorsStrings[FormulasErrorsStrings["reservedWord_If"] = 3] = "reservedWord_If";
    FormulasErrorsStrings[FormulasErrorsStrings["number_contains_2_decimal_points"] = 4] = "number_contains_2_decimal_points";
    FormulasErrorsStrings[FormulasErrorsStrings["reservedWord_Else"] = 5] = "reservedWord_Else";
    FormulasErrorsStrings[FormulasErrorsStrings["reservedWord_Not"] = 6] = "reservedWord_Not";
    FormulasErrorsStrings[FormulasErrorsStrings["invalid_char_in_number"] = 7] = "invalid_char_in_number";
    FormulasErrorsStrings[FormulasErrorsStrings["invalid_characters_following_with_operator"] = 6] = "invalid_characters_following_with_operator";
    FormulasErrorsStrings[FormulasErrorsStrings["mismatched_parentheses"] = 8] = "mismatched_parentheses";
    FormulasErrorsStrings[FormulasErrorsStrings["unknown_formula_name"] = 9] = "unknown_formula_name";
    FormulasErrorsStrings[FormulasErrorsStrings["requires_a_single_argument"] = 10] = "requires_a_single_argument";
    FormulasErrorsStrings[FormulasErrorsStrings["requires_3_args"] = 11] = "requires_3_args";
    FormulasErrorsStrings[FormulasErrorsStrings["invalid_Math_argument"] = 12] = "invalid_Math_argument";
    FormulasErrorsStrings[FormulasErrorsStrings["requires_2_args"] = 13] = "requires_2_args";
    FormulasErrorsStrings[FormulasErrorsStrings["bad_index"] = 14] = "bad_index";
    FormulasErrorsStrings[FormulasErrorsStrings["too_complex"] = 15] = "too_complex";
    FormulasErrorsStrings[FormulasErrorsStrings["circular_reference"] = 16] = "circular_reference";
    FormulasErrorsStrings[FormulasErrorsStrings["missing_formula"] = 17] = "missing_formula";
    FormulasErrorsStrings[FormulasErrorsStrings["improper_formula"] = 18] = "improper_formula";
    FormulasErrorsStrings[FormulasErrorsStrings["invalid_expression"] = 19] = "invalid_expression";
    FormulasErrorsStrings[FormulasErrorsStrings["cell_empty"] = 20] = "cell_empty";
    FormulasErrorsStrings[FormulasErrorsStrings["bad_formula"] = 21] = "bad_formula";
    FormulasErrorsStrings[FormulasErrorsStrings["empty_expression"] = 22] = "empty_expression";
    FormulasErrorsStrings[FormulasErrorsStrings["virtual_mode_required"] = 23] = "virtual_mode_required";
    FormulasErrorsStrings[FormulasErrorsStrings["mismatched_tics"] = 24] = "mismatched_tics";
    FormulasErrorsStrings[FormulasErrorsStrings["wrong_number_arguments"] = 25] = "wrong_number_arguments";
    FormulasErrorsStrings[FormulasErrorsStrings["invalid_arguments"] = 26] = "invalid_arguments";
    FormulasErrorsStrings[FormulasErrorsStrings["iterations_do_not_converge"] = 27] = "iterations_do_not_converge";
    FormulasErrorsStrings[FormulasErrorsStrings["calculation_overflow"] = 29] = "calculation_overflow";
    FormulasErrorsStrings[FormulasErrorsStrings["already_registered"] = 28] = "already_registered";
    FormulasErrorsStrings[FormulasErrorsStrings["missing_sheet"] = 30] = "missing_sheet";
    FormulasErrorsStrings[FormulasErrorsStrings["cannot_parse"] = 31] = "cannot_parse";
    FormulasErrorsStrings[FormulasErrorsStrings["expression_cannot_end_with_an_operator"] = 32] = "expression_cannot_end_with_an_operator";
    FormulasErrorsStrings[FormulasErrorsStrings["spill"] = 33] = "spill";
    FormulasErrorsStrings[FormulasErrorsStrings["div"] = 34] = "div";
})(FormulasErrorsStrings || (FormulasErrorsStrings = {}));
/**
 * @hidden
 */
export var ExcelFileFormats;
(function (ExcelFileFormats) {
    ExcelFileFormats["xlsx"] = "xlsx";
    ExcelFileFormats["xlsm"] = "xlsm";
    ExcelFileFormats["xlsb"] = "xlsb";
    ExcelFileFormats["xltx"] = "xltx";
    ExcelFileFormats["xltm"] = "xltm";
    ExcelFileFormats["xls"] = "xls";
    ExcelFileFormats["xml"] = "xml";
    ExcelFileFormats["xlam"] = "xlam";
    ExcelFileFormats["xla"] = "xla";
    ExcelFileFormats["xlw"] = "xlw";
    ExcelFileFormats["xlr"] = "xlr";
    ExcelFileFormats["prn"] = "prn";
    ExcelFileFormats["txt"] = "txt";
    ExcelFileFormats["csv"] = "csv";
    ExcelFileFormats["dif"] = "dif";
    ExcelFileFormats["slk"] = "slk";
})(ExcelFileFormats || (ExcelFileFormats = {}));
