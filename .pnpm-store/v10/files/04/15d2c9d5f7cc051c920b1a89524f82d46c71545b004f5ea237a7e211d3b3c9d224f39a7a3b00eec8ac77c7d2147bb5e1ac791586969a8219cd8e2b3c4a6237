@import 'ej2-base/styles/definition/highcontrast.scss';
@import '../spreadsheet-ribbon/highcontrast-definition.scss';
@import 'ej2-buttons/styles/button/highcontrast-definition.scss';
@import 'ej2-buttons/styles/check-box/highcontrast-definition.scss';
@import 'ej2-buttons/styles/radio-button/highcontrast-definition.scss';
@import 'ej2-buttons/styles/switch/highcontrast-definition.scss';
@import 'ej2-navigations/styles/toolbar/highcontrast-definition.scss';
@import 'ej2-navigations/styles/tab/highcontrast-definition.scss';
@import 'ej2-navigations/styles/context-menu/highcontrast-definition.scss';
@import 'ej2-navigations/styles/menu/highcontrast-definition.scss';
@import 'ej2-navigations/styles/treeview/highcontrast-definition.scss';
@import 'ej2-grids/styles/excel-filter/highcontrast-definition.scss';
@import 'ej2-calendars/styles/datepicker/highcontrast-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/highcontrast-definition.scss';
@import 'ej2-inputs/styles/color-picker/highcontrast-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/highcontrast-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/highcontrast-definition.scss';
@import 'ej2-dropdowns/styles/list-box/highcontrast-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/highcontrast-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/highcontrast-definition.scss';
@import 'highcontrast-definition.scss';
@import 'icons/highcontrast.scss';
@import 'all.scss';
