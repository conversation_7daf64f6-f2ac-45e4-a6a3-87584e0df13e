@import 'ej2-base/styles/definition/material-dark.scss';
@import '../spreadsheet-ribbon/material-dark-definition.scss';
@import 'ej2-buttons/styles/button/material-dark-definition.scss';
@import 'ej2-buttons/styles/check-box/material-dark-definition.scss';
@import 'ej2-buttons/styles/radio-button/material-dark-definition.scss';
@import 'ej2-buttons/styles/switch/material-dark-definition.scss';
@import 'ej2-navigations/styles/toolbar/material-dark-definition.scss';
@import 'ej2-navigations/styles/tab/material-dark-definition.scss';
@import 'ej2-navigations/styles/context-menu/material-dark-definition.scss';
@import 'ej2-navigations/styles/menu/material-dark-definition.scss';
@import 'ej2-navigations/styles/treeview/material-dark-definition.scss';
@import 'ej2-grids/styles/excel-filter/material-dark-definition.scss';
@import 'ej2-calendars/styles/datepicker/material-dark-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/material-dark-definition.scss';
@import 'ej2-inputs/styles/color-picker/material-dark-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/material-dark-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/material-dark-definition.scss';
@import 'ej2-dropdowns/styles/list-box/material-dark-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/material-dark-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/material-dark-definition.scss';
@import 'material-dark-definition.scss';
@import 'icons/material-dark.scss';
@import 'all.scss';
