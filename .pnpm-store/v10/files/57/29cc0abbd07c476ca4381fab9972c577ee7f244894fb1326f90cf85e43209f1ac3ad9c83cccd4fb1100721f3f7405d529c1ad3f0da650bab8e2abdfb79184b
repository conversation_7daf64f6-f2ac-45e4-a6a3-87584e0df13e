@import 'ej2-base/styles/definition/fabric-dark.scss';
@import '../spreadsheet-ribbon/fabric-dark-definition.scss';
@import 'ej2-buttons/styles/button/fabric-dark-definition.scss';
@import 'ej2-buttons/styles/check-box/fabric-dark-definition.scss';
@import 'ej2-buttons/styles/radio-button/fabric-dark-definition.scss';
@import 'ej2-buttons/styles/switch/fabric-dark-definition.scss';
@import 'ej2-navigations/styles/toolbar/fabric-dark-definition.scss';
@import 'ej2-navigations/styles/tab/fabric-dark-definition.scss';
@import 'ej2-navigations/styles/context-menu/fabric-dark-definition.scss';
@import 'ej2-navigations/styles/menu/fabric-dark-definition.scss';
@import 'ej2-navigations/styles/treeview/fabric-dark-definition.scss';
@import 'ej2-grids/styles/excel-filter/fabric-dark-definition.scss';
@import 'ej2-calendars/styles/datepicker/fabric-dark-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/fabric-dark-definition.scss';
@import 'ej2-inputs/styles/color-picker/fabric-dark-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/fabric-dark-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/fabric-dark-definition.scss';
@import 'ej2-dropdowns/styles/list-box/fabric-dark-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/fabric-dark-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/fabric-dark-definition.scss';
@import 'fabric-dark-definition.scss';
@import 'icons/fabric-dark.scss';
@import 'all.scss';
