@import 'ej2-base/styles/definition/bootstrap5-dark.scss';
@import 'ej2-buttons/styles/button/bootstrap5-dark-definition.scss';
@import 'ej2-buttons/styles/check-box/bootstrap5-dark-definition.scss';
@import 'ej2-buttons/styles/radio-button/bootstrap5-dark-definition.scss';
@import 'ej2-buttons/styles/switch/bootstrap5-dark-definition.scss';
@import 'ej2-navigations/styles/toolbar/bootstrap5-dark-definition.scss';
@import 'ej2-navigations/styles/tab/bootstrap5-dark-definition.scss';
@import 'ej2-navigations/styles/context-menu/bootstrap5-dark-definition.scss';
@import 'ej2-navigations/styles/menu/bootstrap5-dark-definition.scss';
@import 'ej2-navigations/styles/treeview/bootstrap5-dark-definition.scss';
@import 'ej2-grids/styles/excel-filter/bootstrap5-dark-definition.scss';
@import 'ej2-calendars/styles/datepicker/bootstrap5-dark-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/bootstrap5-dark-definition.scss';
@import 'ej2-inputs/styles/color-picker/bootstrap5-dark-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/bootstrap5-dark-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/bootstrap5-dark-definition.scss';
@import 'ej2-dropdowns/styles/list-box/bootstrap5-dark-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/bootstrap5-dark-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/bootstrap5-dark-definition.scss';
@import 'bootstrap5-dark-definition.scss';
@import 'icons/bootstrap5-dark.scss';
@import 'all.scss';
