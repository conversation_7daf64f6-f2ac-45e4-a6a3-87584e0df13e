import { StickyNotesSettings } from './../pdfviewer';
import { PdfViewerBase, PdfViewer, AllowedInteraction, IPoint } from '../index';
import { AnnotationSelectorSettingsModel } from '../pdfviewer-model';
/**
 * @hidden
 */
export interface IPopupAnnotation {
    shapeAnnotationType: string;
    pathData: string;
    author: string;
    subject: string;
    modifiedDate: string;
    note: string;
    bounds: any;
    color: any;
    opacity: number;
    state: string;
    stateModel: string;
    comments: ICommentsCollection[];
    review: IReviewCollection;
    annotName: string;
    pageNumber: number;
    annotationSelectorSettings: AnnotationSelectorSettingsModel;
    customData: object;
    annotationSettings: any;
    allowedInteractions: AllowedInteraction;
    isPrint: boolean;
    isCommentLock: boolean;
}
/**
 * @hidden
 */
export interface ICommentsCollection {
    author: string;
    modifiedDate: string;
    annotName: string;
    subject: string;
    parentId: string;
    note: string;
    state: string;
    stateModel: string;
    comments: ICommentsCollection[];
    review: IReviewCollection;
    shapeAnnotationType: string;
    position?: number;
    isLock: boolean;
}
/**
 * @hidden
 */
export interface IReviewCollection {
    author: string;
    state: string;
    stateModel: string;
    modifiedDate: string;
    annotId?: string;
}
/**
 * StickyNotes module
 */
export declare class StickyNotesAnnotation {
    private pdfViewer;
    private pdfViewerBase;
    private accordionContent;
    private accordionPageContainer;
    private accordionContentContainer;
    private commentsContainer;
    private commentMenuObj;
    private moreButtonId;
    private commentsCount;
    private commentsreplyCount;
    private commentContextMenu;
    private isAccordionContainer;
    private isSetAnnotationType;
    private isNewcommentAdded;
    private isCreateContextMenu;
    private commentsRequestHandler;
    private selectAnnotationObj;
    private isCommentsSelected;
    /**
     * @private
     */
    isAddAnnotationProgramatically: boolean;
    /**
     * @private
     */
    isEditableElement: boolean;
    /**
     * @private
     */
    accordionContainer: HTMLElement;
    /**
     * @private
     */
    mainContainer: HTMLElement;
    /**
     * @private
     */
    opacity: number;
    private isPageCommentsRendered;
    private isCommentsRendered;
    /**
     * @private
     */
    isAnnotationRendered: boolean;
    private globalize;
    /**
     * @private
     */
    textFromCommentPanel: boolean;
    /**
     * @param pdfViewer
     * @param pdfViewerBase
     * @param pdfViewer
     * @param pdfViewerBase
     * @private
     */
    constructor(pdfViewer: PdfViewer, pdfViewerBase: PdfViewerBase);
    /**
     * @param stickyAnnotations
     * @param pageNumber
     * @param canvas
     * @private
     */
    renderStickyNotesAnnotations(stickyAnnotations: any, pageNumber: number, canvas?: any): void;
    /**
     * @param annotation
     * @private
     */
    getSettings(annotation: any): any;
    /**
     * @param X
     * @param Y
     * @param width
     * @param height
     * @param pageIndex
     * @param annotation
     * @param canvas
     * @param X
     * @param Y
     * @param width
     * @param height
     * @param pageIndex
     * @param annotation
     * @param canvas
     * @param X
     * @param Y
     * @param width
     * @param height
     * @param pageIndex
     * @param annotation
     * @param canvas
     * @private
     */
    drawStickyNotes(X: number, Y: number, width: number, height: number, pageIndex: number, annotation: any, canvas?: any): any;
    private setImageSource;
    /**
     * @private
     */
    createRequestForComments(): void;
    private renderCommentsOnSuccess;
    /**
     * @param excistingAnnotation
     * @param newAnnotation
     * @private
     */
    updateAnnotationsInDocumentCollections(excistingAnnotation: any, newAnnotation: any): any;
    private updateDocumentAnnotationCollections;
    private renderAnnotationCollections;
    /**
     * @param annotation
     * @param isSignature
     * @param annotation
     * @param isSignature
     * @private
     */
    updateCollections(annotation: any, isSignature?: boolean): void;
    /**
     * @param data
     * @param pageIndex
     * @private
     */
    renderAnnotationComments(data: any, pageIndex: number): void;
    /**
     * @private
     */
    initializeAcccordionContainer(): void;
    /**
     * @private
     */
    updateCommentPanelTextTop(): void;
    /**
     * @param pageIndex
     * @private
     */
    createPageAccordion(pageIndex: number): any;
    private alignAccordionContainer;
    /**
     * @param pageNumber
     * @private
     */
    updateCommentPanelScrollTop(pageNumber: number): void;
    private getButtonState;
    /**
     * @param data
     * @param pageIndex
     * @param type
     * @param annotationSubType
     * @param data
     * @param pageIndex
     * @param type
     * @param annotationSubType
     * @param data
     * @param pageIndex
     * @param type
     * @param annotationSubType
     * @private
     */
    createCommentControlPanel(data: any, pageIndex: number, type?: string, annotationSubType?: string, isReRender?: boolean): string;
    private commentDivFocus;
    private updateScrollPosition;
    private updateCommentsScrollTop;
    /**
     * @param args
     * @private
     */
    createCommentDiv(args: any): void;
    /**
     * @param args
     * @param comment
     * @private
     */
    saveCommentDiv(args: any, comment: any): void;
    private renderComments;
    /**
     * @param data
     * @param pageIndex
     * @param isCopy
     * @param data
     * @param pageIndex
     * @param isCopy
     * @private
     */
    createCommentsContainer(data: any, pageIndex: number, isCopy?: boolean): string;
    private modifyProperty;
    private createTitleContainer;
    private createReplyDivTitleContainer;
    private updateCommentIcon;
    private updateStatusContainer;
    /**
     * @param removeDiv
     * @private
     */
    updateAccordionContainer(removeDiv: HTMLElement): void;
    /**
     * @private
     */
    createCommentContextMenu(): void;
    private contextMenuBeforeOpen;
    private commentMenuItemSelect;
    private moreOptionsClick;
    private openTextEditor;
    private checkIslockProperty;
    private openEditorElement;
    private commentsDivClickEvent;
    private commentsDivDoubleClickEvent;
    private commentDivOnSelect;
    private commentDivMouseOver;
    private commentDivMouseLeave;
    /**
     * @param event
     * @private
     */
    drawIcons(event: any): void;
    /**
     * @param annotationType
     * @param pageNumber
     * @param annotationSubType
     * @param annotationType
     * @param pageNumber
     * @param annotationSubType
     * @param annotationType
     * @param pageNumber
     * @param annotationSubType
     * @private
     */
    addComments(annotationType: string, pageNumber: number, annotationSubType?: string): string;
    private commentsAnnotationSelect;
    private findAnnotationObject;
    private checkAnnotationSettings;
    private updateCommentsContainerWidth;
    /**
     * @param pageIndex
     * @private
     */
    selectCommentsAnnotation(pageIndex: number): void;
    private setAnnotationType;
    private modifyTextProperty;
    /**
     * @param date
     * @private
     */
    getDateAndTime(date?: any): string;
    private modifyCommentsProperty;
    private modifyStatusProperty;
    /**
     * @param commentsElement
     * @param replyElement
     * @private
     */
    modifyCommentDeleteProperty(commentsElement: any, replyElement: any): any;
    /**
     * @param annotation
     * @private
     */
    updateOpacityValue(annotation: any): void;
    /**
     * @param annotation
     * @param isAction
     * @param undoAnnotation
     * @param annotation
     * @param isAction
     * @param undoAnnotation
     * @param annotation
     * @param isAction
     * @param undoAnnotation
     * @private
     */
    undoAction(annotation: any, isAction: string, undoAnnotation?: any): any;
    /**
     * @param annotation
     * @param isAction
     * @param undoAnnotation
     * @param annotation
     * @param isAction
     * @param undoAnnotation
     * @private
     */
    redoAction(annotation: any, isAction: string, undoAnnotation?: any): any;
    private updateUndoRedoCollections;
    /**
     * @param pageIndex
     * @param type
     * @private
     */
    addAnnotationComments(pageIndex: any, type: string): void;
    /**
     * @param annotation
     * @param type
     * @param action
     * @private
     */
    findPosition(annotation: any, type: string, action?: string): any;
    private getAnnotations;
    private manageAnnotations;
    updateStickyNotes(annotation: any, id: any): any;
    saveStickyAnnotations(): any;
    private deleteStickyNotesAnnotations;
    addStickyNotesAnnotations(pageNumber: number, annotationBase: any): void;
    /**
     * @param annotName
     * @param text
     * @private
     */
    addTextToComments(annotName: string, text: string): void;
    /**
     * @param newAnnotation
     * @param annotation
     * @param isCut
     * @param newAnnotation
     * @param annotation
     * @param isCut
     * @param newAnnotation
     * @param annotation
     * @param isCut
     * @private
     */
    updateAnnotationCollection(newAnnotation: any, annotation: any, isCut: boolean): void;
    private findAnnotationType;
    private setExistingAnnotationModifiedDate;
    private updateModifiedTime;
    private setModifiedDate;
    private convertUTCDateToLocalDate;
    private updateModifiedDate;
    /**
     * @param annotation
     * @param isBounds
     * @param isUndoRedoAction
     * @private
     */
    updateAnnotationModifiedDate(annotation: any, isBounds?: boolean, isUndoRedoAction?: boolean): void;
    /**
     * @param annotation
     * @param pageNumber
     * @param annotation
     * @param pageNumber
     * @private
     */
    saveImportedStickyNotesAnnotations(annotation: any, pageNumber: number): any;
    /**
     * @param annotation
     * @param pageNumber
     * @param annotation
     * @param pageNumber
     * @private
     */
    updateStickyNotesAnnotationCollections(annotation: any, pageNumber: number): any;
    /**
     * @private
     */
    clear(): void;
    /**
     * @private
     */
    getModuleName(): string;
    /**
     * This method used to add annotations with using program.
     *
     * @param annotationObject - It describes type of annotation object
     * @param offset - It describes about the annotation bounds or location
     * @returns Object
     * @private
     */
    updateAddAnnotationDetails(annotationObject: StickyNotesSettings, offset: IPoint): Object;
    /**
     * @private
    */
    private getAnnotationType;
    /**
     * @private
    */
    private getAuthorName;
}
