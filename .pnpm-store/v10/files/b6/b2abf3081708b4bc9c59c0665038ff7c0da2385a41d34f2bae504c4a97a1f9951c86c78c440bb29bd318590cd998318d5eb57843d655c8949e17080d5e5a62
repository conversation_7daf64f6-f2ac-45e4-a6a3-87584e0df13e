﻿@include export-module('document-editor-container-layout') {
  .e-de-toolbar {
    height: 100%;
  }
  .e-documenteditorcontainer {
    display: block;
  }
  .e-de-toolbar .e-toolbar-items .e-toolbar-item .e-tbar-btn-text .e-de-text-wrap {
    height: 0;
    @if $skin-name == 'tailwind' {
      line-height: 1;
    }
    @else if $skin-name == 'bootstrap5' or $skin-name == 'FluentUI' {
      line-height: .5;
    }
    @else {
      line-height: 0;
    }
  }

  .e-de-ctnr-file-picker {
    left: -110em;
    position: fixed;
  }

  .e-de-ctnr-rtl {
    direction: rtl;
  }

  .e-de-ctnr-hglt-btn {
    border: .5px solid transparent;
    display: inline-block;
    height: 25px;
    margin: 3px;
    width: 25px;
  }

  .e-color-selected,
  .e-de-ctnr-hglt-btn:hover {
    border-color: $de-white-color;
    outline: $de-black-color .5px solid;
  }

  .e-hglt-no-color {
    height: 30px;
    padding-top: 1px;
    width: 157px;

    #{if(&, '&', '*')}:hover {
      background-color: $de-hover-bg;
      cursor: pointer;
    }
  }

  .e-de-ctnr-hglt-no-color {
    font-size: 12px;
    font-weight: normal;
    left: 40px;
    padding-top: 11px;
    position: absolute;
    top: 100px;
  }

  /* stylelint-disable */
  .e-de-scrollbar-hide::-webkit-scrollbar {
    width: 0;
  }

  .e-de-scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* stylelint-enable */
  @if $skin-name == 'FluentUI' {
    //Font color icons is smaller when comparing with other icons.
    .e-de-ctnr-group-btn .e-btn .e-btn-icon.e-icons.e-de-ctnr-fontcolor,
    .e-de-ctnr-group-btn .e-btn .e-btn-icon.e-icons.e-de-ctnr-change-case {
      font-size: 18px;
    }

    .e-bigger {
      .e-de-ctnr-group-btn .e-btn .e-btn-icon.e-icons.e-de-ctnr-fontcolor,
      .e-de-ctnr-group-btn .e-btn .e-btn-icon.e-icons.e-de-ctnr-change-case {
        font-size: 21px;
      }
    }
  }
}
