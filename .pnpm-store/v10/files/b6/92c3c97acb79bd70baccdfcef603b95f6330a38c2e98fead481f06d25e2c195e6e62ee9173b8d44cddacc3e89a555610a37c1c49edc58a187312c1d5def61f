import { PdfViewer, PdfViewerBase } from '../index';
/**
 * The `LinkAnnotation` module is used to handle link annotation actions of PDF viewer.
 *
 * @hidden
 */
export declare class LinkAnnotation {
    private pdfViewer;
    private pdfViewerBase;
    /**
     * @private
     */
    linkAnnotation: number[];
    /**
     * @private
     */
    linkPage: number[];
    /**
     * @private
     */
    annotationY: number[];
    /**
     * @param pdfViewer
     * @param viewerBase
     * @param pdfViewer
     * @param viewerBase
     * @private
     */
    constructor(pdfViewer: PdfViewer, viewerBase: PdfViewerBase);
    /**
     * @param data
     * @param pageIndex
     * @param data
     * @param pageIndex
     * @private
     */
    renderHyperlinkContent(data: any, pageIndex: number): void;
    /**
     * @private
     */
    disableHyperlinkNavigationUnderObjects(eventTarget: HTMLElement, evt: MouseEvent | TouchEvent, element: any): void;
    private renderWebLink;
    private triggerHyperlinkEvent;
    private renderDocumentLink;
    private setHyperlinkProperties;
    /**
     * @param pageNumber
     * @param isAdd
     * @param pageNumber
     * @param isAdd
     * @private
     */
    modifyZindexForTextSelection(pageNumber: number, isAdd: boolean): void;
    /**
     * @param element
     * @param isAdd
     * @param element
     * @param isAdd
     * @private
     */
    modifyZindexForHyperlink(element: HTMLElement, isAdd: boolean): void;
    /**
     * @private
     */
    destroy(): void;
    /**
     * @private
     */
    getModuleName(): string;
}
