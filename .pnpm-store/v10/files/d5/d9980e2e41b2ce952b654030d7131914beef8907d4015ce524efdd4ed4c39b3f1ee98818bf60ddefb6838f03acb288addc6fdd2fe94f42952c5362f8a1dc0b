/**
 * Workbook all module.
 *
 * @private
 */
export declare class WorkbookAllModule {
    /**
     * Constructor for Workbook all module.
     *
     * @private
     */
    constructor();
    /**
     * For internal use only - Get the module name.
     *
     * @private
     * @returns {string} - Get the module name.
     */
    protected getModuleName(): string;
    /**
     * Destroys the Workbook all module.
     *
     * @returns {void} - Destroys the Workbook all module.
     */
    destroy(): void;
}
