var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
import { isNullOrUndefined } from '@syncfusion/ej2-base';
import { BaseHistoryInfo } from './base-history-info';
import { CommentCharacterElementBox } from '../viewer/page';
import { CONTROL_CHARACTERS } from '../../base/types';
/**
 * EditorHistory preservation class
 */
/**
 * @private
 */
var HistoryInfo = /** @class */ (function (_super) {
    __extends(HistoryInfo, _super);
    function HistoryInfo(node, isChild) {
        var _this = _super.call(this, node) || this;
        _this.isChildHistoryInfo = false;
        _this.editRangeStart = undefined;
        _this.documentHelper = node.documentHelper;
        _this.isChildHistoryInfo = isChild;
        return _this;
    }
    Object.defineProperty(HistoryInfo.prototype, "hasAction", {
        get: function () {
            return !isNullOrUndefined(this.modifiedActions);
        },
        enumerable: true,
        configurable: true
    });
    HistoryInfo.prototype.addModifiedAction = function (baseHistoryInfo) {
        // For complex actions such as Replace text, Insert/Remove Hyperlink etc.
        if (!(this.editorHistory.isUndoing || this.editorHistory.isRedoing)) {
            if (isNullOrUndefined(this.modifiedActions)) {
                this.modifiedActions = [];
            }
            this.modifiedActions.push(baseHistoryInfo);
        }
    };
    /**
     * @private
     */
    HistoryInfo.prototype.getActionInfo = function (isInvertOperation) {
        var action = this.action;
        var operations = [];
        switch (action) {
            case 'InsertBookmark':
            case 'RestrictEditing':
                for (var i_1 = 0; i_1 < this.modifiedActions.length; i_1++) {
                    var currentHistory_1 = this.modifiedActions[parseInt(i_1.toString(), 10)];
                    if (currentHistory_1.action === 'DeleteBookmark') {
                        operations.push(currentHistory_1.getDeleteOperation('DeleteBookmark'));
                        operations.push(currentHistory_1.getDeleteOperation('DeleteBookmark', true));
                        continue;
                    }
                    operations.push(currentHistory_1.getInsertOperation(action));
                }
                break;
            case 'BackSpace':
            case 'Delete':
            case 'RemoveEditRange':
                for (var i_2 = 0; i_2 < this.modifiedActions.length; i_2++) {
                    var currentHistory_2 = this.modifiedActions[parseInt(i_2.toString(), 10)];
                    if (currentHistory_2.action === 'InsertInline') {
                        var operation = currentHistory_2.getInsertOperation('InsertBookmark');
                        operations.push(operation);
                    }
                    else {
                        operations.push(currentHistory_2.getDeleteOperation(action));
                        if (currentHistory_2.action === 'RemoveEditRange') {
                            operations.push(currentHistory_2.getDeleteOperation(action, true));
                        }
                    }
                }
                break;
            case 'PageBreak':
            case 'ColumnBreak':
                if (this.owner.enableTrackChanges) {
                    for (var i_3 = 0; i_3 < this.modifiedActions.length; i_3++) {
                        var currentHistory_3 = this.modifiedActions[parseInt(i_3.toString(), 10)];
                        if (currentHistory_3.removedNodes.length > 0) {
                            operations = operations.concat(currentHistory_3.getDeleteOperationsForTrackChanges());
                        }
                        var markerData = currentHistory_3.markerData[currentHistory_3.markerData.length - 1];
                        var operation_1 = currentHistory_3.getInsertOperation('Enter');
                        var breakOperation = this.getInsertOperation(action);
                        operation_1.markerData = markerData;
                        breakOperation.markerData = this.owner.editor.getMarkerData(undefined, undefined, this.owner.editor.getRevision(markerData.revisionId));
                        operations.push(operation_1);
                        operations.push(breakOperation);
                        operations.push(operation_1);
                        operation_1.markerData.skipOperation = true;
                    }
                }
                else {
                    for (var i_4 = 0; i_4 < this.modifiedActions.length; i_4++) {
                        var currentHistory_4 = this.modifiedActions[parseInt(i_4.toString(), 10)];
                        if (currentHistory_4.removedNodes.length > 0) {
                            operations.push(currentHistory_4.getDeleteOperation(action));
                        }
                    }
                    operations.push(this.getInsertOperation('Enter'));
                    operations.push(this.getInsertOperation(action));
                    operations.push(this.getInsertOperation('Enter'));
                }
                break;
            case 'InsertHyperlink':
            case 'AutoFormatHyperlink':
            case 'RemoveHyperlink':
                for (var i = 0; i < this.modifiedActions.length; i++) {
                    var currentHistory = this.modifiedActions[parseInt(i.toString(), 10)];
                    if (currentHistory.action === 'InsertInline') {
                        if (currentHistory.insertedText === CONTROL_CHARACTERS.Marker_Start || currentHistory.insertedText === CONTROL_CHARACTERS.Marker_End) {
                            operations.push(currentHistory.getInsertOperation('InsertHyperlink'));
                        }
                        else {
                            operations.push(currentHistory.getInsertOperation('Insert'));
                        }
                    }
                    else if (currentHistory.action === 'Delete') {
                        operations.push(currentHistory.getDeleteOperation(currentHistory.action));
                    }
                }
                break;
            case 'InsertComment':
                for (var i_5 = 0; i_5 < this.modifiedActions.length; i_5++) {
                    var currentHistory_5 = this.modifiedActions[parseInt(i_5.toString(), 10)];
                    var operation_2 = currentHistory_5.getInsertOperation(currentHistory_5.action);
                    if ((currentHistory_5.insertedElement instanceof CommentCharacterElementBox && currentHistory_5.action === 'InsertInline')) {
                        operations.push(currentHistory_5.getCommentOperation(operation_2));
                    }
                    else if (currentHistory_5.action === 'InsertCommentWidget') {
                        operation_2 = this.getUpdateOperation();
                        operations.push(currentHistory_5.getCommentOperation(operation_2));
                    }
                }
                break;
            case 'RemoveComment':
                for (var i_6 = 0; i_6 < this.modifiedActions.length; i_6++) {
                    var currentHistory_6 = this.modifiedActions[parseInt(i_6.toString(), 10)];
                    var operation_3 = undefined;
                    var operationCollection = [];
                    if (currentHistory_6.action === 'RemoveInline' && currentHistory_6.removedNodes[0] instanceof CommentCharacterElementBox) {
                        operation_3 = currentHistory_6.getDeleteOperation(currentHistory_6.action);
                        operationCollection.push(currentHistory_6.getCommentOperation(operation_3));
                    }
                    else if (currentHistory_6.action === 'InsertInline' && currentHistory_6.insertedElement instanceof CommentCharacterElementBox) {
                        operation_3 = currentHistory_6.getInsertOperation(currentHistory_6.action);
                        operationCollection.push(currentHistory_6.getCommentOperation(operation_3));
                    }
                    else {
                        operationCollection = currentHistory_6.getActionInfo();
                    }
                    operations = operations.concat(operationCollection);
                }
                break;
            case 'DeleteComment':
                this.getDeleteCommentOperation(this.modifiedActions, operations);
                break;
            case 'FormField':
                var currentHistory = this.modifiedActions.pop();
                operations = currentHistory.getFieldOperation();
                break;
            case 'IMEInput':
                if (isInvertOperation) {
                    if (this.modifiedActions[0].removedNodes.length > 0) {
                        var removeOperation = this.modifiedActions[0].getDeleteOperation('Delete');
                        removeOperation.length = removeOperation.text.length;
                        operations.push(removeOperation);
                    }
                    var insertOperation = this.modifiedActions[this.modifiedActions.length - 1].getInsertOperation('Insert');
                    insertOperation.length = insertOperation.text.length;
                    operations.push(insertOperation);
                    operations.reverse();
                    for (var i_7 = 0; i_7 < operations.length; i_7++) {
                        var operation_4 = operations[parseInt(i_7.toString(), 10)];
                        if (operation_4.action === 'Insert') {
                            operation_4.action = 'Delete';
                        }
                        else if (operation_4.action === 'Delete') {
                            operation_4.action = 'Insert';
                        }
                    }
                }
                else {
                    var currentHistory_7 = this.modifiedActions[this.modifiedActions.length - 1];
                    if (currentHistory_7.removedNodes.length > 0) {
                        operations.push(currentHistory_7.getDeleteOperation(action));
                    }
                    operations.push(currentHistory_7.getInsertOperation(this.action));
                }
                break;
            case 'Accept All':
            case 'ReplaceAll':
            case 'Reject All':
                var isSkip = false;
                for (var i_8 = 0; i_8 < this.modifiedActions.length; i_8++) {
                    var currentHistory_8 = this.modifiedActions[parseInt(i_8.toString(), 10)];
                    if (!isNullOrUndefined(currentHistory_8.cellOperation) && currentHistory_8.cellOperation.length > 0) {
                        operations.push(currentHistory_8.cellOperation[0]);
                        isSkip = true;
                    }
                    else {
                        var operationsCollection = currentHistory_8.getActionInfo();
                        operations.push.apply(operations, operationsCollection);
                    }
                }
                if (!isSkip && (action === 'Accept All' || action === 'Reject All')) {
                    operations.reverse();
                }
                break;
            case 'Paste':
                for (var i_9 = 0; i_9 < this.modifiedActions.length; i_9++) {
                    var currentHistory_9 = this.modifiedActions[parseInt(i_9.toString(), 10)];
                    currentHistory_9.type = this.type === 'PasteToc' ? this.type : 'Paste';
                    var pasteOperations = currentHistory_9.getActionInfo();
                    operations.push.apply(operations, pasteOperations);
                }
                break;
            case 'TOC':
                for (var i_10 = 0; i_10 < this.modifiedActions.length; i_10++) {
                    var currentHistory_10 = this.modifiedActions[parseInt(i_10.toString(), 10)];
                    currentHistory_10.type = currentHistory_10.action === 'Paste' ? 'PasteToc' : undefined;
                    var tocOperations = currentHistory_10.getActionInfo();
                    operations.push.apply(operations, tocOperations);
                }
                break;
            case 'DragAndDropContent':
                for (var i_11 = 0; i_11 < this.modifiedActions.length; i_11++) {
                    var currentHistory_11 = this.modifiedActions[parseInt(i_11.toString(), 10)];
                    currentHistory_11.type = currentHistory_11.action === 'Paste' ? 'Paste' : undefined;
                    var ddcOperations = currentHistory_11.getActionInfo();
                    operations.push.apply(operations, ddcOperations);
                }
                break;
            case 'ClearFormat':
                var clearHistory = this.modifiedActions[this.modifiedActions.length - 1];
                var formatOperation = clearHistory.buildFormatOperation('ClearFormat', true, false);
                operations = formatOperation.slice();
                break;
            case 'ApplyStyle':
                var styleHistory = void 0;
                var formatstyleOperation = [];
                if (this.modifiedActions[0] instanceof HistoryInfo) {
                    var historyInfo = this.modifiedActions[0];
                    styleHistory = historyInfo.modifiedActions[0];
                    formatstyleOperation = styleHistory.buildFormatOperation('ClearFormat', true, false);
                    operations = formatstyleOperation.slice();
                }
                if (!(this.modifiedActions[this.modifiedActions.length - 1] instanceof HistoryInfo)) {
                    formatstyleOperation = this.modifiedActions[this.modifiedActions.length - 1].buildFormatOperation(action, true, false);
                    for (var i_12 = 0; i_12 < formatstyleOperation.length; i_12++) {
                        operations.push(formatstyleOperation[parseInt(i_12.toString(), 10)]);
                    }
                }
                break;
            case 'TableMarginsSelection':
                this.modifiedActions[this.modifiedActions.length - 1].createTableFormat(this.modifiedActions[this.modifiedActions.length - 1].action);
                operations.push(this.modifiedActions[this.modifiedActions.length - 1].getFormatOperation());
                break;
            case 'BordersAndShading':
                if (this.modifiedActions[0].action === 'TableFormat') {
                    this.modifiedActions[0].createTableFormat('BordersAndShading');
                    operations.push(this.modifiedActions[0].getFormatOperation());
                }
                else {
                    this.modifiedActions[0].createCellFormat('BordersAndShading');
                    operations = this.modifiedActions[0].getSelectedCellOperation('BordersAndShading', undefined, true, true);
                }
                break;
            case 'AutoList':
                for (var i_13 = 0; i_13 < this.modifiedActions.length; i_13++) {
                    var currentHistory_12 = this.modifiedActions[parseInt(i_13.toString(), 10)];
                    var autoListOperations = currentHistory_12.getActionInfo();
                    operations.push.apply(operations, autoListOperations);
                }
                break;
            case 'TableProperties':
                for (var i_14 = 0; i_14 < this.modifiedActions.length; i_14++) {
                    var tablePropHistory = this.modifiedActions[parseInt(i_14.toString(), 10)];
                    if (tablePropHistory.action === 'TableFormat') {
                        tablePropHistory.createTableFormat(tablePropHistory.action);
                        operations.push(tablePropHistory.getFormatOperation());
                    }
                    else if (tablePropHistory.action === 'RowFormat') {
                        tablePropHistory.createRowFormat(tablePropHistory.action);
                        operations.push(tablePropHistory.getFormatOperation());
                    }
                    else if (tablePropHistory.action === 'CellFormat') {
                        tablePropHistory.createCellFormat(tablePropHistory.action);
                        var cellProp = tablePropHistory.getSelectedCellOperation(tablePropHistory.action);
                        for (var i_15 = 0; i_15 < cellProp.length; i_15++) {
                            operations.push(cellProp[parseInt(i_15.toString(), 10)]);
                        }
                    }
                }
                break;
            case 'CellMarginsSelection':
                this.modifiedActions[this.modifiedActions.length - 1].createCellFormat('CellOptions');
                operations = this.modifiedActions[this.modifiedActions.length - 1].getSelectedCellOperation('CellOptions').slice();
                break;
        }
        return operations;
    };
    HistoryInfo.prototype.revert = function () {
        this.editorHistory.currentHistoryInfo = this;
        if (this.action === 'BordersAndShading') {
            this.owner.editorModule.isBordersAndShadingDialog = true;
        }
        if (!isNullOrUndefined(this.modifiedActions)) {
            if (this.editorHistory.isUndoing) {
                var i = this.modifiedActions.length;
                while (i > 0) {
                    var baseHistoryInfo = this.modifiedActions[i - 1];
                    baseHistoryInfo.revert();
                    i = i - 1;
                }
            }
            else {
                var i = 0;
                while (i < this.modifiedActions.length) {
                    var baseHistoryInfo = this.modifiedActions[parseInt(i.toString(), 10)];
                    baseHistoryInfo.revert();
                    i = i + 1;
                }
            }
        }
        if (this.action === 'RestrictEditing') {
            var user = this.editRangeStart.user !== '' ? this.editRangeStart.user : this.editRangeStart.group;
            if (this.editorHistory.isUndoing) {
                var index = this.owner.documentHelper.editRanges.get(user).indexOf(this.editRangeStart);
                if (index !== -1) {
                    this.owner.documentHelper.editRanges.get(user).splice(index, 1);
                }
            }
            else {
                this.owner.editor.updateRangeCollection(this.editRangeStart, user);
            }
            this.owner.selection.updateEditRangeCollection();
        }
        if (!this.isChildHistoryInfo) {
            this.editorHistory.updateComplexHistory();
        }
        else {
            this.editorHistory.updateComplexHistoryInternal();
        }
    };
    HistoryInfo.prototype.destroy = function () {
        if (!isNullOrUndefined(this.modifiedActions)) {
            while (this.modifiedActions.length > 0) {
                var baseHistoryInfo = this.modifiedActions[this.modifiedActions.length - 1];
                baseHistoryInfo.destroy();
                this.modifiedActions.splice(this.modifiedActions.indexOf(baseHistoryInfo), 1);
            }
            this.modifiedActions = undefined;
        }
        _super.prototype.destroy.call(this);
    };
    return HistoryInfo;
}(BaseHistoryInfo));
export { HistoryInfo };
