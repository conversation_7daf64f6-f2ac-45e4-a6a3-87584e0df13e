@import 'ej2-base/styles/definition/bootstrap-dark.scss';
@import '../spreadsheet-ribbon/bootstrap-dark-definition.scss';
@import 'ej2-buttons/styles/button/bootstrap-dark-definition.scss';
@import 'ej2-buttons/styles/check-box/bootstrap-dark-definition.scss';
@import 'ej2-buttons/styles/radio-button/bootstrap-dark-definition.scss';
@import 'ej2-buttons/styles/switch/bootstrap-dark-definition.scss';
@import 'ej2-navigations/styles/toolbar/bootstrap-dark-definition.scss';
@import 'ej2-navigations/styles/tab/bootstrap-dark-definition.scss';
@import 'ej2-navigations/styles/context-menu/bootstrap-dark-definition.scss';
@import 'ej2-navigations/styles/menu/bootstrap-dark-definition.scss';
@import 'ej2-navigations/styles/treeview/bootstrap-dark-definition.scss';
@import 'ej2-grids/styles/excel-filter/bootstrap-dark-definition.scss';
@import 'ej2-calendars/styles/datepicker/bootstrap-dark-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/bootstrap-dark-definition.scss';
@import 'ej2-inputs/styles/color-picker/bootstrap-dark-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/bootstrap-dark-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/bootstrap-dark-definition.scss';
@import 'ej2-dropdowns/styles/list-box/bootstrap-dark-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/bootstrap-dark-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/bootstrap-dark-definition.scss';
@import 'bootstrap-dark-definition.scss';
@import 'icons/bootstrap-dark.scss';
@import 'all.scss';
