import { Spreadsheet } from '../index';
/**
 * The `Resize` module is used to handle the resizing functionalities in Spreadsheet.
 */
export declare class Resize {
    private parent;
    private trgtEle;
    private event;
    private isMouseMoved;
    /**
     * Constructor for resize module in Spreadsheet.
     *
     * @param {Spreadsheet} parent - Constructor for resize module in Spreadsheet.
     * @private
     */
    constructor(parent: Spreadsheet);
    private addEventListener;
    private autoFit;
    private wireEvents;
    private wireResizeCursorEvent;
    private unWireResizeCursorEvent;
    private unwireEvents;
    private removeEventListener;
    private mouseMoveHandler;
    private mouseDownHandler;
    private mouseUpHandler;
    private dblClickHandler;
    private setTarget;
    private getColPrevSibling;
    private getRowPrevSibling;
    private updateTarget;
    private setAutoFitHandler;
    private getWrapText;
    private setAutofit;
    private createResizeHandler;
    private resizeTooltip;
    private setColWidth;
    private showHideCopyIndicator;
    private showHiddenColumns;
    private setRowHeight;
    private resizeOn;
    private resizeStart;
    private updateCursor;
    private getFloatingElementWidth;
    /**
     * To destroy the resize module.
     *
     * @returns {void} - To destroy the resize module.
     */
    destroy(): void;
    /**
     * Get the module name.
     *
     * @returns {string} - Get the module name.
     */
    protected getModuleName(): string;
}
