/** @hidden */
export declare const internalZoomFactorChange: string;
/** @hidden */
export declare const contentChangeEvent: string;
/** @hidden */
export declare const documentChangeEvent: string;
/** @hidden */
export declare const selectionChangeEvent: string;
/** @hidden */
export declare const zoomFactorChangeEvent: string;
/** @hidden */
export declare const beforeFieldFillEvent: string;
/** @hidden */
export declare const afterFieldFillEvent: string;
/** @hidden */
export declare const afterFormFieldFillEvent: string;
/** @hidden */
export declare const beforeFormFieldFillEvent: string;
/** @hidden */
export declare const serviceFailureEvent: string;
/** @hidden */
export declare const viewChangeEvent: string;
/** @hidden */
export declare const customContextMenuSelectEvent: string;
/** @hidden */
export declare const customContextMenuBeforeOpenEvent: string;
/** @hidden */
export declare const contentControlEvent: string;
/** @hidden */
export declare const commentBeginEvent: string;
/** @hidden */
export declare const commentEndEvent: string;
/** @hidden */
export declare const beforeCommentActionEvent: string;
/** @hidden */
export declare const commentDeleteEvent: string;
/** @hidden */
export declare const revisionActionEvent: string;
/** @hidden */
export declare const beforePaneSwitchEvent: string;
/** @hidden */
export declare const requestNavigateEvent: string;
/** @hidden */
export declare const actionCompleteEvent: string;
/** @hidden */
export declare const trackChangeEvent: string;
/** @hidden */
export declare const searchResultsChangeEvent: string;
/** @hidden */
export declare const keyDownEvent: string;
/** @hidden */
export declare const toolbarClickEvent: string;
/** @hidden */
export declare const beforeFileOpenEvent: string;
/** @hidden */
export declare const internalviewChangeEvent: string;
/** @hidden */
export declare const beforeXmlHttpRequestSend: string;
/** @hidden */
export declare const protectionTypeChangeEvent: string;
/** @hidden */
export declare const internalDocumentEditorSettingsChange: string;
/** @hidden */
export declare const internalStyleCollectionChange: string;
/** @hidden */
export declare const defaultFont: string;
/** @hidden */
export declare const internalAutoResize: string;
/** @hidden */
export declare const beforeAutoResize: string;
/** @hidden */
export declare const trackChanges: string;
