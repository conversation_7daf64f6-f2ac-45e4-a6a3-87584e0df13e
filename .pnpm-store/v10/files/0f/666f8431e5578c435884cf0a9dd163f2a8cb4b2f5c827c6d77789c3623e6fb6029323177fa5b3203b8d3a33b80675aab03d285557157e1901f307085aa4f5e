@import 'ej2-base/styles/definition/fluent.scss';
@import '../spreadsheet-ribbon/fluent-definition.scss';
@import 'ej2-buttons/styles/button/fluent-definition.scss';
@import 'ej2-buttons/styles/check-box/fluent-definition.scss';
@import 'ej2-buttons/styles/radio-button/fluent-definition.scss';
@import 'ej2-buttons/styles/switch/fluent-definition.scss';
@import 'ej2-navigations/styles/toolbar/fluent-definition.scss';
@import 'ej2-navigations/styles/tab/fluent-definition.scss';
@import 'ej2-navigations/styles/context-menu/fluent-definition.scss';
@import 'ej2-navigations/styles/menu/fluent-definition.scss';
@import 'ej2-navigations/styles/treeview/fluent-definition.scss';
@import 'ej2-grids/styles/excel-filter/fluent-definition.scss';
@import 'ej2-calendars/styles/datepicker/fluent-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/fluent-definition.scss';
@import 'ej2-inputs/styles/color-picker/fluent-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/fluent-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/fluent-definition.scss';
@import 'ej2-dropdowns/styles/list-box/fluent-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/fluent-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/fluent-definition.scss';
@import 'fluent-definition.scss';
@import 'icons/fluent.scss';
@import 'all.scss';
