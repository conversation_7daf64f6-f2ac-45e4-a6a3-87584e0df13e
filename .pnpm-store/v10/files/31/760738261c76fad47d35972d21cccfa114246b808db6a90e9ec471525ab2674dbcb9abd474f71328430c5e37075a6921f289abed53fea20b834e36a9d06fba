/**
 * Workbook basic module.
 *
 * @private
 */
export declare class WorkbookBasicModule {
    /**
     * Constructor for Workbook basic module.
     *
     * @private
     */
    constructor();
    /**
     * For internal use only - Get the module name.
     *
     * @private
     * @returns {string} - Get the module name.
     */
    protected getModuleName(): string;
    /**
     * Destroys the Workbook basic module.
     *
     * @returns {void} - Destroys the Workbook basic module.
     */
    destroy(): void;
}
