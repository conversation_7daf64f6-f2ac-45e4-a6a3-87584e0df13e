// eslint-disable-next-line
export function PdfiumRunner() {
    var moduleString = "Module";
    var pageLoaded = false;
    var moduleLoaded = false;
    var FPDF = {};
    var pdfiumWindow = pdfiumWindow || {};
    var documentDetails;
    var PDFiumModule = typeof (pdfiumWindow["" + moduleString]) !== 'undefined' ? (pdfiumWindow["" + moduleString]) : {};
    var I8 = Int8Array;
    var I16 = Int16Array;
    var I32 = Int32Array;
    var U8 = Uint8Array;
    var CH = U8;
    var U16 = Uint16Array;
    var U32 = Uint32Array;
    var F32 = Float32Array;
    var F64 = Float64Array;
    var H = function (t, s, d) { return function (f) {
        var _a = pdfiumWindow.heap(t, s), m = _a[0], a = _a.slice(1);
        var v = f.apply(void 0, a.map(function (x) { return x.p; }));
        if (!v) {
            m.free();
            return d;
        }
        var r = a.map(function (x) { return x.v; });
        m.free();
        return r;
    }; };
    var F = FPDF.Bitmap_BGRA;
    var C = 4;
    Object.assign(FPDF, {
        LCD_TEXT: 0x02,
        NO_NATIVETEXT: 0x04,
        GRAYSCALE: 0x08,
        DEBUG_INFO: 0x80,
        NO_CATCH: 0x100,
        RENDER_LIMITEDIMAGECACHE: 0x200,
        RENDER_FORCEHALFTONE: 0x400,
        PRINTING: 0x800,
        REVERSE_BYTE_ORDER: 0x10,
        Bitmap_Gray: 1,
        Bitmap_BGR: 2,
        Bitmap_BGRx: 3,
        Bitmap_BGRA: 4,
        LAST_ERROR: {
            SUCCESS: 0,
            UNKNOWN: 1,
            FILE: 2,
            FORMAT: 3,
            PASSWORD: 4,
            SECURITY: 5,
            PAGE: 6
        }
    });
    function initializeFPDF() {
        FPDF.Init = PDFiumModule.cwrap('FPDF_InitLibrary');
        FPDF.RenderPageBitmap = PDFiumModule.cwrap('FPDF_RenderPageBitmap', '', ['number', 'number', 'number', 'number', 'number', 'number', 'number', 'number']);
        FPDF.Bitmap_FillRect = PDFiumModule.cwrap('FPDFBitmap_FillRect', '', ['number', 'number', 'number', 'number', 'number', 'number']);
        FPDF.Bitmap_CreateEx = PDFiumModule.cwrap('FPDFBitmap_CreateEx', 'number', ['number', 'number', 'number', 'number', 'number']);
        FPDF.Bitmap_Destroy = PDFiumModule.cwrap('FPDFBitmap_Destroy', '', ['number']);
        FPDF.LoadPage = PDFiumModule.cwrap('FPDF_LoadPage', 'number', ['number', 'number']);
        FPDF.ClosePage = PDFiumModule.cwrap('FPDF_ClosePage', '', ['number']);
        FPDF.LoadMemDocument = PDFiumModule.cwrap('FPDF_LoadMemDocument', 'number', ['number', 'number', 'string']);
        FPDF.GetPageSizeByIndex = PDFiumModule.cwrap('FPDF_GetPageSizeByIndex', 'number', ['number', 'number', 'number', 'number']);
        FPDF.GetLastError = PDFiumModule.cwrap('FPDF_GetLastError', 'number');
        FPDF.GetPageCount = PDFiumModule.cwrap('FPDF_GetPageCount', 'number', ['number']);
        FPDF.CloseDocument = PDFiumModule.cwrap('FPDF_CloseDocument', '', ['number']);
        FPDF.DestroyLibrary = PDFiumModule.cwrap('FPDF_DestroyLibrary');
        FPDF.LoadTextPage = PDFiumModule.cwrap('FPDFText_LoadPage', 'number', ['number']);
        FPDF.CloseTextPage = PDFiumModule.cwrap('FPDFText_ClosePage', '', ['number']);
        FPDF.TextCountChars = PDFiumModule.cwrap('FPDFText_CountChars', 'number', ['number']);
        FPDF.GetUnicodeChar = PDFiumModule.cwrap('FPDFText_GetUnicode', 'number', ['number']);
        FPDF.GetCharBox = PDFiumModule.cwrap('FPDFText_GetCharBox', 'number', ['number', 'number', 'number', 'number', 'number']);
        FPDF.GetPageRotation = PDFiumModule.cwrap('FPDFPage_GetRotation', 'number', ['number']);
        FPDF.GetCharAngle = PDFiumModule.cwrap('FPDFText_GetCharAngle', 'number', ['number']);
        pdfiumWindow.heap = function (J, s) {
            var E;
            switch (J) {
                case Int8Array:
                    E = PDFiumModule.HEAP8;
                    break;
                case Int16Array:
                    E = PDFiumModule.HEAP16;
                    break;
                case Int32Array:
                    E = PDFiumModule.HEAP32;
                    break;
                case Uint8Array:
                    E = PDFiumModule.HEAPU8;
                    break;
                case Uint16Array:
                    E = PDFiumModule.HEAPU16;
                    break;
                case Uint32Array:
                    E = PDFiumModule.HEAPU32;
                    break;
                case Float32Array:
                    E = PDFiumModule.HEAPF32;
                    break;
                case Float64Array:
                    E = PDFiumModule.HEAPF64;
                    break;
            }
            var Z = J.BYTES_PER_ELEMENT;
            var m = PDFiumModule.asm.malloc(s * Z);
            var a = Array(1 + s);
            a[0] = ({ s: s, J: J, Z: Z, E: E, m: m, free: function () { return PDFiumModule.asm.free(m); } });
            var _loop_1 = function (i) {
                a[i + 1] = ({ p: m + (i * Z), get v() { return E[m / Z + i]; } });
            };
            for (var i = 0; i < s; i++) {
                _loop_1(i);
            }
            return a;
        };
    }
    function checkIfEverythingWasLoaded() {
        pageLoaded = true;
        if (pageLoaded || moduleLoaded) {
            startApp();
        }
    }
    PDFiumModule.onRuntimeInitialized = function () {
        moduleLoaded = true;
        checkIfEverythingWasLoaded();
    };
    function startApp() {
        initializeFPDF();
        if (pdfiumWindow.loaded) {
            pdfiumWindow.loaded();
        }
    }
    pdfiumWindow.onload = function () {
        pageLoaded = true;
        checkIfEverythingWasLoaded();
    };
    pdfiumWindow.loaded = function () {
        ctx.postMessage({ message: 'loaded' });
    };
    var ctx = self;
    ctx.onmessage = function (event) {
        if (event.data.message === 'initialLoading') {
            importScripts(event.data.url + '/pdfium.js');
            PDFiumModule.url = event.data.url;
            PDFiumModule.onRuntimeInitialized = function () {
                moduleLoaded = true;
                checkIfEverythingWasLoaded();
            };
            this['PDFiumModule'](PDFiumModule);
        }
        else if (event.data.message === 'LoadPageCollection') {
            pdfiumWindow.fileByteArray = event.data.uploadedFile;
            var fileSize = pdfiumWindow.fileByteArray.length;
            FPDF.Init();
            var wasmBuffer = PDFiumModule.asm.malloc(fileSize);
            PDFiumModule.HEAPU8.set(pdfiumWindow.fileByteArray, wasmBuffer);
            documentDetails = new DocumentInfo({
                wasm: FPDF.LoadMemDocument(wasmBuffer, fileSize, event.data.password),
                wasmBuffer: wasmBuffer,
            });
            var pages = FPDF.GetPageCount(documentDetails.processor.wasmData.wasm);
            documentDetails.setPages(pages);
            documentDetails.createAllPages();
            ctx.postMessage({ message: 'PageLoaded', pageIndex: event.data.pageIndex, isZoomMode: event.data.isZoomMode });
        }
        if (documentDetails) {
            if (event.data.message === 'renderPage') {
                var firstPage = documentDetails.getPage(event.data.pageIndex);
                var ImageData_1 = event.data;
                var data = firstPage.render(null, ImageData_1.zoomFactor, ImageData_1.isTextNeed, null, null, ImageData_1.textDetailsId);
                ctx.postMessage(data);
            }
            else if (event.data.message === 'renderPageSearch') {
                var firstPage = documentDetails.getPage(event.data.pageIndex);
                var ImageData_2 = event.data;
                var data = firstPage.render(null, ImageData_2.zoomFactor, ImageData_2.isTextNeed, null, null, ImageData_2.textDetailsId);
                data.message = 'imageRenderedSearch';
                ctx.postMessage(data);
            }
            else if (event.data.message === 'extractText') {
                var firstPage = documentDetails.getPage(event.data.pageIndex);
                var ImageData_3 = event.data;
                var data = firstPage.render(null, ImageData_3.zoomFactor, ImageData_3.isTextNeed, null, null, ImageData_3.textDetailsId);
                data.message = 'textExtracted';
                ctx.postMessage(data);
            }
            else if (event.data.message === 'renderThumbnail') {
                var firstPage = documentDetails.getPage(event.data.pageIndex);
                var data = firstPage.render("thumbnail", null, false, null, null);
                ctx.postMessage(data);
            }
            else if (event.data.message === 'printImage') {
                var firstPage = documentDetails.getPage(event.data.pageIndex);
                var data = firstPage.render("print", null, false, event.data.printScaleFactor, event.data.printDevicePixelRatio);
                ctx.postMessage(data);
            }
            else if (event.data.message === 'extractImage') {
                var firstPage = documentDetails.getPage(event.data.pageIndex);
                var ImageData_4 = event.data;
                var data = firstPage.render(null, ImageData_4.zoomFactor, ImageData_4.isTextNeed, null, null, ImageData_4.textDetailsId);
                data.message = 'imageExtracted';
                ctx.postMessage(data);
            }
            else if (event.data.message === 'renderImageAsTile') {
                var values = event.data;
                var firstPage = documentDetails.getPage(event.data.pageIndex);
                var data = firstPage.renderTileImage(values.tileX, values.tileY, values.tileXCount, values.tileYCount, values.zoomFactor, event.data.isTextNeed, event.data.textDetailsId);
                ctx.postMessage(data);
            }
            else if (event.data.message === 'renderImageAsTileSearch') {
                var values = event.data;
                var firstPage = documentDetails.getPage(event.data.pageIndex);
                var data = firstPage.renderTileImage(values.tileX, values.tileY, values.tileXCount, values.tileYCount, values.zoomFactor, event.data.isTextNeed, event.data.textDetailsId);
                data.message = 'renderTileImageSearch';
                ctx.postMessage(data);
            }
            else if (event.data.message === 'unloadFPDF') {
                if (documentDetails) {
                    PDFiumModule.asm.free(documentDetails.processor.wasmData.wasmBuffer);
                    FPDF.CloseDocument(documentDetails.processor.wasmData.wasm);
                    FPDF.DestroyLibrary();
                }
            }
        }
    };
    var Page = /** @class */ (function () {
        function Page(index, processor) {
            this.index = index;
            this.src = null;
            this.processor = processor;
        }
        Page.prototype.render = function (message, zoomFactor, isTextNeed, printScaleFactor, printDevicePixelRatio, textDetailsId) {
            return this.processor.render(this.index, message, zoomFactor, isTextNeed, printScaleFactor, printDevicePixelRatio, textDetailsId);
        };
        Page.prototype.renderTileImage = function (x, y, tileX, tileY, zoomFactor, isTextNeed, textDetailsId) {
            return this.processor.renderTileImage(this.index, x, y, tileX, tileY, zoomFactor, isTextNeed, textDetailsId);
        };
        return Page;
    }());
    var RectAngle = /** @class */ (function () {
        function RectAngle(X, Y, Width, Height, Text, Rotation) {
            this.X = X;
            this.Y = Y;
            this.Width = Width;
            this.Height = Height;
            this.Bottom = this.Y + this.Height;
            this.Right = this.X + this.Width;
            this.Top = this.Y;
            this.Left = this.X;
            this.Rotation = Rotation;
            this.Text = Text;
        }
        return RectAngle;
    }());
    ;
    var Processor = /** @class */ (function () {
        function Processor(wasmData) {
            this.TextBounds = [];
            this.TextContent = [];
            this.CharacterBounds = [];
            this.PageText = "";
            this.wasmData = wasmData;
        }
        Processor.prototype.getPageSize = function (i) {
            var _this = this;
            if (i === void 0) { i = 0; }
            return H(F64, 2, [-1, -1])(function (w, h) { return FPDF.GetPageSizeByIndex(_this.wasmData.wasm, i, w, h); }).map(function (v) { return parseInt((v * (96 / 72)).toString()); });
        };
        Processor.prototype.getCharBounds = function (pagePointer, i) {
            if (i === void 0) { i = 0; }
            return H(F64, 4, [-1, -1, -1, -1])(function (left, right, bottom, top) { return FPDF.GetCharBox(pagePointer, i, left, right, bottom, top); });
        };
        Processor.prototype.getRender = function (i, w, h, isTextNeed) {
            if (i === void 0) { i = 0; }
            var flag = FPDF.REVERSE_BYTE_ORDER;
            var heap = PDFiumModule.asm.malloc(w * h * 4);
            PDFiumModule.HEAPU8.fill(0, heap, heap + (w * h * 4));
            var bmap = FPDF.Bitmap_CreateEx(w, h, FPDF.Bitmap_BGRA, heap, w * 4);
            var page = FPDF.LoadPage(this.wasmData.wasm, i);
            FPDF.Bitmap_FillRect(bmap, 0, 0, w, h, 0xFFFFFFFF);
            FPDF.RenderPageBitmap(bmap, page, 0, 0, w, h, 0, flag);
            FPDF.Bitmap_Destroy(bmap);
            this.textExtraction(page, i, isTextNeed);
            FPDF.ClosePage(page);
            return heap;
        };
        Processor.prototype.textExtraction = function (pagePointer, pageIndex, isTextNeed) {
            var _a;
            if (isTextNeed) {
                var _b = this.getPageSize(pageIndex), pageWidth = _b[0], pageHeight = _b[1];
                var textPage = FPDF.LoadTextPage(pagePointer, pageIndex);
                var pageRotation = FPDF.GetPageRotation(pagePointer);
                var totalCharacterCount = FPDF.TextCountChars(textPage);
                this.TextBounds = [];
                this.TextContent = [];
                this.CharacterBounds = [];
                var pageText = "";
                var minTop = 0;
                var maxBottom = 0;
                var minLeft = 0;
                var maxRight = 0;
                var top_1 = [];
                var bottom = [];
                var left = [];
                var right = [];
                var wordBounds = [];
                var word = "";
                var wordMinLeft = 0;
                var wordMaxRight = 0;
                var wordMinTop = 0;
                var wordMaxBottom = 0;
                var wordRotation = 0;
                var wordStart = true;
                var isZeroWidthSpace = false;
                var isPreviousSpace = false;
                var startNewLine = false;
                var maximumSpaceForNewLine = 11;
                for (var charCount = 0; charCount <= totalCharacterCount; charCount++) {
                    var result = FPDF.GetUnicodeChar(textPage, charCount);
                    var rotationRadian = FPDF.GetCharAngle(textPage, charCount);
                    var character = String.fromCharCode(result);
                    var _c = this.getCharBounds(textPage, charCount), charLeft = _c[0], charRight = _c[1], charBottom = _c[2], charTop = _c[3];
                    var X = this.pointerToPixelConverter(charLeft);
                    var Y = (pageHeight) - this.pointerToPixelConverter(charTop);
                    var Width = this.pointerToPixelConverter(charRight - charLeft);
                    var Height = this.pointerToPixelConverter(charTop - charBottom);
                    var rotationAngle = parseInt((rotationRadian * 180 / Math.PI).toString());
                    if (charCount < totalCharacterCount) {
                        pageText += character;
                        var currentCharacterBounds = new RectAngle(X, Y, Width, Height, character, rotationAngle);
                        this.CharacterBounds.push(currentCharacterBounds);
                    }
                    if (pageRotation == 1 || pageRotation == 3) {
                        Y = (pageWidth) - this.pointerToPixelConverter(charTop);
                    }
                    switch (character) {
                        case "\0":
                            minTop = Math.min.apply(Math, top_1);
                            maxBottom = Math.max.apply(Math, bottom);
                            minLeft = Math.min.apply(Math, left);
                            maxRight = Math.max.apply(Math, right);
                            var newWordBounds = new RectAngle(wordMinLeft, wordMinTop, wordMaxRight - wordMinLeft, wordMaxBottom - wordMinTop, word, wordRotation);
                            wordBounds.push(newWordBounds);
                            this.textBoundsCalculation(wordBounds, minTop, maxBottom, maxRight, minLeft, pageRotation, pageWidth, pageHeight);
                            wordBounds = [];
                            wordStart = true;
                            isPreviousSpace = false;
                            word = "";
                            top_1 = [];
                            left = [];
                            bottom = [];
                            right = [];
                            minTop = 0;
                            maxBottom = 0;
                            minLeft = 0;
                            maxRight = 0;
                            break;
                        case "\r":
                            if (charCount < totalCharacterCount) {
                                var characterBounds = new RectAngle(X, Y, Width, Height, "\r\n", rotationAngle);
                                top_1.push(characterBounds.Top);
                                bottom.push(characterBounds.Bottom);
                                left.push(characterBounds.Left);
                                right.push(characterBounds.Right);
                                minTop = Math.min.apply(Math, top_1);
                                maxBottom = Math.max.apply(Math, bottom);
                                minLeft = Math.min.apply(Math, left);
                                maxRight = Math.max.apply(Math, right);
                                var newWordBounds_1 = void 0;
                                if (wordStart == false) {
                                    newWordBounds_1 = new RectAngle(wordMinLeft, wordMinTop, wordMaxRight - wordMinLeft, wordMaxBottom - wordMinTop, word, wordRotation);
                                    wordBounds.push(newWordBounds_1);
                                }
                                wordBounds.push(characterBounds);
                                this.textBoundsCalculation(wordBounds, minTop, maxBottom, maxRight, minLeft, pageRotation, pageWidth, pageHeight);
                                wordBounds = [];
                                wordStart = true;
                                isPreviousSpace = false;
                                word = "";
                                top_1 = [];
                                left = [];
                                bottom = [];
                                right = [];
                                minTop = 0;
                                maxBottom = 0;
                                minLeft = 0;
                                maxRight = 0;
                                pageText += '\n';
                                rotationRadian = FPDF.GetCharAngle(textPage, charCount);
                                _a = this.getCharBounds(textPage, charCount), charLeft = _a[0], charRight = _a[1], charBottom = _a[2], charTop = _a[3];
                                X = this.pointerToPixelConverter(charLeft);
                                Y = (pageHeight) - this.pointerToPixelConverter(charTop);
                                Width = this.pointerToPixelConverter(charRight - charLeft);
                                Height = this.pointerToPixelConverter(charTop - charBottom);
                                rotationAngle = parseInt((rotationRadian * 180 / Math.PI).toString());
                                var currentCharacterBounds = new RectAngle(X, Y, Width, Height, character, rotationAngle);
                                this.CharacterBounds.push(currentCharacterBounds);
                                charCount++;
                            }
                            break;
                        case "\u0002":
                        case "\ufffe":
                            {
                                var characterBounds = new RectAngle(X, Y, Width, Height, character, rotationAngle);
                                top_1.push(characterBounds.Top);
                                bottom.push(characterBounds.Bottom);
                                left.push(characterBounds.Left);
                                right.push(characterBounds.Right);
                                minTop = Math.min.apply(Math, top_1);
                                maxBottom = Math.max.apply(Math, bottom);
                                minLeft = Math.min.apply(Math, left);
                                maxRight = Math.max.apply(Math, right);
                                var newWordBounds_2 = void 0;
                                if (wordStart == false) {
                                    newWordBounds_2 = new RectAngle(wordMinLeft, wordMinTop, wordMaxRight - wordMinLeft, wordMaxBottom - wordMinTop, word, wordRotation);
                                    wordBounds.push(newWordBounds_2);
                                }
                                if (character == "\u0002") {
                                    wordBounds.push(characterBounds);
                                }
                                this.textBoundsCalculation(wordBounds, minTop, maxBottom, maxRight, minLeft, pageRotation, pageWidth, pageHeight);
                                wordBounds = [];
                                wordStart = true;
                                isPreviousSpace = false;
                                word = "";
                                top_1 = [];
                                left = [];
                                bottom = [];
                                right = [];
                                minTop = 0;
                                maxBottom = 0;
                                minLeft = 0;
                                maxRight = 0;
                            }
                            break;
                        default:
                            if (Width == 0 || Height == 0) {
                                isZeroWidthSpace = true;
                                minTop = Math.min.apply(Math, top_1);
                                maxBottom = Math.max.apply(Math, bottom);
                                minLeft = Math.min.apply(Math, left);
                                maxRight = Math.max.apply(Math, right);
                                var newWordBounds_3 = new RectAngle(wordMinLeft, wordMinTop, wordMaxRight - wordMinLeft, wordMaxBottom - wordMinTop, word, wordRotation);
                                wordBounds.push(newWordBounds_3);
                                var characterBounds = new RectAngle(X, Y, Width, Height, character, rotationAngle);
                                wordMinTop = characterBounds.Top;
                                wordMaxBottom = characterBounds.Bottom;
                                wordMinLeft = characterBounds.Left;
                                wordMaxRight = characterBounds.Right;
                                word = character;
                                wordRotation = wordBounds[wordBounds.length - 1].Rotation;
                                newWordBounds_3 = new RectAngle(wordMinLeft, wordMinTop, wordMaxRight - wordMinLeft, wordMaxBottom - wordMinTop, word, wordRotation);
                                wordBounds.push(newWordBounds_3);
                                wordMinTop = 0;
                                wordMaxBottom = 0;
                                wordMinLeft = 0;
                                wordMaxRight = 0;
                                word = "";
                                wordRotation = 0;
                                wordStart = true;
                                isPreviousSpace = true;
                            }
                            else {
                                if (wordStart == true) {
                                    wordMinTop = Y;
                                    wordMaxBottom = Y + Height;
                                    wordMinLeft = X;
                                    wordMaxRight = X + Width;
                                }
                                var characterBounds = new RectAngle(X, Y, Width, Height, character, rotationAngle);
                                if (character != " ") {
                                    if (isPreviousSpace && wordBounds.length > 0 && (rotationAngle == wordBounds[0].Rotation)) {
                                        if ((rotationAngle == 180 || rotationAngle == 0) && (Math.abs(characterBounds.Y - wordBounds[0].Y) > maximumSpaceForNewLine)) {
                                            startNewLine = true;
                                        }
                                        if ((rotationAngle == 270 || rotationAngle == 90) && (Math.abs(characterBounds.X - wordBounds[0].X) > maximumSpaceForNewLine)) {
                                            startNewLine = true;
                                        }
                                    }
                                    if ((isZeroWidthSpace && wordBounds.length >= 1 && wordBounds[wordBounds.length - 1].Rotation != characterBounds.Rotation) || startNewLine) {
                                        isZeroWidthSpace = false;
                                        startNewLine = false;
                                        minTop = Math.min.apply(Math, top_1);
                                        maxBottom = Math.max.apply(Math, bottom);
                                        minLeft = Math.min.apply(Math, left);
                                        maxRight = Math.max.apply(Math, right);
                                        var newWordBounds_4 = void 0;
                                        if (wordStart == false) {
                                            newWordBounds_4 = new RectAngle(wordMinLeft, wordMinTop, wordMaxRight - wordMinLeft, wordMaxBottom - wordMinTop, word, wordRotation);
                                            wordBounds.push(newWordBounds_4);
                                        }
                                        this.textBoundsCalculation(wordBounds, minTop, maxBottom, maxRight, minLeft, pageRotation, pageWidth, pageHeight);
                                        wordBounds = [];
                                        wordStart = true;
                                        word = "";
                                        top_1 = [];
                                        left = [];
                                        bottom = [];
                                        right = [];
                                        minTop = 0;
                                        maxBottom = 0;
                                        minLeft = 0;
                                        maxRight = 0;
                                    }
                                    top_1.push(characterBounds.Top);
                                    bottom.push(characterBounds.Bottom);
                                    left.push(characterBounds.Left);
                                    right.push(characterBounds.Right);
                                    wordMinTop = Math.min(wordMinTop, characterBounds.Top);
                                    wordMaxBottom = Math.max(wordMaxBottom, characterBounds.Bottom);
                                    wordMinLeft = Math.min(wordMinLeft, characterBounds.Left);
                                    wordMaxRight = Math.max(wordMaxRight, characterBounds.Right);
                                    word += character;
                                    wordRotation = characterBounds.Rotation;
                                    wordStart = false;
                                    isPreviousSpace = false;
                                }
                                else {
                                    var newWordBounds_5 = new RectAngle(wordMinLeft, wordMinTop, wordMaxRight - wordMinLeft, wordMaxBottom - wordMinTop, word, wordRotation);
                                    wordBounds.push(newWordBounds_5);
                                    wordMinTop = characterBounds.Top;
                                    wordMaxBottom = characterBounds.Bottom;
                                    wordMinLeft = characterBounds.Left;
                                    wordMaxRight = characterBounds.Right;
                                    word = character;
                                    wordRotation = characterBounds.Rotation;
                                    newWordBounds_5 = new RectAngle(wordMinLeft, wordMinTop, wordMaxRight - wordMinLeft, wordMaxBottom - wordMinTop, word, wordRotation);
                                    wordBounds.push(newWordBounds_5);
                                    wordMinTop = 0;
                                    wordMaxBottom = 0;
                                    wordMinLeft = 0;
                                    wordMaxRight = 0;
                                    word = "";
                                    wordRotation = 0;
                                    wordStart = true;
                                    isPreviousSpace = true;
                                }
                            }
                            break;
                    }
                }
                FPDF.CloseTextPage(textPage);
                this.Rotation = pageRotation;
                this.PageText = pageText;
            }
        };
        Processor.prototype.pointerToPixelConverter = function (pointerValue) {
            return (pointerValue * (96 / 72));
        };
        Processor.prototype.textBoundsCalculation = function (wordBounds, minTop, maxBottom, maxRight, minLeft, pageRotation, pageWidth, pageHeight) {
            var newWordBounds;
            var hasInBetweenRotation = false;
            var inBetweenRotatedText = "";
            var maximumSpaceBetweenWords = 30;
            var sentence = wordBounds.reduce(function (word, rect) { return word + rect.Text; }, '');
            var isRTLText = this.checkIsRtlText(sentence);
            for (var count = 0; count < wordBounds.length; count++) {
                var textRotation = wordBounds[count].Rotation;
                if (textRotation == 0 || textRotation == 180) {
                    if (hasInBetweenRotation) {
                        this.TextBounds.push(newWordBounds);
                        this.TextContent.push(inBetweenRotatedText);
                        inBetweenRotatedText = "";
                    }
                    hasInBetweenRotation = false;
                    if (pageRotation == 0) {
                        newWordBounds = new RectAngle(wordBounds[count].Left, minTop, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                    }
                    else if (pageRotation == 1) {
                        newWordBounds = new RectAngle(pageWidth - minTop, wordBounds[count].Left, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                    }
                    else if (pageRotation == 2) {
                        newWordBounds = new RectAngle(pageWidth - wordBounds[count].Left, pageHeight - minTop, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                    }
                    else if (pageRotation == 3) {
                        newWordBounds = new RectAngle(minTop, pageHeight - wordBounds[count].Left, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                    }
                }
                else if (textRotation == 90 || textRotation == 270) {
                    if (hasInBetweenRotation) {
                        this.TextBounds.push(newWordBounds);
                        this.TextContent.push(inBetweenRotatedText);
                        inBetweenRotatedText = "";
                    }
                    hasInBetweenRotation = false;
                    if (pageRotation == 0) {
                        newWordBounds = new RectAngle(minLeft, wordBounds[count].Top, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                    }
                    else if (pageRotation == 1) {
                        newWordBounds = new RectAngle(pageWidth - wordBounds[count].Top, minLeft, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                    }
                    else if (pageRotation == 2) {
                        newWordBounds = new RectAngle(pageWidth - minLeft, pageHeight - wordBounds[count].Top, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                    }
                    else if (pageRotation == 3) {
                        newWordBounds = new RectAngle(wordBounds[count].Top, pageHeight - minLeft, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                    }
                }
                else if (!hasInBetweenRotation) {
                    hasInBetweenRotation = true;
                    inBetweenRotatedText += wordBounds[count].Text;
                    if (pageRotation == 0) {
                        newWordBounds = new RectAngle(wordBounds[count].Left, minTop, maxRight - minLeft, maxBottom - minTop, wordBounds[count].Text, textRotation);
                    }
                    else if (pageRotation == 1) {
                        newWordBounds = new RectAngle(pageWidth - minTop, wordBounds[count].Left, maxRight - minLeft, maxBottom - minTop, wordBounds[count].Text, textRotation);
                    }
                    else if (pageRotation == 2) {
                        newWordBounds = new RectAngle(pageWidth - wordBounds[count].Left, pageHeight - minTop, maxRight - minLeft, maxBottom - minTop, wordBounds[count].Text, textRotation);
                    }
                    else if (pageRotation == 3) {
                        newWordBounds = new RectAngle(minTop, pageHeight - wordBounds[count].Left, maxRight - minLeft, maxBottom - minTop, wordBounds[count].Text, textRotation);
                    }
                }
                else {
                    inBetweenRotatedText += wordBounds[count].Text;
                }
                if (!hasInBetweenRotation && wordBounds[count].Text === " " && count !== 0 && count + 1 <= wordBounds.length) {
                    if (!isRTLText) {
                        if (count + 1 != wordBounds.length) {
                            var spaceWidth = 0;
                            switch (textRotation) {
                                case 0:
                                    spaceWidth = wordBounds[count + 1].Left - (wordBounds[count - 1].Left + wordBounds[count - 1].Width);
                                    if (maximumSpaceBetweenWords < spaceWidth || spaceWidth < 0) {
                                        spaceWidth = 0;
                                    }
                                    if (pageRotation == 0) {
                                        newWordBounds = new RectAngle(wordBounds[count - 1].Left + wordBounds[count - 1].Width, minTop, spaceWidth, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 1) {
                                        newWordBounds = new RectAngle(pageWidth - minTop, wordBounds[count - 1].Left + wordBounds[count - 1].Width, spaceWidth, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 2) {
                                        newWordBounds = new RectAngle(pageWidth - (wordBounds[count - 1].Left + wordBounds[count - 1].Width), pageHeight - minTop, spaceWidth, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 3) {
                                        newWordBounds = new RectAngle(minTop, pageHeight - (wordBounds[count - 1].Left + wordBounds[count - 1].Width), spaceWidth, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    break;
                                case 90:
                                    spaceWidth = wordBounds[count + 1].Top - (wordBounds[count - 1].Top + wordBounds[count - 1].Height);
                                    if (maximumSpaceBetweenWords < spaceWidth || spaceWidth < 0) {
                                        spaceWidth = 0;
                                    }
                                    if (pageRotation == 0) {
                                        newWordBounds = new RectAngle(minLeft, wordBounds[count - 1].Top + wordBounds[count - 1].Height, maxRight - minLeft, spaceWidth, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 1) {
                                        newWordBounds = new RectAngle(pageWidth - (wordBounds[count - 1].Top + wordBounds[count - 1].Height), minLeft, maxRight - minLeft, spaceWidth, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 2) {
                                        newWordBounds = new RectAngle(pageWidth - minLeft, pageHeight - (wordBounds[count - 1].Top + wordBounds[count - 1].Height), maxRight - minLeft, spaceWidth, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 3) {
                                        newWordBounds = new RectAngle((wordBounds[count - 1].Top + wordBounds[count - 1].Height), pageHeight - minLeft, maxRight - minLeft, spaceWidth, wordBounds[count].Text, textRotation);
                                    }
                                    break;
                                case 180:
                                    spaceWidth = wordBounds[count - 1].Left - (wordBounds[count + 1].Left + wordBounds[count + 1].Width);
                                    if (maximumSpaceBetweenWords < spaceWidth || spaceWidth < 0) {
                                        spaceWidth = 0;
                                    }
                                    if (pageRotation == 0) {
                                        newWordBounds = new RectAngle(wordBounds[count + 1].Left + wordBounds[count + 1].Width, minTop, spaceWidth, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 1) {
                                        newWordBounds = new RectAngle(pageWidth - minTop, wordBounds[count + 1].Left + wordBounds[count + 1].Width, spaceWidth, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 2) {
                                        newWordBounds = new RectAngle(pageWidth - (wordBounds[count + 1].Left + wordBounds[count + 1].Width), pageHeight - minTop, spaceWidth, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 3) {
                                        newWordBounds = new RectAngle(minTop, pageHeight - (wordBounds[count + 1].Left + wordBounds[count + 1].Width), spaceWidth, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    break;
                                case 270:
                                    spaceWidth = wordBounds[count - 1].Top - (wordBounds[count + 1].Top + wordBounds[count + 1].Height);
                                    if (maximumSpaceBetweenWords < spaceWidth || spaceWidth < 0) {
                                        spaceWidth = 0;
                                    }
                                    if (pageRotation == 0) {
                                        newWordBounds = new RectAngle(minLeft, wordBounds[count + 1].Top + wordBounds[count + 1].Height, maxRight - minLeft, spaceWidth, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 1) {
                                        newWordBounds = new RectAngle(pageWidth - (wordBounds[count + 1].Top + wordBounds[count + 1].Height), minLeft, maxRight - minLeft, spaceWidth, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 2) {
                                        newWordBounds = new RectAngle(pageWidth - minLeft, pageHeight - (wordBounds[count + 1].Top + wordBounds[count + 1].Height), maxRight - minLeft, spaceWidth, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 3) {
                                        newWordBounds = new RectAngle((wordBounds[count + 1].Top + wordBounds[count + 1].Height), pageHeight - minLeft, maxRight - minLeft, spaceWidth, wordBounds[count].Text, textRotation);
                                    }
                                    break;
                            }
                        }
                        else {
                            switch (textRotation) {
                                case 90:
                                    if (pageRotation == 0) {
                                        newWordBounds = new RectAngle(minLeft, wordBounds[count - 1].Top + wordBounds[count - 1].Height, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 1) {
                                        newWordBounds = new RectAngle(pageWidth - (wordBounds[count - 1].Top + wordBounds[count - 1].Height), minLeft, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 2) {
                                        newWordBounds = new RectAngle(pageWidth - minLeft, pageHeight - (wordBounds[count - 1].Top + wordBounds[count - 1].Height), maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 3) {
                                        newWordBounds = new RectAngle((wordBounds[count - 1].Top + wordBounds[count - 1].Height), pageHeight - minLeft, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                                    }
                                    break;
                                case 270:
                                    if (pageRotation == 0) {
                                        newWordBounds = new RectAngle(minLeft, wordBounds[count - 1].Top - wordBounds[count].Height, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 1) {
                                        newWordBounds = new RectAngle(pageWidth - (wordBounds[count - 1].Top - wordBounds[count].Height), minLeft, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 2) {
                                        newWordBounds = new RectAngle(pageWidth - minLeft, pageHeight - wordBounds[count - 1].Top - wordBounds[count].Height, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 3) {
                                        newWordBounds = new RectAngle((wordBounds[count - 1].Top - wordBounds[count].Height), pageHeight - minLeft, maxRight - minLeft, wordBounds[count].Height, wordBounds[count].Text, textRotation);
                                    }
                                    break;
                                case 180:
                                    if (pageRotation == 0) {
                                        newWordBounds = new RectAngle(wordBounds[count - 1].Left - wordBounds[count].Width, minTop, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 1) {
                                        newWordBounds = new RectAngle((pageWidth - minTop), wordBounds[count - 1].Left - wordBounds[count].Width, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 2) {
                                        newWordBounds = new RectAngle(pageWidth - (wordBounds[count - 1].Left - wordBounds[count].Width), pageHeight - minTop, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 3) {
                                        newWordBounds = new RectAngle(minTop, pageHeight - (wordBounds[count - 1].Left - wordBounds[count].Width), wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    break;
                                case 0:
                                    if (pageRotation == 0) {
                                        newWordBounds = new RectAngle(wordBounds[count - 1].Left + wordBounds[count - 1].Width, minTop, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 1) {
                                        newWordBounds = new RectAngle(pageWidth - minTop, wordBounds[count - 1].Left + wordBounds[count - 1].Width, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 2) {
                                        newWordBounds = new RectAngle(pageWidth - (wordBounds[count - 1].Left + wordBounds[count - 1].Width), pageHeight - minTop, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    else if (pageRotation == 3) {
                                        newWordBounds = new RectAngle(minTop, pageHeight - (wordBounds[count - 1].Left + wordBounds[count - 1].Width), wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, textRotation);
                                    }
                                    break;
                            }
                        }
                    }
                    else if (isRTLText && count + 1 != wordBounds.length) {
                        var spaceWidth = (wordBounds[count - 1].Left - (wordBounds[count + 1].Left + wordBounds[count + 1].Width));
                        if (maximumSpaceBetweenWords < spaceWidth || spaceWidth < 0) {
                            spaceWidth = 0;
                        }
                        newWordBounds = new RectAngle((wordBounds[count + 1].Left + wordBounds[count + 1].Width), minTop, spaceWidth, maxBottom - minTop, wordBounds[count].Text, wordBounds[count].Rotation);
                    }
                    else if (isRTLText) {
                        newWordBounds = new RectAngle((wordBounds[count - 1].Left - wordBounds[count].Width), minTop, wordBounds[count].Width, maxBottom - minTop, wordBounds[count].Text, wordBounds[count].Rotation);
                    }
                }
                if (!hasInBetweenRotation) {
                    this.TextBounds.push(newWordBounds);
                    this.TextContent.push(wordBounds[count].Text);
                }
            }
            if (hasInBetweenRotation) {
                this.TextBounds.push(newWordBounds);
                this.TextContent.push(inBetweenRotatedText);
            }
        };
        Processor.prototype.checkIsRtlText = function (text) {
            // eslint-disable-next-line max-len
            var ltrChars = 'A-Za-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02B8\\u0300-\\u0590\\u0800-\\u1FFF' + '\\u2C00-\\uFB1C\\uFDFE-\\uFE6F\\uFEFD-\\uFFFF';
            var rtlChars = '\\u0591-\\u07FF\\uFB1D-\\uFDFD\\uFE70-\\uFEFC';
            // eslint-disable-next-line
            var rtlDirCheck = new RegExp('^[^' + ltrChars + ']*[' + rtlChars + ']');
            return rtlDirCheck.test(text);
        };
        Processor.prototype.getPageRender = function (n, w, h, isTextNeed) {
            if (n === void 0) { n = 0; }
            var pageRenderPtr = this.getRender(n, w, h, isTextNeed);
            var pageRenderData = [];
            pageRenderData = PDFiumModule.HEAPU8.slice(pageRenderPtr, pageRenderPtr + (w * h * 4));
            PDFiumModule.asm.free(pageRenderPtr);
            return pageRenderData;
        };
        Processor.prototype.render = function (n, message, zoomFactor, isTextNeed, printScaleFactor, printDevicePixelRatio, textDetailsId) {
            if (n === void 0) { n = 0; }
            var _a = this.getPageSize(n), w = _a[0], h = _a[1];
            var scaleFactor = 1.5;
            var thumbnailWidth = 99.7;
            var thumbnailHeight = 141;
            if (message === 'thumbnail') {
                var newWidth = Math.round(thumbnailWidth * scaleFactor);
                var newHeight = Math.round(thumbnailHeight * scaleFactor);
                var data = this.getPageRender(n, newWidth, newHeight, false);
                return { value: data, width: newWidth, height: newHeight, pageIndex: n, message: 'renderThumbnail' };
            }
            else if (message === 'print') {
                //An A0 piece of paper measures 33.1 × 46.8 inches, with 46.8 inches being the greater dimension. The pixel value of 46.8 inches is 4493px. If the document size is too large, we may not be able to display the image. Therefore, we should consider the maximum size of A0 paper if the page size is greater than 4493 pixels.
                var maxPageSize = 4493;
                var scaleFactor_1 = 1.5;
                var whichIsBigger = (w > h) ? 'Width' : 'Height';
                var maxWidth = w;
                var maxHeight = h;
                if (whichIsBigger === 'Width') {
                    maxWidth = (w > maxPageSize) ? maxPageSize : w;
                    if (maxWidth === maxPageSize) {
                        maxHeight = h / (w / maxPageSize);
                    }
                }
                else {
                    maxHeight = (h > maxPageSize) ? maxPageSize : h;
                    if (maxHeight === maxPageSize) {
                        maxWidth = w / (h / maxPageSize);
                    }
                }
                var newWidth = Math.round(maxWidth * printScaleFactor * scaleFactor_1);
                var newHeight = Math.round(maxHeight * printScaleFactor * scaleFactor_1);
                var data = this.getPageRender(n, newWidth, newHeight, false);
                return { value: data, width: newWidth, height: newHeight, pageIndex: n, pageWidth: w, pageHeight: h, message: 'printImage', printDevicePixelRatio: printDevicePixelRatio };
            }
            else {
                var newWidth = Math.round(w * scaleFactor * zoomFactor);
                var newHeight = Math.round(h * scaleFactor * zoomFactor);
                // Reduce the zoom factor if the new image size exceeds the memory limit
                while (((newWidth * newHeight * 4) * 2) >= 2147483648) {
                    zoomFactor = zoomFactor - 0.1;
                    newWidth = Math.round(this.pointerToPixelConverter(w) * zoomFactor);
                    newHeight = Math.round(this.pointerToPixelConverter(h) * zoomFactor);
                }
                var data = this.getPageRender(n, newWidth, newHeight, isTextNeed);
                return { value: data, width: newWidth, height: newHeight, pageWidth: w, pageHeight: h, pageIndex: n, message: 'imageRendered', textBounds: this.TextBounds, textContent: this.TextContent, rotation: this.Rotation, pageText: this.PageText, characterBounds: this.CharacterBounds, zoomFactor: zoomFactor, isTextNeed: isTextNeed, textDetailsId: textDetailsId };
            }
        };
        Processor.prototype.renderTileImage = function (n, tileX, tileY, xCount, yCount, zoomFactor, isTextNeed, textDetailsId) {
            if (n === void 0) { n = 0; }
            var _a = this.getPageSize(n), w = _a[0], h = _a[1];
            var newWidth = Math.round(w * 1.5 * zoomFactor);
            var newHeight = Math.round(h * 1.5 * zoomFactor);
            var w1 = Math.round(newWidth / xCount);
            var h1 = Math.round(newHeight / yCount);
            var flag = FPDF.REVERSE_BYTE_ORDER;
            var heap = PDFiumModule.asm.malloc(w1 * h1 * 4);
            PDFiumModule.HEAPU8.fill(0, heap, heap + (w1 * h1 * 4));
            var bmap = FPDF.Bitmap_CreateEx(w1, h1, 4, heap, w1 * 4);
            var page = FPDF.LoadPage(this.wasmData.wasm, n);
            FPDF.Bitmap_FillRect(bmap, 0, 0, w1, h1, 0xFFFFFFFF);
            FPDF.RenderPageBitmap(bmap, page, -tileX * w1, -tileY * h1, newWidth, newHeight, 0, flag);
            FPDF.Bitmap_Destroy(bmap);
            this.textExtraction(page, n, isTextNeed);
            FPDF.ClosePage(page);
            var pageRenderPtr = heap;
            var data = [];
            data = PDFiumModule.HEAPU8.slice(pageRenderPtr, pageRenderPtr + (w1 * h1 * 4));
            PDFiumModule.asm.free(pageRenderPtr);
            if (tileX === 0 && tileY === 0) {
                return {
                    value: data,
                    w: w1,
                    h: h1,
                    noTileX: xCount,
                    noTileY: yCount,
                    x: tileX,
                    y: tileY,
                    pageIndex: n,
                    message: 'renderTileImage',
                    textBounds: this.TextBounds,
                    textContent: this.TextContent,
                    rotation: this.Rotation,
                    pageText: this.PageText,
                    characterBounds: this.CharacterBounds,
                    textDetailsId: textDetailsId,
                    isTextNeed: isTextNeed
                };
            }
            else {
                return {
                    value: data,
                    w: w1,
                    h: h1,
                    noTileX: xCount,
                    noTileY: yCount,
                    x: tileX,
                    y: tileY,
                    pageIndex: n,
                    message: 'renderTileImage',
                    textDetailsId: textDetailsId,
                    isTextNeed: isTextNeed
                };
            }
        };
        ;
        Processor.prototype.getLastError = function () {
            var lastError = FPDF.GetLastError();
            switch (lastError) {
                case FPDF.LAST_ERROR.SUCCESS:
                    return "success";
                case FPDF.LAST_ERROR.UNKNOWN:
                    return "unknown error";
                case FPDF.LAST_ERROR.FILE:
                    return "file not found or could not be opened";
                case FPDF.LAST_ERROR.FORMAT:
                    return "file not in PDF format or corrupted";
                case FPDF.LAST_ERROR.PASSWORD:
                    return "password required or incorrect password";
                case FPDF.LAST_ERROR.SECURITY:
                    return "unsupported security scheme";
                case FPDF.LAST_ERROR.PAGE:
                    return "page not found or content error";
                default:
                    return "unknown error";
            }
        };
        return Processor;
    }());
    var DocumentInfo = /** @class */ (function () {
        function DocumentInfo(wasmData) {
            this.pages = [];
            this.processor = new Processor(wasmData);
        }
        DocumentInfo.prototype.setPages = function (pagesCount) {
            this.pages = Array(pagesCount).fill(null);
        };
        DocumentInfo.prototype.createAllPages = function () {
            for (var i = 0; i < this.pages.length; i++) {
                this.pages[parseInt(i.toString(), 10)] = new Page(parseInt(i.toString(), 10), this.processor);
            }
        };
        DocumentInfo.prototype.getPage = function (index) {
            // eslint-disable-next-line
            var page = this.pages[index];
            if (!page) {
                page = new Page(index);
                // eslint-disable-next-line
                this.pages[index] = page;
            }
            return page;
        };
        return DocumentInfo;
    }());
}
