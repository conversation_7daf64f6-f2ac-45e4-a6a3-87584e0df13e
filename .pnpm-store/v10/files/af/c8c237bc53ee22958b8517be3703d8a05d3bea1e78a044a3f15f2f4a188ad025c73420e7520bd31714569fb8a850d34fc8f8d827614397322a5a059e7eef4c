/* stylelint-disable property-no-vendor-prefix */
/* stylelint-disable-line no-empty-source */ /*! calendar tailwind theme variables */
/*! component icons */
.e-input-group-icon.e-range-icon,
*.e-control-wrapper .e-input-group-icon.e-range-icon {
  border-bottom-right-radius: 0;
  border-right: 0;
  border-top-right-radius: 0;
  font-size: 16px;
  margin: 0;
  min-height: 18px;
  min-width: 20px;
  outline: none;
}
.e-input-group-icon.e-range-icon::before,
*.e-control-wrapper .e-input-group-icon.e-range-icon::before {
  content: "\e74c";
  font-family: "e-icons";
}
.e-input-group-icon.e-range-icon:focus,
*.e-control-wrapper .e-input-group-icon.e-range-icon:focus {
  background: #f3f4f6;
  border-radius: 50%;
}

.e-daterangepicker .e-calendar .e-header .e-date-icon-next::before {
  content: "\e748";
}
.e-daterangepicker .e-calendar .e-header .e-date-icon-prev::before {
  content: "\e765";
}
.e-daterangepicker .e-change-icon::before {
  content: "\e7f9";
}
.e-daterangepicker .e-calendar.e-rtl .e-header .e-date-icon-next::before {
  content: "\e765";
}
.e-daterangepicker .e-calendar.e-rtl .e-header .e-date-icon-prev::before {
  content: "\e748";
}
.e-daterangepicker.e-rtl .e-start-end .e-change-icon::before {
  content: "\e773";
}

.e-bigger .e-input-group-icon.e-range-icon, .e-bigger.e-control-wrapper .e-input-group-icon.e-range-icon, .e-bigger .e-control-wrapper .e-input-group-icon.e-range-icon {
  font-size: 18px;
  margin: 0;
  min-height: 18px;
  min-width: 20px;
  outline: none;
}

.e-small .e-input-group-icon.e-range-icon,
*.e-control-wrapper.e-small .e-input-group-icon.e-range-icon,
*.e-small .e-control-wrapper .e-input-group-icon.e-range-icon {
  font-size: 14px;
}

.e-small.e-bigger .e-input-group-icon.e-range-icon,
*.e-control-wrapper.e-small.e-bigger .e-input-group-icon.e-range-icon,
*.e-small.e-bigger .e-control-wrapper .e-input-group-icon.e-range-icon {
  font-size: 18px;
}

.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup-expand .e-range-header .e-popup-close::before, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup-expand .e-model-header .e-popup-close::before {
  content: "\e7e7";
  font-family: "e-icons";
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup-expand .e-range-header .e-apply::before, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup-expand .e-model-header .e-apply::before {
  content: "\e774";
  font-family: "e-icons";
}

/*! daterangepicker layout */
.e-input-group.e-control-wrapper.e-date-range-wrapper.e-non-edit.e-input-focus .e-input:focus ~ .e-clear-icon,
.e-float-input.e-control-wrapper.e-input-group.e-date-range-wrapper.e-non-edit.e-input-focus input:focus ~ .e-clear-icon {
  display: -ms-flexbox;
  display: flex;
}

.e-float-input.e-input-group.e-control-wrapper.e-date-range-wrapper .e-daterange-hidden,
.e-input-group.e-control-wrapper.e-date-range-wrapper .e-daterange-hidden,
.e-float-input.e-control-wrapper.e-date-range-wrapper .e-daterange-hidden,
.e-float-input.e-input-group.e-control-wrapper.e-date-range-wrapper.e-input-focus .e-daterange-hidden,
.e-input-group.e-control-wrapper.e-date-range-wrapper.e-input-focus .e-daterange-hidden,
.e-float-input.e-control-wrapper.e-date-range-wrapper.e-input-focus .e-daterange-hidden {
  border: 0;
  height: 0;
  margin: 0;
  padding: 0;
  text-indent: 0;
  visibility: hidden;
  width: 0;
}

.e-daterangepicker.e-popup,
.e-bigger.e-small .e-daterangepicker.e-popup {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: 500px;
  max-width: 730px;
}
.e-daterangepicker.e-popup.e-daterange-day-header-lg,
.e-bigger.e-small .e-daterangepicker.e-popup.e-daterange-day-header-lg {
  max-width: 100%;
}
.e-daterangepicker.e-popup.e-preset-wrapper,
.e-bigger.e-small .e-daterangepicker.e-popup.e-preset-wrapper {
  min-width: 625px;
}
.e-daterangepicker.e-popup.e-preset-wrapper .e-presets,
.e-bigger.e-small .e-daterangepicker.e-popup.e-preset-wrapper .e-presets {
  max-height: none;
}
.e-daterangepicker.e-popup .e-range-header,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header {
  background: #f9fafb;
  padding: 8px 0;
  width: auto;
  border-radius: 6px 6px 0 0;
}
.e-daterangepicker.e-popup .e-range-header .e-start-label, .e-daterangepicker.e-popup .e-range-header .e-end-label,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-start-label,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-end-label {
  cursor: default;
  display: inline-block;
  font-size: 16px;
  overflow: hidden;
  text-align: center;
  text-decoration: none;
  text-overflow: ellipsis;
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
  white-space: nowrap;
  width: 48%;
}
.e-daterangepicker.e-popup .e-range-header .e-change-icon,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-change-icon {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  width: 4%;
}
.e-daterangepicker.e-popup .e-range-header .e-day-span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-day-span {
  direction: ltr;
  font-size: 12px;
  height: 14px;
  margin: 0;
  text-align: center;
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}
.e-daterangepicker.e-popup .e-range-header .e-start-end,
.e-bigger.e-small .e-daterangepicker.e-popup .e-range-header .e-start-end {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  height: 34px;
}
.e-daterangepicker.e-popup .e-separator,
.e-bigger.e-small .e-daterangepicker.e-popup .e-separator {
  height: 1px;
  margin: 0;
}
.e-daterangepicker.e-popup .e-calendar,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar {
  border: none;
  margin: 0;
  padding: 0 8px 8px;
}
.e-daterangepicker.e-popup .e-calendar .e-content table,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content table {
  padding: 0;
}
.e-daterangepicker.e-popup .e-calendar .e-header .e-title,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-title {
  cursor: pointer;
  line-height: 24px;
  width: auto;
  float: none;
  font-weight: 400;
  margin-left: 0;
}
.e-daterangepicker.e-popup .e-calendar .e-header.e-month, .e-daterangepicker.e-popup .e-calendar .e-header.e-year, .e-daterangepicker.e-popup .e-calendar .e-header.e-decade,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header.e-month,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header.e-year,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header.e-decade {
  padding: 8px 6px;
}
.e-daterangepicker.e-popup .e-calendar .e-header .e-next,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-next {
  float: right;
}
.e-daterangepicker.e-popup .e-calendar .e-header .e-prev,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-prev {
  float: left;
}
.e-daterangepicker.e-popup .e-calendar .e-header .e-next, .e-daterangepicker.e-popup .e-calendar .e-header .e-prev,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-next,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-prev {
  height: 24px;
  width: 24px;
}
.e-daterangepicker.e-popup .e-calendar .e-header .e-next span, .e-daterangepicker.e-popup .e-calendar .e-header .e-prev span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-next span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-prev span {
  padding: 0;
}
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover {
  border-radius: 4px 0 0 4px;
}
.e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover {
  border-radius: 0 4px 4px 0;
}
.e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day, .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
  border: none;
}
.e-daterangepicker.e-popup .e-footer,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer {
  -ms-flex-align: center;
      align-items: center;
  border-top: 1px solid #f9fafb;
  clear: both;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
  height: 47px;
  border-radius: 0 0 6px 6px;
}
.e-daterangepicker.e-popup .e-footer .e-btn,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer .e-btn {
  font-weight: 500;
  height: 30px;
  line-height: 24px;
  overflow: hidden;
  padding: 0 16px;
  text-overflow: ellipsis;
}
.e-daterangepicker.e-popup .e-footer .e-btn.e-apply,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer .e-btn.e-apply {
  margin: 0 8px 0 8px;
}
.e-daterangepicker.e-popup .e-date-range-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-date-range-container {
  float: left;
}
.e-daterangepicker.e-popup .e-date-range-container.e-range-border,
.e-bigger.e-small .e-daterangepicker.e-popup .e-date-range-container.e-range-border {
  border-right: 1px solid #f9fafb;
}
.e-daterangepicker.e-popup .e-calendar-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container {
  display: -ms-flexbox;
  display: flex;
}
.e-daterangepicker.e-popup .e-calendar-container .e-left-container, .e-daterangepicker.e-popup .e-calendar-container .e-right-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container .e-left-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container .e-right-container {
  float: left;
}
.e-daterangepicker.e-popup .e-calendar-container .e-left-container,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar-container .e-left-container {
  border-right: 1px solid #f9fafb;
  border-right: none;
}
.e-daterangepicker.e-popup .e-presets,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets {
  max-height: 192px;
  overflow: auto;
  width: auto;
}
.e-daterangepicker.e-popup .e-presets .e-list-item,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-item {
  border-radius: 0;
  cursor: pointer;
  line-height: 48px;
  overflow: hidden;
  padding: 0 24px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.e-daterangepicker.e-popup .e-presets .e-list-parent,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-parent {
  margin: 0;
  max-width: 160px;
  padding: 0;
}
.e-daterangepicker.e-popup .e-presets .e-text-content,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-text-content {
  line-height: 47px;
}
.e-daterangepicker.e-popup .e-presets .e-ul li.e-list-item,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-ul li.e-list-item {
  font-size: 12px;
  height: 36px;
  line-height: 36px;
}
.e-daterangepicker.e-popup .e-hide-range,
.e-bigger.e-small .e-daterangepicker.e-popup .e-hide-range {
  display: none;
}
.e-daterangepicker.e-rtl .e-date-range-container,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container {
  float: right;
}
.e-daterangepicker.e-rtl .e-date-range-container.e-range-border,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container.e-range-border {
  border-left: 1px solid #f9fafb;
  border-right: 0;
}
.e-daterangepicker.e-rtl .e-date-range-container .e-left-container,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-left-container {
  border-left: 1px solid #f9fafb;
  border-right: 0;
  border-left: none;
}
.e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-next,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-next {
  float: left;
}
.e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-prev,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-prev {
  float: right;
}
.e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-start-date.e-selected.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-start-date.e-selected.e-range-hover {
  border-radius: 0 4px 4px 0;
}
.e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-end-date.e-selected.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-date-range-container .e-calendar .e-end-date.e-selected.e-range-hover {
  border-radius: 4px 0 0 4px;
}
.e-daterangepicker.e-rtl .e-footer,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-footer {
  -ms-flex-direction: row;
      flex-direction: row;
  -ms-flex-pack: end;
      justify-content: flex-end;
}
.e-daterangepicker.e-rtl .e-footer .e-btn.e-cancel,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-footer .e-btn.e-cancel {
  margin: 0 8px 0 8px;
}
.e-daterangepicker.e-rtl .e-footer .e-btn.e-apply,
.e-bigger.e-small .e-daterangepicker.e-rtl .e-footer .e-btn.e-apply {
  margin-left: 0;
}

.e-bigger .e-daterangepicker.e-range-modal,
*.e-device.e-daterangepicker.e-range-modal {
  background-color: rgba(243, 244, 246, 0.6);
  height: 100%;
  left: 0;
  opacity: 0.5;
  pointer-events: auto;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}

.e-bigger.e-small .e-daterangepicker .e-calendar {
  max-width: 275px;
}

.e-bigger .e-daterangepicker.e-popup .e-start-label, .e-bigger .e-daterangepicker.e-popup .e-end-label,
*.e-bigger.e-daterangepicker.e-popup .e-start-label,
*.e-bigger.e-daterangepicker.e-popup .e-end-label,
*.e-device.e-daterangepicker.e-popup .e-start-label,
*.e-device.e-daterangepicker.e-popup .e-end-label {
  font-size: 18px;
}
.e-bigger .e-daterangepicker.e-popup .e-day-span,
*.e-bigger.e-daterangepicker.e-popup .e-day-span,
*.e-device.e-daterangepicker.e-popup .e-day-span {
  font-size: 14px;
}
.e-bigger .e-daterangepicker.e-popup.e-preset-wrapper,
*.e-bigger.e-daterangepicker.e-popup.e-preset-wrapper,
*.e-device.e-daterangepicker.e-popup.e-preset-wrapper {
  max-width: 770px;
  min-width: auto;
}
.e-bigger .e-daterangepicker.e-popup.e-preset-wrapper .e-presets,
*.e-bigger.e-daterangepicker.e-popup.e-preset-wrapper .e-presets,
*.e-device.e-daterangepicker.e-popup.e-preset-wrapper .e-presets {
  max-height: none;
}
.e-bigger .e-daterangepicker.e-popup .e-range-header,
*.e-bigger.e-daterangepicker.e-popup .e-range-header,
*.e-device.e-daterangepicker.e-popup .e-range-header {
  padding: 12px 0;
  width: auto;
}
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-change-icon,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-change-icon,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-change-icon {
  font-size: 20px;
}
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end {
  -ms-flex-align: center;
      align-items: center;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  height: 36px;
  -ms-flex-pack: center;
      justify-content: center;
  -webkit-user-select: none;
      -ms-user-select: none;
          user-select: none;
  width: 100%;
}
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn, .e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn {
  border: 1px solid #4f46e5;
  box-shadow: none;
  font-size: 14px;
  font-weight: 500;
  height: 38px;
  line-height: 36px;
  max-width: 116px;
  overflow: hidden;
  padding: 1px 6px;
  text-overflow: ellipsis;
  width: 50%;
}
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn {
  border-left: 0;
  border-radius: 0 2px 2px 0;
}
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn {
  border-radius: 2px 0 0 2px;
}
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn:hover, .e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn:hover:not([disabled]),
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn:hover,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn:hover:not([disabled]),
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn:hover,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn:hover:not([disabled]) {
  box-shadow: none;
}
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active, .e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:active, .e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active, .e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:active:not([disabled]), .e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:hover, .e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:hover,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:active,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:active:not([disabled]),
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:hover,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:hover,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:active,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:active:not([disabled]),
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn.e-active:hover,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn.e-active:hover {
  box-shadow: none;
}
.e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn, .e-bigger .e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
*.e-bigger.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-start-btn,
*.e-device.e-daterangepicker.e-popup .e-range-header .e-start-end .e-end-btn {
  max-width: 141px;
}
.e-bigger .e-daterangepicker.e-popup .e-presets,
*.e-bigger.e-daterangepicker.e-popup .e-presets,
*.e-device.e-daterangepicker.e-popup .e-presets {
  max-height: 240px;
}
.e-bigger .e-daterangepicker.e-popup .e-presets.e-preset-wrapper,
*.e-bigger.e-daterangepicker.e-popup .e-presets.e-preset-wrapper,
*.e-device.e-daterangepicker.e-popup .e-presets.e-preset-wrapper {
  max-height: none;
}
.e-bigger .e-daterangepicker.e-popup .e-presets ul,
*.e-bigger.e-daterangepicker.e-popup .e-presets ul,
*.e-device.e-daterangepicker.e-popup .e-presets ul {
  max-width: none;
}
.e-bigger .e-daterangepicker.e-popup .e-presets ul li.e-list-item,
*.e-bigger.e-daterangepicker.e-popup .e-presets ul li.e-list-item,
*.e-device.e-daterangepicker.e-popup .e-presets ul li.e-list-item {
  font-size: 14px;
  height: 48px;
  line-height: 48px;
  padding: 0 16px;
}
.e-bigger .e-daterangepicker .e-calendar,
*.e-bigger.e-daterangepicker .e-calendar,
*.e-device.e-daterangepicker .e-calendar {
  max-width: 296px;
  padding: 0 16px 12px;
}
.e-bigger .e-daterangepicker .e-calendar .e-content table,
*.e-bigger.e-daterangepicker .e-calendar .e-content table,
*.e-device.e-daterangepicker .e-calendar .e-content table {
  padding: 0;
}
.e-bigger .e-daterangepicker .e-calendar .e-header .e-next, .e-bigger .e-daterangepicker .e-calendar .e-header .e-prev,
*.e-bigger.e-daterangepicker .e-calendar .e-header .e-next,
*.e-bigger.e-daterangepicker .e-calendar .e-header .e-prev,
*.e-device.e-daterangepicker .e-calendar .e-header .e-next,
*.e-device.e-daterangepicker .e-calendar .e-header .e-prev {
  height: 28px;
  width: 28px;
}
.e-bigger .e-daterangepicker .e-calendar .e-header .e-next span, .e-bigger .e-daterangepicker .e-calendar .e-header .e-prev span,
*.e-bigger.e-daterangepicker .e-calendar .e-header .e-next span,
*.e-bigger.e-daterangepicker .e-calendar .e-header .e-prev span,
*.e-device.e-daterangepicker .e-calendar .e-header .e-next span,
*.e-device.e-daterangepicker .e-calendar .e-header .e-prev span {
  padding: 0;
}
.e-bigger .e-daterangepicker .e-calendar .e-header .e-title,
*.e-bigger.e-daterangepicker .e-calendar .e-header .e-title,
*.e-device.e-daterangepicker .e-calendar .e-header .e-title {
  cursor: pointer;
  line-height: 28px;
}
.e-bigger .e-daterangepicker .e-calendar .e-header.e-month, .e-bigger .e-daterangepicker .e-calendar .e-header.e-year, .e-bigger .e-daterangepicker .e-calendar .e-header.e-decade,
*.e-bigger.e-daterangepicker .e-calendar .e-header.e-month,
*.e-bigger.e-daterangepicker .e-calendar .e-header.e-year,
*.e-bigger.e-daterangepicker .e-calendar .e-header.e-decade,
*.e-device.e-daterangepicker .e-calendar .e-header.e-month,
*.e-device.e-daterangepicker .e-calendar .e-header.e-year,
*.e-device.e-daterangepicker .e-calendar .e-header.e-decade {
  padding: 8px 8px;
}
.e-bigger .e-daterangepicker .e-footer,
*.e-bigger.e-daterangepicker .e-footer,
*.e-device.e-daterangepicker .e-footer {
  height: 63px;
}
.e-bigger .e-daterangepicker .e-footer .e-btn,
*.e-bigger.e-daterangepicker .e-footer .e-btn,
*.e-device.e-daterangepicker .e-footer .e-btn {
  height: 36px;
  line-height: 32px;
  overflow: hidden;
}
.e-bigger .e-daterangepicker .e-footer .e-btn.e-apply,
*.e-bigger.e-daterangepicker .e-footer .e-btn.e-apply,
*.e-device.e-daterangepicker .e-footer .e-btn.e-apply {
  margin: 0 8px 0 8px;
}
.e-bigger .e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-end-btn,
*.e-bigger.e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-end-btn,
*.e-device.e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-end-btn {
  border: 1px solid #4f46e5;
  border-radius: 2px 0 0 2px;
  border-right: 0;
}
.e-bigger .e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-start-btn,
*.e-bigger.e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-start-btn,
*.e-device.e-daterangepicker.e-rtl.e-popup .e-range-header .e-start-end .e-start-btn {
  border-radius: 0 2px 2px 0;
}
.e-bigger .e-daterangepicker.e-rtl.e-popup .e-footer.e-btn.e-cancel,
*.e-bigger.e-daterangepicker.e-rtl.e-popup .e-footer.e-btn.e-cancel,
*.e-device.e-daterangepicker.e-rtl.e-popup .e-footer.e-btn.e-cancel {
  margin: 0 8px 0 8px;
}
.e-bigger .e-daterangepicker.e-rtl.e-popup .e-footer .e-btn.e-apply,
*.e-bigger.e-daterangepicker.e-rtl.e-popup .e-footer .e-btn.e-apply,
*.e-device.e-daterangepicker.e-rtl.e-popup .e-footer .e-btn.e-apply {
  margin-left: 0;
}
.e-bigger .e-daterangepicker.e-device.e-popup,
*.e-bigger.e-daterangepicker.e-device.e-popup,
*.e-device.e-daterangepicker.e-device.e-popup {
  max-width: 300px;
}
.e-bigger .e-daterangepicker.e-device.e-popup .e-range-header,
*.e-bigger.e-daterangepicker.e-device.e-popup .e-range-header,
*.e-device.e-daterangepicker.e-device.e-popup .e-range-header {
  margin: 0;
  padding: 12px 37px;
}
.e-bigger .e-daterangepicker.e-device.e-popup .e-range-header .e-day-span,
*.e-bigger.e-daterangepicker.e-device.e-popup .e-range-header .e-day-span,
*.e-device.e-daterangepicker.e-device.e-popup .e-range-header .e-day-span {
  height: 24px;
  line-height: 24px;
  margin: 8px 0 0;
}

.e-small .e-daterangepicker.e-popup .e-range-header,
*.e-small.e-daterangepicker.e-popup .e-range-header {
  padding: 12px 0;
}
.e-small .e-daterangepicker.e-popup .e-range-header .e-start-label, .e-small .e-daterangepicker.e-popup .e-range-header .e-end-label,
*.e-small.e-daterangepicker.e-popup .e-range-header .e-start-label,
*.e-small.e-daterangepicker.e-popup .e-range-header .e-end-label {
  font-size: 16px;
}
.e-small .e-daterangepicker.e-popup .e-range-header .e-change-icon,
*.e-small.e-daterangepicker.e-popup .e-range-header .e-change-icon {
  font-size: 12px;
}
.e-small .e-daterangepicker.e-popup .e-range-header .e-start-end,
*.e-small.e-daterangepicker.e-popup .e-range-header .e-start-end {
  height: 32px;
}
.e-small .e-daterangepicker.e-popup .e-range-header .e-day-span,
*.e-small.e-daterangepicker.e-popup .e-range-header .e-day-span {
  font-size: 12px;
  margin: 0 0 10px 0;
}
.e-small .e-daterangepicker.e-popup .e-range-header .e-separator,
*.e-small.e-daterangepicker.e-popup .e-range-header .e-separator {
  margin: 0 10px;
}
.e-small .e-daterangepicker.e-popup .e-footer .e-btn.e-apply,
*.e-small.e-daterangepicker.e-popup .e-footer .e-btn.e-apply {
  margin: 10px 10px 10px 8px;
}
.e-small .e-daterangepicker.e-popup.e-preset-wrapper .e-presets .e-list-parent.e-ul .e-list-item,
*.e-small.e-daterangepicker.e-popup.e-preset-wrapper .e-presets .e-list-parent.e-ul .e-list-item {
  font-size: 12px;
  height: 26px;
  line-height: 26px;
}

/* stylelint-disable */
.e-range-overflow {
  overflow: hidden;
}

.e-daterangepick-mob-popup-wrap {
  -ms-flex-align: center;
      align-items: center;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  height: 100%;
  -ms-flex-pack: center;
      justify-content: center;
  left: 0;
  max-height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1002;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-control.e-lib.e-device.e-popup-open {
  position: relative;
  top: 0 !important;
  left: 0 !important;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand.e-control.e-lib.e-device.e-popup-open {
  min-height: 100%;
  min-width: 100%;
  height: 100%;
  width: 100%;
}

.e-content-placeholder.e-daterangepicker.e-placeholder-daterangepicker {
  background-size: 250px 33px;
  min-height: 33px;
}

.e-bigger .e-content-placeholder.e-daterangepicker.e-placeholder-daterangepicker,
.e-bigger.e-content-placeholder.e-daterangepicker.e-placeholder-daterangepicker {
  background-size: 250px 40px;
  min-height: 40px;
}

.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container {
  min-height: 100%;
  min-width: 100%;
  height: 100%;
  width: 100%;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header {
  height: 20vh;
  padding: 2vh 2vw;
  margin: 0;
  color: #374151;
}
@media (max-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header .e-model-header-wrapper {
    font-size: 16px;
  }
}
@media (min-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header .e-model-header-wrapper {
    font-size: 18px;
  }
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header .e-model-header-wrapper .e-apply {
  float: right;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header .e-start-end {
  margin: 3vh 0 0 0;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar {
  min-width: 100%;
  width: 100%;
  padding: 0;
  overflow: unset;
}
@media (max-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar {
    min-height: 100%;
    height: 100%;
  }
}
@media (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar {
    min-height: 80vh;
    height: 80vh;
  }
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-month, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-year, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-decade {
  height: 10vh;
  border-style: solid;
  border-width: 0 0 1px 0;
  border-color: #e5e7eb;
  padding: 2vh 2vw;
  line-height: 5vh;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-month .e-prev, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-year .e-prev, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-decade .e-prev {
  height: 36px;
  width: 36px;
}
@media (min-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-month .e-prev span, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-year .e-prev span, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-decade .e-prev span {
    font-size: 18px;
    padding: 11px;
  }
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-month .e-next, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-year .e-next, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-decade .e-next {
  height: 36px;
  width: 36px;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-month .e-next span, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-year .e-next span, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-decade .e-next span {
  padding: 10px;
  line-height: 1;
}
@media (min-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-month .e-next span, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-year .e-next span, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-decade .e-next span {
    font-size: 18px;
  }
}
@media (max-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-month .e-title, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-year .e-title, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-decade .e-title {
    line-height: 8vh;
  }
}
@media (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-month .e-title, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-year .e-title, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-decade .e-title {
    line-height: inherit;
  }
}
@media (min-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-month .e-title, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-year .e-title, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-header.e-decade .e-title {
    font-size: 18px;
  }
}
@media (min-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar th {
    font-size: 18px;
    height: 48px;
  }
}
@media (min-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-content span.e-day {
    font-size: 18px;
    height: 64px;
    width: 64px;
    line-height: 64px;
  }
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-start-date.e-selected.e-range-hover, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-calendar .e-end-date.e-selected.e-range-hover {
  border-radius: 0;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-holder {
  height: 100%;
}
@media (max-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-holder .e-calendar-container {
    min-height: 78vh;
    height: 78vh;
  }
}
@media (max-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-holder .e-calendar-container .e-calendar {
    min-height: 78vh;
    height: 78vh;
  }
}
@media (max-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-container {
    min-height: 100%;
    height: 100%;
  }
}
@media (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-container {
    min-height: 80vh;
    height: 80vh;
  }
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-separator {
  margin: 0;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-month {
  height: 69vh;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-month table {
  padding: 0 2vw;
  height: 69vh;
  border-spacing: unset;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-footer {
  display: none;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-presets {
  max-height: 90%;
  height: 90%;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-presets ul {
  height: 100%;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-presets ul li.e-list-item {
  font-size: 16px;
}
@media (min-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-presets ul li.e-list-item {
    font-size: 18px;
  }
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-range-mob-popup-wrap {
  position: relative;
  height: 100%;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-range-mob-popup-wrap .e-model-header {
  height: 10%;
  padding: 2.5vh 2.5vw 2.5vh 1.5vw;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  font-size: 2vh;
  border-bottom: none;
}
@media (min-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-range-mob-popup-wrap .e-model-header {
    font-size: 18px;
  }
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-range-mob-popup-wrap .e-model-header .e-popup-close {
  float: left;
  padding: 1vh 2vw;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-range-mob-popup-wrap .e-model-header .e-model-title {
  padding: 1vh 2vw;
  text-transform: capitalize;
}

@media screen and (orientation: landscape) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header .e-model-header-wrapper .e-btn {
    padding: 0;
  }
}
@media screen and (orientation: landscape) and (min-device-width: 768px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header .e-model-header-wrapper .e-btn {
    font-size: 18px;
  }
}
@media screen and (orientation: landscape) and (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-holder .e-calendar-container {
    min-height: 70vh;
    height: 70vh;
  }
}
@media screen and (orientation: landscape) and (max-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-holder .e-calendar-container {
    min-height: 65%;
    height: 65%;
  }
}
@media screen and (orientation: landscape) and (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-holder .e-calendar-container .e-calendar {
    min-height: 70vh;
    height: 70vh;
  }
}
@media screen and (orientation: landscape) and (max-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-holder .e-calendar-container .e-content.e-month, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-holder .e-calendar-container .e-content.e-year, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-holder .e-calendar-container .e-content.e-decade {
    height: 50vh;
  }
}
@media screen and (orientation: landscape) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-presets {
    max-height: 85%;
    height: 85%;
  }
}
@media screen and (orientation: landscape) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-range-mob-popup-wrap .e-model-header {
    height: 15%;
    font-size: 18px;
  }
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-range-mob-popup-wrap .e-model-header .e-popup-close {
    padding: 1vh 1vw;
  }
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-range-mob-popup-wrap .e-model-header .e-model-title {
    padding: 1vh 1vw;
  }
}
@media screen and (orientation: landscape) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header {
    width: 100%;
  }
}
@media screen and (orientation: landscape) and (max-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header {
    height: 27vh;
  }
}
@media screen and (orientation: landscape) and (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header {
    height: 25vh;
  }
}
@media screen and (orientation: landscape) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header .e-start-end {
    margin: 0;
    height: 28px;
  }
}
@media screen and (orientation: landscape) and (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header .e-start-end {
    margin: 5vh 0 0 0;
  }
}
@media screen and (orientation: landscape) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-date-range-container .e-range-header .e-day-span {
    margin: 8px 0;
    font-size: 16px;
  }
}
@media screen and (orientation: landscape) and (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-calendar-container {
    min-height: 75vh;
    height: 75vh;
  }
}
@media screen and (orientation: landscape) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-month, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-year, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-decade {
    overflow-y: auto;
  }
}
@media screen and (orientation: landscape) and (max-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-month, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-year, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-decade {
    height: 60vh;
  }
}
@media screen and (orientation: landscape) and (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-month, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-year, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-decade {
    height: 65vh;
  }
}
@media screen and (orientation: landscape) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-month table, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-year table, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-decade table {
    display: table;
    border-spacing: unset;
  }
}
@media screen and (orientation: landscape) and (min-height: 600px) {
  .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-month table, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-year table, .e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-content.e-decade table {
    height: 65vh;
  }
}
.e-outline.e-float-input.e-control-wrapper label.e-float-text.e-label-bottom span.e-float-text-content.e-float-text-overflow.e-date-time-icon {
  width: calc(100% - 80px);
}

.e-outline.e-float-input.e-static-clear.e-control-wrapper label.e-float-text.e-label-bottom span.e-float-text-content.e-float-text-overflow.e-date-time-icon {
  width: calc(100% - 110px);
}

/* stylelint-enable */
/*! daterangepicker theme */
.e-date-range-wrapper .e-input-group-icon.e-icons.e-active {
  color: #111827;
}
.e-date-range-wrapper.e-input-group:not(.e-disabled) .e-input-group-icon.e-active:active {
  color: #111827;
}

.e-daterangepicker.e-popup,
.e-bigger.e-small .e-daterangepicker.e-popup,
.e-bigger.e-small.e-daterangepicker.e-popup {
  background: #fff;
}
.e-daterangepicker.e-popup .e-calendar,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar {
  background-color: #fff;
}
.e-daterangepicker.e-popup .e-calendar .e-header .e-title, .e-daterangepicker.e-popup .e-calendar .e-header .e-title:hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-title,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-header .e-title:hover,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-header .e-title,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-header .e-title:hover {
  color: #374151;
  text-decoration: none;
}
.e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover {
  background-color: #e5e7eb;
  color: #374151;
}
.e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover:not(.e-other-month) span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover:not(.e-other-month) span,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-content .e-range-hover:not(.e-other-month) span {
  background: #e5e7eb;
  border: none;
  color: #374151;
}
.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover, .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-today.e-range-hover span, .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover span, .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover:hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-today.e-range-hover span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover:hover span.e-day,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-today.e-range-hover span,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover span,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover:hover span.e-day {
  background-color: transparent;
  border: none;
  color: #6b7280;
}
.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-content .e-other-month.e-range-hover {
  background-color: transparent;
  border: none;
  color: #6b7280;
}
.e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day, .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover span.e-day,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover span.e-day {
  background-color: #4f46e5;
  color: #fff;
}
.e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover.e-other-month span.e-day, .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-other-month span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover.e-other-month span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-other-month span.e-day,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-end-date.e-selected.e-range-hover.e-other-month span.e-day,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-other-month span.e-day {
  background-color: #4f46e5;
  color: #fff;
}
.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-start-date.e-selected.e-range-hover.e-today span.e-day {
  background-color: #4f46e5;
  color: #fff;
}
.e-daterangepicker.e-popup .e-calendar .e-other-month.e-selected span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-calendar .e-other-month.e-selected span,
.e-bigger.e-small.e-daterangepicker.e-popup .e-calendar .e-other-month.e-selected span {
  color: #fff;
}
.e-daterangepicker.e-popup .e-presets,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets,
.e-bigger.e-small.e-daterangepicker.e-popup .e-presets {
  background-color: #fff;
  color: #111827;
}
.e-daterangepicker.e-popup .e-presets .e-list-item.e-active,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-item.e-active,
.e-bigger.e-small.e-daterangepicker.e-popup .e-presets .e-list-item.e-active {
  background-color: #e5e7eb;
  color: #111827;
}
.e-daterangepicker.e-popup .e-presets .e-list-item.e-hover,
.e-bigger.e-small .e-daterangepicker.e-popup .e-presets .e-list-item.e-hover,
.e-bigger.e-small.e-daterangepicker.e-popup .e-presets .e-list-item.e-hover {
  background-color: #f3f4f6;
  color: #111827;
}
.e-daterangepicker.e-popup .e-start-label, .e-daterangepicker.e-popup .e-end-label,
.e-bigger.e-small .e-daterangepicker.e-popup .e-start-label,
.e-bigger.e-small .e-daterangepicker.e-popup .e-end-label,
.e-bigger.e-small.e-daterangepicker.e-popup .e-start-label,
.e-bigger.e-small.e-daterangepicker.e-popup .e-end-label {
  color: #6b7280;
}
.e-daterangepicker.e-popup .e-change-icon,
.e-bigger.e-small .e-daterangepicker.e-popup .e-change-icon,
.e-bigger.e-small.e-daterangepicker.e-popup .e-change-icon {
  color: #6b7280;
}
.e-daterangepicker.e-popup .e-day-span,
.e-bigger.e-small .e-daterangepicker.e-popup .e-day-span,
.e-bigger.e-small.e-daterangepicker.e-popup .e-day-span {
  color: #9ca3af;
}
.e-daterangepicker.e-popup .e-separator,
.e-bigger.e-small .e-daterangepicker.e-popup .e-separator,
.e-bigger.e-small.e-daterangepicker.e-popup .e-separator {
  background-color: #e5e7eb;
}
.e-daterangepicker.e-popup .e-footer,
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer,
.e-bigger.e-small.e-daterangepicker.e-popup .e-footer {
  background-color: #f9fafb;
}
.e-daterangepicker.e-popup .e-footer .e-cancel.e-flat:not(:hover),
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer .e-cancel.e-flat:not(:hover),
.e-bigger.e-small.e-daterangepicker.e-popup .e-footer .e-cancel.e-flat:not(:hover) {
  /* stylelint-disable property-no-vendor-prefix */
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border-color: #d1d5db;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  color: #374151;
}
.e-daterangepicker.e-popup .e-footer .e-apply.e-flat.e-primary:not(:hover):not(:disabled),
.e-bigger.e-small .e-daterangepicker.e-popup .e-footer .e-apply.e-flat.e-primary:not(:hover):not(:disabled),
.e-bigger.e-small.e-daterangepicker.e-popup .e-footer .e-apply.e-flat.e-primary:not(:hover):not(:disabled) {
  -webkit-tap-highlight-color: transparent;
  background-color: #4f46e5;
  border-color: #4f46e5;
  color: #fff;
}

.e-bigger .e-daterangepicker,
*.e-bigger.e-daterangepicker,
*.e-device.e-daterangepicker {
  background-color: #fff;
  padding: 0;
}
.e-bigger .e-daterangepicker .e-calendar th,
*.e-bigger.e-daterangepicker .e-calendar th,
*.e-device.e-daterangepicker .e-calendar th {
  color: #6b7280;
}
.e-bigger .e-daterangepicker .e-start-btn, .e-bigger .e-daterangepicker .e-end-btn,
*.e-bigger.e-daterangepicker .e-start-btn,
*.e-bigger.e-daterangepicker .e-end-btn,
*.e-device.e-daterangepicker .e-start-btn,
*.e-device.e-daterangepicker .e-end-btn {
  background: #fff;
}
.e-bigger .e-daterangepicker .e-start-btn.e-active, .e-bigger .e-daterangepicker .e-start-btn.e-active:active, .e-bigger .e-daterangepicker .e-end-btn.e-active, .e-bigger .e-daterangepicker .e-end-btn.e-active:active:not([disabled]), .e-bigger .e-daterangepicker .e-start-btn.e-active:hover, .e-bigger .e-daterangepicker .e-end-btn.e-active:hover,
*.e-bigger.e-daterangepicker .e-start-btn.e-active,
*.e-bigger.e-daterangepicker .e-start-btn.e-active:active,
*.e-bigger.e-daterangepicker .e-end-btn.e-active,
*.e-bigger.e-daterangepicker .e-end-btn.e-active:active:not([disabled]),
*.e-bigger.e-daterangepicker .e-start-btn.e-active:hover,
*.e-bigger.e-daterangepicker .e-end-btn.e-active:hover,
*.e-device.e-daterangepicker .e-start-btn.e-active,
*.e-device.e-daterangepicker .e-start-btn.e-active:active,
*.e-device.e-daterangepicker .e-end-btn.e-active,
*.e-device.e-daterangepicker .e-end-btn.e-active:active:not([disabled]),
*.e-device.e-daterangepicker .e-start-btn.e-active:hover,
*.e-device.e-daterangepicker .e-end-btn.e-active:hover {
  background: #4f46e5;
  color: #fff;
}

.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-model-header {
  background-color: #f9fafb;
  color: #374151;
}
.e-daterangepick-mob-popup-wrap .e-daterangepicker.e-popup.e-popup-expand .e-model-header .e-popup-close {
  color: #374151;
}