{"_from": "@syncfusion/ej2-pdfviewer@*", "_id": "@syncfusion/ej2-pdfviewer@23.1.43", "_inBundle": false, "_integrity": "sha512-artZX3KeIGOxv/Wsm4ZwT8Q0SM8gZ8XvSZWlty4GWX6vC4Y5Vbcm3KElRNxcQvylkdfVdlVNWpgk5Q4IX3TpzQ==", "_location": "/@syncfusion/ej2-pdfviewer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@syncfusion/ej2-pdfviewer@*", "name": "@syncfusion/ej2-pdfviewer", "escapedName": "@syncfusion%2fej2-pdfviewer", "scope": "@syncfusion", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/", "/@syncfusion/ej2", "/@syncfusion/ej2-angular-pdfviewer", "/@syncfusion/ej2-react-pdfviewer", "/@syncfusion/ej2-vue-pdfviewer"], "_resolved": "https://nexus.syncfusion.com/repository/ej2-hotfix-new/@syncfusion/ej2-pdfviewer/-/ej2-pdfviewer-23.1.43.tgz", "_shasum": "14603a1ccb4fe07294847a37a3a0e92bf3e90852", "_spec": "@syncfusion/ej2-pdfviewer@*", "_where": "/jenkins/workspace/elease-automation_release_23.1.1/packages/included", "author": {"name": "Syncfusion Inc."}, "bugs": {"url": "https://github.com/syncfusion/ej2-pdfviewer/issues"}, "bundleDependencies": false, "dependencies": {"@syncfusion/ej2-base": "~23.1.41", "@syncfusion/ej2-buttons": "~23.1.43", "@syncfusion/ej2-calendars": "~23.1.41", "@syncfusion/ej2-compression": "~23.1.36", "@syncfusion/ej2-data": "~23.1.44", "@syncfusion/ej2-drawings": "~23.1.44", "@syncfusion/ej2-dropdowns": "~23.1.44", "@syncfusion/ej2-excel-export": "~23.1.36", "@syncfusion/ej2-file-utils": "~23.1.36", "@syncfusion/ej2-filemanager": "~23.1.43", "@syncfusion/ej2-grids": "~23.1.44", "@syncfusion/ej2-inplace-editor": "~23.1.41", "@syncfusion/ej2-inputs": "~23.1.43", "@syncfusion/ej2-layouts": "~23.1.36", "@syncfusion/ej2-lists": "~23.1.43", "@syncfusion/ej2-navigations": "~23.1.44", "@syncfusion/ej2-notifications": "~23.1.40", "@syncfusion/ej2-pdf": "~23.1.44", "@syncfusion/ej2-pdf-export": "~23.1.43", "@syncfusion/ej2-popups": "~23.1.44", "@syncfusion/ej2-richtexteditor": "~23.1.44"}, "deprecated": false, "description": "Essential JS 2 PDF viewer Component", "devDependencies": {}, "es2015": "./dist/es6/ej2-pdfviewer.es5.js", "homepage": "https://www.syncfusion.com/javascript-ui-controls", "keywords": ["ej2", "syncfusion", "JavaScript", "TypeScript", "pdf-viewer", "annotations", "form-fields", "digital-signature"], "license": "SEE LICENSE IN license", "main": "./dist/ej2-pdfviewer.umd.min.js", "module": "./index.js", "name": "@syncfusion/ej2-pdfviewer", "repository": {"type": "git", "url": "git+https://github.com/syncfusion/ej2-pdfviewer.git"}, "typings": "index.d.ts", "version": "23.1.44", "sideEffects": false}