@import 'ej2-base/styles/definition/tailwind.scss';
@import '../spreadsheet-ribbon/tailwind-definition.scss';
@import 'ej2-buttons/styles/button/tailwind-definition.scss';
@import 'ej2-buttons/styles/check-box/tailwind-definition.scss';
@import 'ej2-buttons/styles/radio-button/tailwind-definition.scss';
@import 'ej2-buttons/styles/switch/tailwind-definition.scss';
@import 'ej2-navigations/styles/toolbar/tailwind-definition.scss';
@import 'ej2-navigations/styles/tab/tailwind-definition.scss';
@import 'ej2-navigations/styles/context-menu/tailwind-definition.scss';
@import 'ej2-navigations/styles/menu/tailwind-definition.scss';
@import 'ej2-navigations/styles/treeview/tailwind-definition.scss';
@import 'ej2-grids/styles/excel-filter/tailwind-definition.scss';
@import 'ej2-calendars/styles/datepicker/tailwind-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/tailwind-definition.scss';
@import 'ej2-inputs/styles/color-picker/tailwind-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/tailwind-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/tailwind-definition.scss';
@import 'ej2-dropdowns/styles/list-box/tailwind-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/tailwind-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/tailwind-definition.scss';
@import 'tailwind-definition.scss';
@import 'icons/tailwind.scss';
@import 'all.scss';
