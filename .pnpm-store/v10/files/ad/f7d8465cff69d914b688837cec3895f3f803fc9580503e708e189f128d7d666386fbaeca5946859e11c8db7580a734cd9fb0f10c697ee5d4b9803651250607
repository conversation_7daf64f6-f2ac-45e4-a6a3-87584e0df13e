import { WParagraphFormat } from '../format/paragraph-format';
import { WSectionFormat } from '../format/section-format';
import { WCharacterFormat } from '../format/character-format';
import { WListFormat } from '../format/list-format';
import { HistoryInfo } from '../index';
import { ModifiedLevel, RowHistoryFormat, TableHistoryInfo } from './history-helper';
import { BlockWidget, ParagraphWidget, BodyWidget, TableCellWidget, FieldElementBox, TableWidget, TableRowWidget, BookmarkElementBox, HeaderFooterWidget, CheckBoxFormField, TextFrame, TextElementBox, FootnoteElementBox, ImageElementBox } from '../viewer/page';
import { Dictionary } from '../../base/dictionary';
import { abstractListsProperty, listIdProperty, listsProperty, nsidProperty } from '../../index';
import { TextPosition, ImageInfo } from '../index';
import { isNullOrUndefined } from '@syncfusion/ej2-base';
import { ElementBox, CommentCharacterElementBox } from '../viewer/page';
import { WTableFormat, WRowFormat, WCellFormat, WParagraphStyle } from '../format/index';
import { HelperMethods } from '../editor/editor-helper';
import { CONTROL_CHARACTERS } from '../../base/types';
/**
 * @private
 */
var BaseHistoryInfo = /** @class */ (function () {
    function BaseHistoryInfo(node) {
        this.cellOperation = [];
        this.splittedRevisions = [];
        /**
         * @private
         */
        this.markerData = [];
        this.ownerIn = node;
        this.documentHelper = node.documentHelper;
        this.modifiedPropertiesIn = [];
        this.modifiedNodeLength = [];
        this.removedNodesIn = [];
        this.insertedNodes = [];
    }
    Object.defineProperty(BaseHistoryInfo.prototype, "owner", {
        //Properties
        //gets owner control
        get: function () {
            return this.ownerIn;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(BaseHistoryInfo.prototype, "editorHistory", {
        get: function () {
            return this.owner.editorHistory;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(BaseHistoryInfo.prototype, "action", {
        get: function () {
            return this.actionIn;
        },
        set: function (value) {
            this.actionIn = value;
            if (this.owner.enableCollaborativeEditing) {
                if (value === 'DeleteColumn' || value === 'DeleteCells' || value === 'ClearCells' || value === 'MergeCells') {
                    if (!this.owner.selection.isTableSelected() || value === 'ClearCells' || value === 'MergeCells') {
                        this.insertedText = CONTROL_CHARACTERS.Cell;
                        this.deleteColumnOperation(this.action);
                    }
                }
                else if (value === 'Accept Change' || value === 'Reject Change') {
                    if (this.cellOperation.length > 0) {
                        return;
                    }
                    this.createAcceptRejectOperation(this.action);
                }
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(BaseHistoryInfo.prototype, "modifiedProperties", {
        get: function () {
            return this.modifiedPropertiesIn;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(BaseHistoryInfo.prototype, "removedNodes", {
        /* eslint-enable */
        get: function () {
            return this.removedNodesIn;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(BaseHistoryInfo.prototype, "selectionStart", {
        //gets or sets selection start
        get: function () {
            return this.selectionStartIn;
        },
        set: function (value) {
            this.selectionStartIn = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(BaseHistoryInfo.prototype, "selectionEnd", {
        get: function () {
            return this.selectionEndIn;
        },
        set: function (value) {
            this.selectionEndIn = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(BaseHistoryInfo.prototype, "insertPosition", {
        get: function () {
            return this.insertPositionIn;
        },
        set: function (value) {
            this.insertPositionIn = value;
            if (this.owner.enableCollaborativeEditing && value !== '' && !isNullOrUndefined(value) && value.indexOf('C') === -1) {
                //TODO: Insert position not needed in all the cases. Need to optimize it.
                this.insertIndex = this.owner.selection.getAbsolutePositionFromRelativePosition(value);
            }
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(BaseHistoryInfo.prototype, "endPosition", {
        get: function () {
            return this.endPositionIn;
        },
        set: function (value) {
            this.endPositionIn = value;
        },
        enumerable: true,
        configurable: true
    });
    Object.defineProperty(BaseHistoryInfo.prototype, "viewer", {
        get: function () {
            return this.ownerIn.viewer;
        },
        enumerable: true,
        configurable: true
    });
    BaseHistoryInfo.prototype.updateSelection = function () {
        if (this.owner.enableCollaborativeEditing) {
            //TODO: Need to consider formard and backward selection
            var start = this.owner.selection.start.clone();
            var end = this.owner.selection.end.clone();
            this.startIndex = this.owner.selection.getAbsolutePositionFromRelativePosition(start);
            this.endIndex = this.owner.selection.getAbsolutePositionFromRelativePosition(end);
        }
        var blockInfo = this.owner.selection.getParagraphInfo(this.owner.selection.start);
        this.selectionStart = this.owner.selection.getHierarchicalIndex(blockInfo.paragraph, blockInfo.offset.toString());
        blockInfo = this.owner.selection.getParagraphInfo(this.owner.selection.end);
        this.selectionEnd = this.owner.selection.getHierarchicalIndex(blockInfo.paragraph, blockInfo.offset.toString());
    };
    BaseHistoryInfo.prototype.setBookmarkInfo = function (bookmark) {
        this.removedNodes.push({ 'bookmark': bookmark, 'startIndex': bookmark.indexInOwner, 'endIndex': bookmark.reference.indexInOwner });
    };
    BaseHistoryInfo.prototype.setFormFieldInfo = function (field, value) {
        this.removedNodes.push({ 'formField': field, 'value': value });
    };
    BaseHistoryInfo.prototype.setEditRangeInfo = function (editStart) {
        this.removedNodes.push({ 'editStart': editStart, 'startIndex': editStart.indexInOwner, 'endIndex': editStart.editRangeEnd.indexInOwner });
    };
    BaseHistoryInfo.prototype.revertFormTextFormat = function () {
        /* eslint-disable @typescript-eslint/no-explicit-any */
        var fieldInfo = this.removedNodes[0];
        var text = fieldInfo.value;
        /* eslint-enable @typescript-eslint/no-explicit-any */
        var formField = fieldInfo.formField;
        if (this.editorHistory.isUndoing) {
            this.owner.editorModule.applyTextFormatInternal(formField, text);
            this.editorHistory.recordChanges(this);
        }
        else {
            text = HelperMethods.formatText(formField.formFieldData.format, text);
            this.owner.editorModule.applyTextFormatInternal(formField, text);
            this.editorHistory.undoStack.push(this);
        }
    };
    BaseHistoryInfo.prototype.revertFormField = function () {
        /* eslint-disable @typescript-eslint/no-explicit-any */
        var fieldInfo = this.removedNodes[0];
        /* eslint-enable @typescript-eslint/no-explicit-any */
        var field = fieldInfo.formField;
        if (field.formFieldData instanceof CheckBoxFormField) {
            this.owner.editorModule.toggleCheckBoxFormField(field, true, fieldInfo.value);
        }
        else {
            this.owner.editorModule.updateFormField(field, fieldInfo.value);
        }
    };
    BaseHistoryInfo.prototype.revertBookmark = function () {
        var bookmarkInfo = this.removedNodes[0];
        var bookmark = bookmarkInfo.bookmark;
        if (this.editorHistory.isUndoing) {
            this.documentHelper.bookmarks.add(bookmark.name, bookmark);
            bookmark.line.children.splice(bookmarkInfo.startIndex, 0, bookmark);
            var previousNode = bookmark.previousNode;
            if (previousNode instanceof FieldElementBox && !isNullOrUndefined(previousNode.formFieldData)) {
                previousNode.formFieldData.name = bookmark.name;
            }
            bookmark.reference.line.children.splice(bookmarkInfo.endIndex, 0, bookmark.reference);
            this.editorHistory.recordChanges(this);
        }
        else {
            this.owner.editorModule.deleteBookmarkInternal(bookmark);
            this.editorHistory.undoStack.push(this);
        }
    };
    BaseHistoryInfo.prototype.revertComment = function () {
        var editPosition = this.insertPosition;
        var comment = this.removedNodes[0];
        var insert = false;
        if (this.action === 'ResolveComment') {
            this.editorHistory.currentBaseHistoryInfo = this;
            this.owner.editor.resolveOrReopenComment(comment, !comment.isResolved);
            return;
        }
        if (this.action === 'EditComment') {
            var modifiedCommentObject = this.modifiedProperties[0];
            this.editorHistory.currentBaseHistoryInfo = this;
            var commentView = this.owner.commentReviewPane.commentPane.comments.get(comment);
            commentView.commentText.innerText = modifiedCommentObject.text;
            modifiedCommentObject.text = comment.text;
            comment.text = commentView.commentText.innerText;
            this.owner.editorHistory.updateHistory();
            this.owner.fireContentChange();
            return;
        }
        if (this.action === 'InsertCommentWidget') {
            insert = (this.editorHistory.isRedoing);
        }
        else if (this.action === 'DeleteCommentWidget') {
            insert = (this.editorHistory.isUndoing);
        }
        if (insert) {
            if (comment) {
                if (comment.isReply) {
                    this.owner.editor.addReplyComment(comment, this.insertPosition);
                }
                else {
                    this.owner.editor.addCommentWidget(comment, false, true, true);
                }
            }
        }
        else {
            var commentElement = this.owner.editor.getCommentElementBox(editPosition);
            this.owner.editor.deleteCommentWidget(commentElement);
        }
    };
    BaseHistoryInfo.prototype.revertEditRangeRegion = function () {
        var editRangeInfo = this.removedNodes[0];
        var editStart = editRangeInfo.editStart;
        if (this.editorHistory.isUndoing) {
            var user = editStart.user === '' ? editStart.group : editStart.user;
            this.owner.editor.updateRangeCollection(editStart, user);
            editStart.line.children.splice(editRangeInfo.startIndex, 0, editStart);
            editStart.editRangeEnd.line.children.splice(editRangeInfo.endIndex, 0, editStart.editRangeEnd);
            this.editorHistory.recordChanges(this);
        }
        else {
            this.owner.editorModule.removeUserRestrictionsInternal(editStart);
            this.editorHistory.undoStack.push(this);
        }
        this.owner.editor.fireContentChange();
    };
    /* eslint-disable  */
    BaseHistoryInfo.prototype.revert = function () {
        if (this.action === 'FormTextFormat') {
            this.revertFormTextFormat();
            return;
        }
        if (this.action === 'UpdateFormField') {
            this.revertFormField();
            return;
        }
        if (this.action === 'DeleteBookmark') {
            this.revertBookmark();
            return;
        }
        if (this.action === 'RemoveEditRange') {
            this.revertEditRangeRegion();
            return;
        }
        if (this.action === 'InsertCommentWidget' || this.action === 'DeleteCommentWidget' || this.action === 'ResolveComment' || this.action === 'EditComment') {
            this.revertComment();
            return;
        }
        if (this.action === 'ListFormat' && this.owner.editor.listNumberFormat !== '') {
            var abstractList = this.documentHelper.lists[0].abstractList.levels[this.owner.editor.listLevelNumber];
            var currentListLevelPattern = abstractList.listLevelPattern;
            var currentNUmberFormat = abstractList.numberFormat;
            abstractList.listLevelPattern = this.owner.editor.listLevelPattern;
            abstractList.numberFormat = this.owner.editor.listNumberFormat;
            this.owner.editor.listLevelPattern = currentListLevelPattern;
            this.owner.editor.listNumberFormat = currentNUmberFormat;
        }
        this.owner.isShiftingEnabled = true;
        var selectionStartTextPosition = undefined;
        var selectionEndTextPosition = undefined;
        var start = this.selectionStart;
        var end = this.selectionEnd;
        var isForwardSelection = TextPosition.isForwardSelection(start, end);
        if (this.modifiedProperties.length > 0 || this.action === 'Selection'
            || this.action === 'ClearCharacterFormat' || this.action === 'ClearParagraphFormat') {
            selectionStartTextPosition = this.owner.selection.getTextPosBasedOnLogicalIndex(start);
            selectionEndTextPosition = this.owner.selection.getTextPosBasedOnLogicalIndex(end);
            this.revertModifiedProperties(selectionStartTextPosition, selectionEndTextPosition);
        }
        else {
            var sel = this.owner.selection;
            var deletedNodes = this.removedNodes;
            this.removedNodesIn = [];
            if (isNullOrUndefined(this.endPosition)) {
                this.endPosition = this.insertPosition;
            }
            var isForward = TextPosition.isForwardSelection(this.insertPosition, this.endPosition);
            var insertTextPosition = sel.getTextPosBasedOnLogicalIndex(isForward ? this.insertPosition : this.endPosition);
            var endTextPosition = sel.getTextPosBasedOnLogicalIndex(isForward ? this.endPosition : this.insertPosition);
            if (this.lastElementRevision && this.editorHistory.isUndoing) {
                if (isNullOrUndefined(this.endRevisionLogicalIndex)) {
                    this.updateEndRevisionInfo();
                }
            }
            if (this.action === 'ClearRevisions') {
                this.undoRevisionForElements(insertTextPosition, endTextPosition, deletedNodes[deletedNodes.length - 1]);
                deletedNodes = [];
            }
            if (this.action === 'Uppercase') {
                sel.selectPosition(insertTextPosition, endTextPosition);
                this.editorHistory.currentBaseHistoryInfo = this;
                var editModule = this.owner.editorModule;
                editModule.changeSelectedTextCase(sel, insertTextPosition, endTextPosition, this.action.toString(), deletedNodes);
                editModule.reLayout(sel);
                return;
            }
            if (insertTextPosition.isAtSamePosition(endTextPosition)) {
                sel.selectContent(insertTextPosition, true);
            }
            else {
                sel.selectPosition(insertTextPosition, endTextPosition);
            }
            if (this.action === 'InsertHyperlink' && this.editorHistory.isRedoing) {
                var fieldBegin = this.owner.selection.getHyperlinkField();
                if (!isNullOrUndefined(fieldBegin)) {
                    var offset = (fieldBegin.line).getOffset(fieldBegin, 0);
                    insertTextPosition.setPositionParagraph(fieldBegin.line, offset);
                    this.owner.selection.start.setPositionInternal(insertTextPosition);
                    offset = fieldBegin.fieldEnd.line.getOffset(fieldBegin.fieldEnd, 1);
                    endTextPosition.setPositionParagraph(fieldBegin.fieldEnd.line, offset);
                }
            }
            this.editorHistory.currentBaseHistoryInfo = this;
            this.selectionStart = this.insertPosition;
            this.insertPosition = undefined;
            this.selectionEnd = this.endPosition;
            this.endPosition = undefined;
            var isRemoveContent = false;
            if (this.endRevisionLogicalIndex && deletedNodes.length > 0) {
                if (this.editorHistory.isUndoing || (this.editorHistory.isRedoing && insertTextPosition.isAtSamePosition(endTextPosition))) {
                    var currentPosition = sel.getTextPosBasedOnLogicalIndex(this.endRevisionLogicalIndex);
                    sel.selectPosition(insertTextPosition, currentPosition);
                }
                if (this.editorHistory.isUndoing) {
                    this.owner.editor.deleteSelectedContents(sel, true);
                }
            }
            if (!insertTextPosition.isAtSamePosition(endTextPosition)) {
                isRemoveContent = this.action === 'BackSpace' || this.action === 'Delete' || this.action === 'ClearCells'
                    || this.action === 'DeleteCells';
                var skipDelete = (deletedNodes.length > 0 && this.action === 'ParaMarkTrack') || this.action === 'ClearRevisions' || this.action === 'AcceptTOC';
                if (!(isRemoveContent) && this.action !== 'MergeCells' && this.action !== 'InsertRowAbove'
                    && this.action !== 'InsertRowBelow' && this.action !== 'InsertColumnLeft'
                    && this.action !== 'InsertColumnRight' && this.action !== 'Borders'
                    && this.action !== 'DeleteTable' && this.action !== 'DeleteColumn' && this.action !== 'DeleteRow') {
                    sel.end.setPositionInternal(endTextPosition);
                    if (!this.owner.selection.isEmpty && !skipDelete) {
                        if (this.editorHistory.isRedoing && this.action !== 'Accept Change' && this.action !== 'ParaMarkTrack' &&
                            this.action !== 'ParaMarkReject' && this.action !== 'RemoveRowTrack') {
                            this.owner.editorModule.removeSelectedContents(sel);
                        }
                        else {
                            this.owner.editorModule.deleteSelectedContents(sel, true);
                        }
                        if (!isNullOrUndefined(this.editorHistory.currentHistoryInfo) &&
                            this.editorHistory.currentHistoryInfo.action === 'PageBreak' && this.documentHelper.blockToShift) {
                            this.documentHelper.layout.shiftLayoutedItems(false);
                        }
                    }
                }
            }
            else if (this.action === 'SectionBreakContinuous' && insertTextPosition && this.editorHistory.isUndoing) {
                if (insertTextPosition.offset === 0 && !isNullOrUndefined(insertTextPosition.paragraph.previousRenderedWidget) && insertTextPosition.paragraph.previousRenderedWidget instanceof ParagraphWidget && insertTextPosition.paragraph.previousRenderedWidget.isEndsWithPageBreak && insertTextPosition.paragraph.containerWidget instanceof BodyWidget && insertTextPosition.currentWidget === insertTextPosition.currentWidget.paragraph.firstChild && insertTextPosition.paragraph.containerWidget.sectionFormat.breakCode === 'NoBreak') {
                    var section = insertTextPosition.paragraph.previousRenderedWidget.containerWidget;
                    this.owner.editor.combineSectionInternal(this.owner.selection, section, insertTextPosition.paragraph.containerWidget);
                    this.owner.editorModule.layoutWholeDocument();
                }
            }
            var isRedoAction = (this.editorHistory.isRedoing && !isRemoveContent);
            isRemoveContent = this.lastElementRevision ? false : isRemoveContent;
            this.revertModifiedNodes(deletedNodes, isRedoAction, isForwardSelection ? start : end, start === end);
            if (isRemoveContent) {
                this.removeContent(insertTextPosition, endTextPosition, true);
            }
            //this.owner.editorModule.reLayout(this.documentHelper.selection);
        }
        var isSelectionChanged = false;
        var updateSelection = false;
        if (!isNullOrUndefined(this.editorHistory.currentHistoryInfo) && (this.editorHistory.currentHistoryInfo.action === 'Reject All' || this.editorHistory.currentHistoryInfo.action === 'Accept All' || this.editorHistory.currentHistoryInfo.action === 'Paste')) {
            updateSelection = true;
        }
        if (!this.owner.trackChangesPane.isTrackingPageBreak && ((this.editorHistory.isUndoing || this.endRevisionLogicalIndex || this.action === 'RemoveRowTrack' || updateSelection) && isNullOrUndefined(this.editorHistory.currentHistoryInfo) || updateSelection) ||
            ((this.action === 'InsertRowAbove' || this.action === 'Borders' || this.action === 'InsertRowBelow' || this.action === 'InsertColumnLeft' || this.action === 'InsertColumnRight' || this.action === 'Accept Change' || this.action === 'PasteColumn' || this.action === 'PasteRow' || this.action === 'PasteOverwrite' || this.action === 'PasteNested') && (this.editorHistory.isRedoing
                || this.editorHistory.currentHistoryInfo.action === 'Paste'))) {
            if (this.action === 'RemoveRowTrack' && this.editorHistory.isRedoing) {
                selectionStartTextPosition = this.owner.selection.getTextPosBasedOnLogicalIndex(this.selectionStart);
                selectionEndTextPosition = this.owner.selection.getTextPosBasedOnLogicalIndex(this.selectionEnd);
            }
            else {
                selectionStartTextPosition = this.owner.selection.getTextPosBasedOnLogicalIndex(start);
                selectionEndTextPosition = this.owner.selection.getTextPosBasedOnLogicalIndex(end);
            }
            this.owner.selection.selectRange(selectionStartTextPosition, selectionEndTextPosition);
            this.documentHelper.updateFocus();
            isSelectionChanged = true;
        }
        this.owner.trackChangesPane.isTrackingPageBreak = false;
        // Updates insert position of history info instance.
        this.insertPosition = start;
        this.endPosition = end;
        if (!isNullOrUndefined(this.editorHistory.currentHistoryInfo) &&
            (this.editorHistory.currentHistoryInfo.action === 'Accept All'
                || this.editorHistory.currentHistoryInfo.action === 'Reject All' || this.editorHistory.currentHistoryInfo.action === 'RemoveComment')) {
            if (this.owner.documentHelper.blockToShift) {
                this.owner.documentHelper.layout.shiftLayoutedItems(false);
            }
        }
        this.owner.editorModule.reLayout(this.owner.selection, this.owner.selection.isEmpty);
        if (this.editorHistory.isUndoing && this.action === 'SectionBreak') {
            this.owner.editorModule.layoutWholeDocument();
        }
        if (isSelectionChanged) {
            this.documentHelper.scrollToPosition(this.owner.selection.start, this.owner.selection.end);
        }
        this.highlightListText();
    };
    BaseHistoryInfo.prototype.highlightListText = function () {
        if (!isNullOrUndefined(this.editorHistory.currentHistoryInfo)) {
            if (this.action === 'ListCharacterFormat' || (this.editorHistory.currentHistoryInfo.action === 'ListSelect' && this.action === 'ListFormat')) {
                var selectionStartTextPosition = this.owner.selection.getTextPosBasedOnLogicalIndex(this.selectionStart);
                var widget = selectionStartTextPosition.currentWidget;
                this.documentHelper.selection.highlightListText(widget);
            }
        }
    };
    BaseHistoryInfo.prototype.removeContent = function (insertTextPosition, endTextPosition, skipDeletecell) {
        //If the base parent of the insert text position and end text position is null 
        //then the paragraphs already removed.
        //Example scenario: In table editing that is delete cells operation 
        // we will backed up the entire table ad it will be replaced on undo operation.
        //At that time if the positions are in table 
        //which is already replaced in undo (revert modified nodes method) then the base parent of the paragraph will be null.
        //So again, selecting the content and deleting is unnecessary
        // and it will cause improper position updates and null reference exceptions. 
        if ((!isNullOrUndefined(insertTextPosition.paragraph.containerWidget) &&
            insertTextPosition.paragraph.containerWidget instanceof BodyWidget &&
            (!isNullOrUndefined(endTextPosition.paragraph.containerWidget)
                && endTextPosition.paragraph.containerWidget instanceof BodyWidget))
            || (!isNullOrUndefined(insertTextPosition.paragraph.containerWidget)
                && !isNullOrUndefined(endTextPosition.paragraph.containerWidget)
                && insertTextPosition.paragraph.containerWidget instanceof TableCellWidget
                && endTextPosition.paragraph.containerWidget instanceof TableCellWidget
                && !isNullOrUndefined(insertTextPosition.paragraph.bodyWidget)) ||
            (!isNullOrUndefined(insertTextPosition.paragraph.containerWidget)
                && !isNullOrUndefined(endTextPosition.paragraph.containerWidget)
                && insertTextPosition.paragraph.containerWidget instanceof TextFrame
                && endTextPosition.paragraph.containerWidget instanceof TextFrame)) {
            //Removes if any empty paragraph is added while delete.
            this.owner.selection.selectRange(insertTextPosition, endTextPosition);
            this.documentHelper.updateFocus();
            var isDelete = false;
            if (this.action === 'BackSpace' || this.action === 'Uppercase' || this.action === 'RemoveRowTrack') {
                isDelete = true;
            }
            this.owner.editorModule.deleteSelectedContents(this.owner.selection, isDelete, skipDeletecell);
        }
    };
    BaseHistoryInfo.prototype.updateEndRevisionInfo = function () {
        this.lastElementRevision = this.checkAdjacentNodeForMarkedRevision(this.lastElementRevision);
        var currentRevision = this.retrieveEndPosition(this.lastElementRevision);
        var blockInfo = this.owner.selection.getParagraphInfo(currentRevision);
        if (blockInfo.paragraph.getLength() == blockInfo.offset) {
            blockInfo.offset++;
        }
        this.endRevisionLogicalIndex = this.owner.selection.getHierarchicalIndex(blockInfo.paragraph, blockInfo.offset.toString());
        this.lastElementRevision.isMarkedForRevision = false;
    };
    BaseHistoryInfo.prototype.retrieveEndPosition = function (elementBox) {
        var endPosition = new TextPosition(this.owner);
        var offset = elementBox.line.getOffset(elementBox, 0) + elementBox.length;
        endPosition.setPositionFromLine(elementBox.line, offset);
        return endPosition;
    };
    /**
     * Method to retrieve exact spitted node which is marked as last available element.
     *
     * @param {ElementBox} elementBox - Specifies the element box
     * @returns {ElementBox} - Returns element box
     */
    BaseHistoryInfo.prototype.checkAdjacentNodeForMarkedRevision = function (elementBox) {
        var nextItem = elementBox.nextNode;
        var markedNode;
        while (!isNullOrUndefined(nextItem) && nextItem.isMarkedForRevision) {
            markedNode = nextItem;
            nextItem = nextItem.nextNode;
        }
        return !isNullOrUndefined(markedNode) ? markedNode : elementBox;
    };
    BaseHistoryInfo.prototype.revertModifiedProperties = function (start, end) {
        if (this.action === 'CellFormat' || this.action === 'CellOptions' || this.action === 'TableOptions') {
            this.owner.isShiftingEnabled = false;
        }
        this.owner.selection.selectRange(start, end);
        this.documentHelper.updateFocus();
        if (this.action === 'RowResizing' || this.action === 'CellResizing') {
            this.revertResizing();
        }
        else if (this.action === 'CellOptions' || this.action === 'TableOptions') {
            this.revertTableDialogProperties(this.action);
        }
        else if (this.action !== 'Selection') {
            this.revertProperties();
        }
    };
    // Redoes the Action
    BaseHistoryInfo.prototype.redoAction = function () {
        var editor = this.owner.editorModule;
        switch (this.action) {
            case 'BackSpace':
                editor.singleBackspace(this.owner.selection, true);
                break;
            case 'Delete':
                editor.singleDelete(this.owner.selection, true);
                break;
            case 'DeleteTable':
                editor.deleteTable();
                break;
            case 'DeleteColumn':
                editor.deleteColumn();
                break;
            case 'DeleteRow':
                editor.deleteRow();
                break;
            case 'MergeCells':
                editor.mergeSelectedCellsInTable();
                break;
            case 'InsertRowAbove':
                editor.insertRow(true);
                break;
            case 'InsertRowBelow':
                editor.insertRow(false);
                break;
            case 'InsertColumnLeft':
                editor.insertColumn(true);
                break;
            case 'InsertColumnRight':
                editor.insertColumn(true);
                break;
            case 'SectionBreak':
                editor.insertSection(this.owner.selection, true);
                break;
            case 'SectionBreakContinuous':
                editor.insertSection(this.owner.selection, true, undefined, true);
                break;
            case 'TableAutoFitToContents':
                editor.autoFitTable('FitToContents');
                break;
            case 'TableAutoFitToWindow':
                editor.autoFitTable('FitToWindow');
                break;
            case 'TableFixedColumnWidth':
                editor.autoFitTable('FixedColumnWidth');
                break;
            case 'RemoveRowTrack':
                this.owner.selection.handleAcceptReject(true);
                break;
        }
    };
    BaseHistoryInfo.prototype.revertModifiedNodes = function (deletedNodes, isRedoAction, start, isEmptySelection) {
        if (isRedoAction && (this.action === 'BackSpace' || this.action === 'Delete' || this.action === 'DeleteTable'
            || this.action === 'DeleteColumn' || this.action === 'DeleteRow' || this.action === 'InsertRowAbove' ||
            this.action === 'InsertRowBelow' || this.action === 'InsertColumnLeft' || this.action === 'InsertColumnRight'
            || this.action === 'MergeCells' || this.action === 'SectionBreak' || this.action === 'SectionBreakContinuous' || this.action === 'TableAutoFitToContents' ||
            this.action === 'TableAutoFitToWindow' || this.action === 'TableFixedColumnWidth' || this.action === 'PasteColumn' || this.action === 'PasteOverwrite' || this.action === 'PasteNested')) {
            this.redoAction();
            if (this.action === 'SectionBreak' || this.action === 'SectionBreakContinuous') {
                return;
            }
        }
        if (deletedNodes.length > 0) {
            //tslint:disable-next-line:max-line-length
            if ((this.editorHistory.isUndoing && (this.action === 'RemoveRowTrack' || this.action === 'DeleteCells' ||
                this.action === 'DeleteColumn' || this.action === 'DeleteRow' || this.action === 'MergeCells'))
                || (this.action === 'InsertRowAbove' || this.action === 'InsertRowBelow' || this.action === 'InsertColumnLeft'
                    //tslint:disable-next-line:max-line-length
                    || this.action === 'ClearCells' || this.action === 'InsertColumnRight' || this.action === 'Borders' || this.action === 'TableAutoFitToContents' || this.action === 'TableAutoFitToWindow' ||
                    this.action === 'TableFixedColumnWidth' || this.action === 'RemoveRowTrack' || this.action === 'PasteColumn' || this.action === 'PasteRow' || this.action === 'PasteOverwrite' || this.action === 'PasteNested')) {
                var insertIndex = this.selectionStart;
                var block = this.owner.editorModule.getBlock({ index: insertIndex }).node;
                var lastNode = deletedNodes[deletedNodes.length - 1];
                if ((block instanceof TableWidget || block.previousRenderedWidget instanceof TableWidget || block.isInsideTable)
                    && lastNode instanceof TableWidget) {
                    if (block instanceof ParagraphWidget && !block.isInsideTable) {
                        block = block.previousRenderedWidget;
                    }
                    else if (block instanceof ParagraphWidget && block.isInsideTable) {
                        block = block.associatedCell.ownerTable;
                    }
                    block = block.combineWidget(this.viewer);
                    this.owner.editorModule.insertTableInternal(block, lastNode, false);
                    if (this.action === 'PasteColumn' || this.action === 'PasteRow' || this.action === 'PasteOverwrite' || this.action === 'PasteNested') {
                        this.removedNodes.push(block);
                    }
                    else {
                        deletedNodes.splice(deletedNodes.indexOf(lastNode), 1);
                    }
                }
                else if (lastNode instanceof TableWidget) {
                    this.owner.editorModule.insertBlock(lastNode);
                }
            }
            else {
                var initialStart = start;
                var block = this.owner.editorModule.getBlock({ index: initialStart }).node;
                // initialStart = blockObj.position;
                if (deletedNodes.length > 0 && (this.action === 'BackSpace' && isEmptySelection
                    || (!(block instanceof TableWidget) && !(block instanceof HeaderFooterWidget)))) {
                    var lastNode = deletedNodes[0];
                    if (lastNode instanceof BodyWidget && !isNullOrUndefined(deletedNodes[1])) {
                        lastNode = deletedNodes[1];
                    }
                    if (this.action === 'TrackingPageBreak' || ((this.action === 'SectionBreak' || this.action === 'SectionBreakContinuous') && lastNode instanceof BodyWidget ||
                        !isNullOrUndefined(this.editorHistory.currentHistoryInfo) &&
                            this.editorHistory.currentHistoryInfo.action === 'PageBreak')) {
                        lastNode = deletedNodes[1];
                    }
                    if (lastNode instanceof WCharacterFormat) {
                        var newParagraph = new ParagraphWidget();
                        newParagraph.characterFormat = lastNode;
                        this.owner.editorModule.insertNewParagraphWidget(newParagraph, true);
                        deletedNodes.splice(deletedNodes.indexOf(lastNode), 1);
                        block = newParagraph;
                    }
                    if (lastNode instanceof ParagraphWidget && this.owner.selection.start.offset > 0) {
                        this.owner.editorModule.insertNewParagraphWidget(lastNode, true);
                        if (lastNode.characterFormat.removedIds.length > 0) {
                            this.owner.editor.constructRevisionFromID(lastNode.characterFormat, undefined);
                        }
                        deletedNodes.splice(deletedNodes.indexOf(lastNode), 1);
                        if (isNullOrUndefined(block)) {
                            var nextBlock = this.documentHelper.selection.getNextParagraphBlock(lastNode.getSplitWidgets().pop());
                            this.owner.selection.getNextRenderedBlock(lastNode);
                            var startParagraph = this.owner.selection.start.paragraph;
                            if (nextBlock && startParagraph && startParagraph.bodyWidget instanceof BodyWidget
                                && !startParagraph.isInsideTable && !this.owner.selection.isinEndnote && !this.owner.selection.isinFootnote
                                && !startParagraph.bodyWidget.equals(nextBlock.bodyWidget)) {
                                nextBlock = undefined;
                            }
                            if (isNullOrUndefined(nextBlock)) {
                                //Sets the selection as starting of last paragraph.
                                this.owner.selection.selectParagraphInternal(lastNode, true);
                            }
                        }
                    }
                    if (lastNode instanceof TableWidget && this.owner.selection.start.offset > 0) {
                        var firstBlock = deletedNodes[deletedNodes.length - 1];
                        if (firstBlock instanceof ParagraphWidget) {
                            this.owner.editorModule.insertNewParagraphWidget(firstBlock, true);
                            deletedNodes.splice(deletedNodes.indexOf(firstBlock), 1);
                            if (isNullOrUndefined(block)) {
                                var nextBlock = this.documentHelper.selection.getNextParagraphBlock(firstBlock.getSplitWidgets().pop());
                                if (isNullOrUndefined(nextBlock)) {
                                    //Sets the selection as starting of last paragraph.
                                    this.owner.selection.selectParagraphInternal(firstBlock, true);
                                }
                            }
                        }
                    }
                }
                if (deletedNodes.length > 0) {
                    var firstNode = deletedNodes[deletedNodes.length - 1];
                    if (block instanceof TableWidget) {
                        block = block.combineWidget(this.viewer);
                        if (firstNode instanceof TableWidget) {
                            this.owner.editorModule.insertTableInternal(block, firstNode, true);
                            deletedNodes.splice(deletedNodes.indexOf(firstNode), 1);
                            this.insertPosition = start;
                            var nextWidget = firstNode.getSplitWidgets().pop();
                            if (nextWidget.nextRenderedWidget instanceof TableWidget) {
                                block = nextWidget.nextRenderedWidget;
                            }
                            else {
                                initialStart = start;
                                block = this.owner.editorModule.getBlock({ index: initialStart }).node;
                            }
                        }
                    }
                    //Checks if first node is paragraph and current insert position is paragraph end.
                    if (firstNode instanceof ParagraphWidget && this.owner.selection.start.offset > 0
                        && this.owner.selection.start.offset === this.owner.selection.getLineLength(this.owner.selection.start.paragraph.lastChild)) {
                        var editor = this.owner.editorModule;
                        editor.insertNewParagraphWidget(firstNode, false);
                        if (firstNode.characterFormat.removedIds.length > 0) {
                            this.owner.editor.constructRevisionFromID(firstNode.characterFormat, undefined);
                        }
                        deletedNodes.splice(deletedNodes.indexOf(firstNode), 1);
                        //Removes the intermediate empty paragraph instance.
                        if (this.action !== 'Paste') {
                            editor.removeBlock(this.owner.selection.start.paragraph);
                        }
                        var paragraph = this.documentHelper.selection.getNextParagraphBlock(firstNode.getSplitWidgets().pop());
                        if (!isNullOrUndefined(paragraph)) {
                            this.owner.selection.selectParagraphInternal(paragraph, true);
                        }
                    }
                    else if (deletedNodes[0] instanceof TableWidget && deletedNodes.length !== 1) {
                        var nextNode = deletedNodes[1];
                        if (nextNode instanceof ParagraphWidget && nextNode.isEmpty()) {
                            deletedNodes.splice(deletedNodes.indexOf(nextNode), 1);
                        }
                    }
                }
                if (deletedNodes.length > 0) {
                    if (block instanceof TableWidget) {
                        block = block.combineWidget(this.viewer);
                    }
                    this.insertRemovedNodes(deletedNodes, block);
                }
            }
        }
    };
    BaseHistoryInfo.prototype.insertRemovedNodes = function (deletedNodes, block) {
        for (var i = deletedNodes.length - 1, index = 0; i > -1; i--) {
            var node = deletedNodes[i];
            if (node instanceof ElementBox) {
                this.owner.editorModule.insertInlineInSelection(this.owner.selection, node);
            }
            else if (node instanceof BlockWidget) {
                if (node instanceof TableRowWidget) {
                    if (block instanceof TableWidget) {
                        block.childWidgets.splice(index, 0, node);
                        this.owner.editorModule.updateNextBlocksIndex(node, true);
                        if (i === 0 || !(deletedNodes[i - 1] instanceof TableRowWidget)) {
                            this.documentHelper.layout.layoutBodyWidgetCollection(block.index, block.containerWidget, block, false);
                        }
                    }
                }
                else if (block instanceof TableWidget) {
                    this.owner.editorModule.insertBlockTable(this.owner.selection, node, block);
                }
                else {
                    this.owner.editorModule.insertBlock(node);
                }
            }
            else if (node instanceof WCharacterFormat) {
                var insertIndex = this.selectionStart;
                var wiget = this.owner.editorModule.getBlock({ index: insertIndex }).node;
                if (wiget instanceof ParagraphWidget) {
                    if (node.removedIds.length > 0) {
                        wiget.characterFormat.removedIds = node.removedIds.slice();
                        this.owner.editor.constructRevisionFromID(wiget.characterFormat, true);
                    }
                    else if (wiget.characterFormat.revisions.length > 0) {
                        wiget.characterFormat = node.cloneFormat();
                    }
                }
            }
            else if (node instanceof BodyWidget) {
                if (!isNullOrUndefined(node.removedHeaderFooters) && node.removedHeaderFooters.length !== 0) {
                    this.owner.documentHelper.headersFooters.splice(node.sectionIndex, 0, node.removedHeaderFooters[0]);
                    node.removedHeaderFooters = undefined;
                }
                this.owner.editorModule.insertSection(this.owner.selection, false, true, undefined, undefined, node.sectionFormat);
            }
            else if (typeof (node) === 'string' && this.action === 'AcceptTOC') {
                var insertIndex = this.selectionStart;
                var widget = this.owner.editorModule.getBlock({ index: insertIndex }).node;
                var endWidget = this.owner.editorModule.getBlock({ index: this.selectionEnd }).node;
                var currentRevision = this.owner.documentHelper.revisionsInternal.get(node);
                if (this.editorHistory.isUndoing) {
                    while (widget instanceof ParagraphWidget && widget !== endWidget) {
                        this.owner.editor.insertRevisionForBlock(widget, currentRevision.revisionType, true, currentRevision);
                        widget = this.documentHelper.selection.getNextParagraphBlock(widget.getSplitWidgets().pop());
                    }
                    this.owner.editor.insertRevisionForBlock(endWidget, currentRevision.revisionType, true, currentRevision);
                }
                else {
                    while (currentRevision.range.length > 0) {
                        var item = currentRevision.range[0];
                        var revisionIndex = item.revisions.indexOf(currentRevision);
                        if (revisionIndex >= 0) {
                            item.revisions.splice(revisionIndex, 1);
                            var rangeIndex = currentRevision.range.indexOf(item);
                            currentRevision.range.splice(rangeIndex, 1);
                            this.owner.trackChangesPane.updateCurrentTrackChanges(currentRevision);
                        }
                        if (currentRevision.range.length === 0) {
                            this.owner.revisions.remove(currentRevision);
                        }
                    }
                }
                this.owner.editor.addRemovedNodes(currentRevision.revisionID);
            }
        }
        deletedNodes = [];
    };
    BaseHistoryInfo.prototype.undoRevisionForElements = function (start, end, id) {
        var currentPara = start.paragraph;
        var endPara = end.paragraph;
        var currentRevision = this.documentHelper.revisionsInternal.get(id);
        var startoffset = this.owner.selection.getParagraphInfo(start).offset;
        var endoffset = this.owner.selection.getParagraphInfo(end).offset;
        var isSamePara = start.paragraph === end.paragraph;
        if (this.editorHistory.isUndoing) {
            while (currentPara !== endPara) {
                this.owner.editor.applyRevisionForCurrentPara(currentPara, startoffset, currentPara.getLength(), id, true);
                currentPara = this.documentHelper.selection.getNextParagraphBlock(currentPara.getSplitWidgets().pop());
                if (currentPara !== endPara) {
                    startoffset = 0;
                }
            }
            if (currentPara === endPara) {
                if (!isSamePara) {
                    startoffset = 0;
                }
                this.owner.editor.applyRevisionForCurrentPara(currentPara, startoffset, endoffset, id, false);
            }
        }
        else {
            while (currentRevision.range.length > 0) {
                var item = currentRevision.range[0];
                var revisionIndex = item.revisions.indexOf(currentRevision);
                if (revisionIndex >= 0) {
                    item.revisions.splice(revisionIndex, 1);
                    var rangeIndex = currentRevision.range.indexOf(item);
                    currentRevision.range.splice(rangeIndex, 1);
                    this.owner.trackChangesPane.updateCurrentTrackChanges(currentRevision);
                }
                if (currentRevision.range.length === 0) {
                    this.owner.revisions.remove(currentRevision);
                }
            }
        }
        this.removedNodes.push(id);
    };
    BaseHistoryInfo.prototype.revertResizing = function () {
        this.editorHistory.currentBaseHistoryInfo = this;
        if (this.action === 'RowResizing') {
            if (this.modifiedProperties[0] instanceof RowHistoryFormat) {
                this.modifiedProperties[0].revertChanges(this.editorHistory.isRedoing, this.owner);
            }
        }
        else {
            if (this.modifiedProperties[0] instanceof TableHistoryInfo) {
                //selected cell resizing the condition checks done based on the selected widgets only. so need to highlight the selection.
                if (this.owner.selection.selectedWidgets.length === 0) {
                    this.owner.selection.highlightSelection(true);
                }
                var prevTableHistoryInfo = this.modifiedProperties[0];
                var position = prevTableHistoryInfo.tableHierarchicalIndex;
                var block = this.owner.editorModule.getBlock({ index: position }).node;
                if (block instanceof TableWidget) {
                    var tableResize = this.owner.editorModule.tableResize;
                    this.owner.editor.setOffsetValue(this.owner.selection);
                    block = block.combineWidget(this.owner.viewer);
                    tableResize.currentResizingTable = block;
                    this.modifiedProperties.splice(0, 1);
                    if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
                        var tableHistoryInfoCurrent = new TableHistoryInfo(block, this.owner);
                        this.modifiedProperties.splice(0, 0, tableHistoryInfoCurrent);
                        this.owner.isLayoutEnabled = false;
                        tableResize.applyProperties(tableResize.currentResizingTable, prevTableHistoryInfo);
                        tableResize.currentResizingTable.isGridUpdated = true;
                        this.owner.isLayoutEnabled = true;
                        tableResize.updateGridValue(tableResize.currentResizingTable, false);
                        prevTableHistoryInfo.destroy();
                        prevTableHistoryInfo = undefined;
                    }
                }
            }
        }
    };
    BaseHistoryInfo.prototype.revertTableDialogProperties = function (action) {
        this.owner.isShiftingEnabled = false;
        this.editorHistory.currentBaseHistoryInfo = this;
        this.currentPropertyIndex = 0;
        if (action === 'CellOptions') {
            var selection = this.owner.selection;
            var cellFormat = this.modifiedProperties[0];
            this.owner.editorModule.updateCellMargins(selection, cellFormat);
        }
        else if (action === 'TableOptions') {
            this.owner.tableOptionsDialogModule.applyTableOptionsHelper(this.modifiedProperties[0]);
        }
        this.currentPropertyIndex = 0;
        this.owner.isShiftingEnabled = true;
    };
    BaseHistoryInfo.prototype.addModifiedPropertiesForSection = function (format, property, value) {
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var modifiedProperties = this.modifiedProperties;
            var previousFormat = (this.currentPropertyIndex < modifiedProperties.length ?
                modifiedProperties[this.currentPropertyIndex] : modifiedProperties[modifiedProperties.length - 1]);
            if (isNullOrUndefined(property)) {
                value = previousFormat;
                if (this.currentPropertyIndex < this.modifiedProperties.length) {
                    this.modifiedProperties[this.currentPropertyIndex] = format.cloneFormat();
                }
                else {
                    this.modifiedProperties[this.modifiedProperties.length - 1] = format.cloneFormat();
                }
            }
            else {
                value = previousFormat.getPropertyValue(property);
                previousFormat.copyFormat(format);
            }
            this.currentPropertyIndex++;
        }
        else {
            if (isNullOrUndefined(property)) {
                this.modifiedProperties.push(format.cloneFormat());
            }
            else {
                var currentFormat = new WSectionFormat();
                currentFormat.copyFormat(format);
                this.modifiedProperties.push(currentFormat);
            }
        }
        return value;
    };
    BaseHistoryInfo.prototype.addModifiedProperties = function (format, property, value) {
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var previousFormat = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            var skipRemove = false;
            if (format.ownerBase instanceof ElementBox) {
                var prevLength = this.modifiedNodeLength[this.currentPropertyIndex];
                if (format.ownerBase.length < prevLength) {
                    skipRemove = true;
                    this.modifiedNodeLength[this.currentPropertyIndex] = format.ownerBase.length;
                    this.modifiedNodeLength.splice(this.currentPropertyIndex + 1, 0, prevLength - format.ownerBase.length);
                    //Adds a copy of character format at next position for splitted inline.
                    var nextFormat = new WCharacterFormat(undefined);
                    nextFormat.copyFormat(previousFormat);
                    this.modifiedProperties.splice(this.currentPropertyIndex + 1, 0, nextFormat);
                }
            }
            if (this.action === 'ClearCharacterFormat') {
                if (this.editorHistory.isUndoing) {
                    value = previousFormat;
                    if (!skipRemove) {
                        this.modifiedProperties.splice(this.currentPropertyIndex, 1);
                        this.currentPropertyIndex--;
                    }
                }
                else {
                    this.modifiedProperties.push(format.cloneFormat());
                }
            }
            else {
                value = previousFormat;
                if (this.currentPropertyIndex < this.modifiedProperties.length) {
                    this.modifiedProperties[this.currentPropertyIndex] = format.cloneFormat();
                }
                else {
                    this.modifiedProperties[this.modifiedProperties.length - 1] = format.cloneFormat();
                }
            }
            this.currentPropertyIndex++;
        }
        else {
            if (isNullOrUndefined(property)) {
                this.modifiedProperties.push(format.cloneFormat());
            }
            else {
                var currentFormat = new WCharacterFormat(undefined);
                currentFormat.copyFormat(format);
                this.modifiedProperties.push(currentFormat);
            }
            if (format.ownerBase instanceof ElementBox) {
                this.modifiedNodeLength.push(format.ownerBase.length);
            }
            else {
                this.modifiedNodeLength.push(0);
            }
        }
        return value;
    };
    BaseHistoryInfo.prototype.addModifiedPropertiesForParagraphFormat = function (format, property, value) {
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var previousFormat = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            if (this.action === 'ClearParagraphFormat') {
                if (this.editorHistory.isUndoing) {
                    value = previousFormat;
                    this.modifiedProperties.splice(this.currentPropertyIndex, 1);
                    this.currentPropertyIndex--;
                }
                else {
                    this.modifiedProperties.push(format.cloneFormat());
                }
                this.currentPropertyIndex++;
                return value;
            }
            if (isNullOrUndefined(property)) {
                value = previousFormat;
                if (this.currentPropertyIndex < this.modifiedProperties.length) {
                    this.modifiedProperties[this.currentPropertyIndex] = format.cloneFormat();
                }
                else {
                    this.modifiedProperties[this.modifiedProperties.length - 1] = format.cloneFormat();
                }
                this.currentPropertyIndex++;
                return value;
            }
            if (property === 'listFormat') {
                value = new WParagraphFormat(undefined);
                value.copyFormat(previousFormat);
                previousFormat.listFormat = new WListFormat(previousFormat);
                previousFormat.listFormat.copyFormat(format.listFormat);
                this.currentPropertyIndex++;
                return value;
            }
            if (property === 'styleName') {
                if (!isNullOrUndefined(previousFormat.baseStyle)) {
                    value = new WParagraphStyle();
                    value.copyStyle(previousFormat.baseStyle);
                    this.currentPropertyIndex++;
                    if (!isNullOrUndefined(format.baseStyle)) {
                        previousFormat.baseStyle = new WParagraphStyle();
                        previousFormat.baseStyle.copyStyle(format.baseStyle);
                    }
                    return value;
                }
                else {
                    if (!isNullOrUndefined(format.baseStyle)) {
                        previousFormat.baseStyle = new WParagraphStyle();
                        previousFormat.baseStyle.copyStyle(format.baseStyle);
                    }
                    return undefined;
                }
            }
            if (property === 'borders') {
                value = previousFormat.borders.cloneFormat();
            }
            else if (this.action.indexOf('Border') >= 0) {
                value = previousFormat.borders.getBorder(property.replace('Border', ''));
            }
            else {
                value = previousFormat.getPropertyValue(property);
            }
            previousFormat.copyFormat(format);
            this.currentPropertyIndex++;
        }
        else {
            if (isNullOrUndefined(property)) {
                this.modifiedProperties.push(format.cloneFormat());
            }
            else {
                var currentFormat = new WParagraphFormat(undefined);
                currentFormat.copyFormat(format);
                this.modifiedProperties.push(currentFormat);
            }
        }
        return value;
    };
    BaseHistoryInfo.prototype.addModifiedPropertiesForContinueNumbering = function (paragraphFormat, value) {
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var previousFormat = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            value = previousFormat;
            if (this.currentPropertyIndex < this.modifiedProperties.length) {
                this.modifiedProperties[this.currentPropertyIndex] = paragraphFormat.cloneFormat();
            }
            else {
                this.modifiedProperties[this.modifiedProperties.length - 1] = paragraphFormat.cloneFormat();
            }
            this.currentPropertyIndex++;
            return value;
        }
        else {
            var currentFormat = new WParagraphFormat();
            currentFormat.copyFormat(paragraphFormat);
            this.modifiedProperties.push(currentFormat);
        }
        return value;
    };
    BaseHistoryInfo.prototype.addModifiedPropertiesForRestartNumbering = function (listFormat, value) {
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var listId = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            value = listId;
            if (this.currentPropertyIndex < this.modifiedProperties.length) {
                this.modifiedProperties[this.currentPropertyIndex] = listFormat.listId;
            }
            else {
                this.modifiedProperties[this.modifiedProperties.length - 1] = listFormat.listId;
            }
            this.currentPropertyIndex++;
            return value;
        }
        else {
            this.modifiedProperties.push(listFormat.listId);
        }
        return value;
    };
    BaseHistoryInfo.prototype.addModifiedPropertiesForList = function (listLevel) {
        var value;
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var previousLevel = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            value = previousLevel;
            previousLevel = new ModifiedLevel(listLevel, this.owner.editorModule.cloneListLevel(listLevel));
            if (this.currentPropertyIndex < this.modifiedProperties.length) {
                this.modifiedProperties[this.currentPropertyIndex] = previousLevel;
            }
            else {
                this.modifiedProperties[this.modifiedProperties.length - 1] = previousLevel;
            }
            this.currentPropertyIndex++;
        }
        else {
            this.modifiedProperties.push(new ModifiedLevel(listLevel, this.owner.editorModule.cloneListLevel(listLevel)));
            value = listLevel;
        }
        return value;
    };
    BaseHistoryInfo.prototype.revertProperties = function () {
        this.editorHistory.currentBaseHistoryInfo = this;
        this.currentPropertyIndex = 0;
        var property = this.getProperty();
        this.viewer.owner.editorModule.setOffsetValue(this.documentHelper.selection);
        if (this.action === 'ClearCharacterFormat' || this.modifiedProperties[0] instanceof WCharacterFormat) {
            if (this.action === 'ListCharacterFormat') {
                this.owner.editorModule.updateListCharacterFormat(this.documentHelper.selection, property, undefined);
                return;
            }
            this.owner.editorModule.updateSelectionCharacterFormatting(property, undefined, false);
        }
        else if (this.action === 'ClearParagraphFormat' || this.modifiedProperties[0] instanceof WParagraphFormat) {
            if (this.action === 'ContinueNumbering') {
                this.owner.editorModule.revertContinueNumbering(this.owner.selection, this.modifiedProperties[0]);
                return;
            }
            if (this.action === 'StyleName' && this.modifiedProperties[0] instanceof WParagraphFormat) {
                this.owner.editorModule.updateSelectionParagraphFormatting(property, this.modifiedProperties[0].baseStyle, false);
                return;
            }
            this.owner.editor.setPreviousBlockToLayout();
            this.owner.editorModule.updateSelectionParagraphFormatting(property, undefined, false);
        }
        else if (this.action === 'LinkToPrevious' && this.modifiedProperties[0] instanceof WSectionFormat) {
            var sectionIndex = parseInt(this.selectionStart.split(';')[0]);
            this.owner.editorModule.updateHeaderFooters(property, undefined, sectionIndex, this.modifiedProperties[0].removedHeaderFooters[0]);
        }
        else if (this.modifiedProperties[0] instanceof WSectionFormat) {
            this.owner.editorModule.updateSectionFormat(property, undefined);
        }
        else if (this.action === 'RestartNumbering') {
            this.owner.editorModule.restartListAtInternal(this.owner.selection, this.modifiedProperties[0]);
            return;
        }
        else if (this.modifiedProperties[0] instanceof ImageInfo) {
            this.owner.selection.updateImageSize(this.modifiedProperties[0]);
        }
        else if (this.modifiedProperties[0] instanceof ModifiedLevel) {
            var modified = new Dictionary();
            for (var i = 0; i < this.modifiedProperties.length; i++) {
                var modifiedLevel = this.modifiedProperties[i];
                // modified.modifiedLevels.add(modifiedLevel.ownerListLevel.levelNumber, modifiedLevel);
                modified.add(i, modifiedLevel);
            }
            this.editorHistory.updateListChanges(modified);
            modified.destroy();
            modified = undefined;
        }
        else if (this.modifiedProperties[0] instanceof WTableFormat) {
            this.owner.editorModule.updateTableFormat(this.owner.selection, property, undefined);
        }
        else if (this.modifiedProperties[0] instanceof WCellFormat) {
            this.owner.isShiftingEnabled = true;
            this.owner.editorModule.updateCellFormat(this.owner.selection, property, undefined);
        }
        else if (this.modifiedProperties[0] instanceof WRowFormat) {
            this.owner.editorModule.updateRowFormat(this.owner.selection, property, undefined);
        }
        this.currentPropertyIndex = 0;
        if (this.action === 'ClearCharacterFormat' || this.action === 'ClearParagraphFormat') {
            this.owner.editorModule.getOffsetValue(this.documentHelper.selection);
        }
    };
    BaseHistoryInfo.prototype.addModifiedCellOptions = function (applyFormat, format, table) {
        var currentFormat;
        if (isNullOrUndefined(applyFormat.bottomMargin) && isNullOrUndefined(applyFormat.topMargin)
            && isNullOrUndefined(applyFormat.rightMargin) && isNullOrUndefined(applyFormat.leftMargin)) {
            currentFormat = this.copyCellOptions(table.tableFormat);
        }
        else {
            currentFormat = this.copyCellOptions(applyFormat);
        }
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var previousFormat = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            format = previousFormat;
            if (this.currentPropertyIndex < this.modifiedProperties.length) {
                this.modifiedProperties[this.currentPropertyIndex] = this.copyCellOptions(applyFormat);
            }
            else {
                this.modifiedProperties[this.modifiedProperties.length - 1] = this.copyCellOptions(applyFormat);
            }
            this.currentPropertyIndex++;
            return format;
        }
        else {
            this.modifiedProperties.push(currentFormat);
        }
        return format;
    };
    BaseHistoryInfo.prototype.copyCellOptions = function (format) {
        var cellFormat = new WCellFormat();
        cellFormat.topMargin = format.topMargin;
        cellFormat.rightMargin = format.rightMargin;
        cellFormat.bottomMargin = format.bottomMargin;
        cellFormat.leftMargin = format.leftMargin;
        return cellFormat;
    };
    BaseHistoryInfo.prototype.addModifiedTableOptions = function (format) {
        var currentFormat = this.copyTableOptions(format);
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var previousFormat = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            if (this.currentPropertyIndex < this.modifiedProperties.length) {
                this.modifiedProperties.splice(this.currentPropertyIndex, 1, currentFormat);
            }
            else {
                this.modifiedProperties.splice(this.modifiedProperties.length - 1, 1, currentFormat);
            }
            this.currentPropertyIndex++;
        }
        else {
            this.modifiedProperties.push(currentFormat);
        }
    };
    BaseHistoryInfo.prototype.copyTableOptions = function (format) {
        var tableFormat = new WTableFormat();
        tableFormat.topMargin = format.topMargin;
        tableFormat.rightMargin = format.rightMargin;
        tableFormat.bottomMargin = format.bottomMargin;
        tableFormat.leftMargin = format.leftMargin;
        tableFormat.cellSpacing = format.cellSpacing;
        return tableFormat;
    };
    BaseHistoryInfo.prototype.getProperty = function () {
        switch (this.action) {
            case 'Bold':
                return 'bold';
            case 'Italic':
                return 'italic';
            case 'FontColor':
                return 'fontColor';
            case 'FontFamily':
                return 'fontFamily';
            case 'FontSize':
                return 'fontSize';
            case 'HighlightColor':
                return 'highlightColor';
            case 'BaselineAlignment':
                return 'baselineAlignment';
            case 'Strikethrough':
                return 'strikethrough';
            case 'Underline':
                return 'underline';
            case 'AfterSpacing':
                return 'afterSpacing';
            case 'BeforeSpacing':
                return 'beforeSpacing';
            case 'LeftIndent':
                return 'leftIndent';
            case 'RightIndent':
                return 'rightIndent';
            case 'FirstLineIndent':
                return 'firstLineIndent';
            case 'LineSpacingType':
                return 'lineSpacingType';
            case 'LineSpacing':
                return 'lineSpacing';
            case 'TextAlignment':
                return 'textAlignment';
            case 'ListFormat':
                return 'listFormat';
            case 'PageHeight':
                return 'pageHeight';
            case 'PageWidth':
                return 'pageWidth';
            case 'DifferentOddAndEvenPages':
                return 'differentOddAndEvenPages';
            case 'TableAlignment':
                return 'tableAlignment';
            case 'TableLeftIndent':
                return 'leftIndent';
            case 'DefaultCellSpacing':
                return 'cellSpacing';
            case 'LeftMargin':
            case 'CellLeftMargin':
            case 'DefaultCellLeftMargin':
                return 'leftMargin';
            case 'RightMargin':
            case 'CellRightMargin':
            case 'DefaultCellRightMargin':
                return 'rightMargin';
            case 'TopMargin':
            case 'CellTopMargin':
            case 'DefaultCellTopMargin':
                return 'topMargin';
            case 'BottomMargin':
            case 'CellBottomMargin':
            case 'DefaultCellBottomMargin':
                return 'bottomMargin';
            case 'CellContentVerticalAlignment':
                return 'verticalAlignment';
            case 'RowHeight':
                return 'height';
            case 'RowHeightType':
                return 'heightType';
            case 'RowHeader':
                return 'isHeader';
            case 'AllowBreakAcrossPages':
                return 'allowBreakAcrossPages';
            case 'TablePreferredWidth':
            case 'CellPreferredWidth':
                return 'preferredWidth';
            case 'TablePreferredWidthType':
            case 'CellPreferredWidthType':
                return 'preferredWidthType';
            case 'Shading':
                return 'shading';
            case 'StyleName':
                return 'styleName';
            case 'ParagraphBidi':
            case 'TableBidi':
                return 'bidi';
            case 'ContextualSpacing':
                return 'contextualSpacing';
            case 'LinkToPrevious':
                return 'linkToPrevious';
            case 'LeftBorder':
            case 'TopBorder':
            case 'RightBorder':
            case 'BottomBorder':
            case 'HorizontalBorder':
            case 'VerticalBorder':
            case 'Borders':
                return (this.action[0].toLowerCase() + this.action.slice(1));
        }
        return undefined;
    };
    BaseHistoryInfo.prototype.getCharacterPropertyValue = function (property, modifiedProperty) {
        var value;
        if (property === 'bold') {
            value = modifiedProperty.bold;
        }
        else if (property === 'italic') {
            value = modifiedProperty.italic;
        }
        else if (property === 'fontColor') {
            value = modifiedProperty.fontColor;
        }
        else if (property === 'fontFamily') {
            value = modifiedProperty.fontFamily;
        }
        else if (property === 'fontSize') {
            value = modifiedProperty.fontSize;
        }
        else if (property === 'highlightColor') {
            value = modifiedProperty.highlightColor;
        }
        else if (property === 'baselineAlignment') {
            value = modifiedProperty.baselineAlignment;
        }
        else if (property === 'strikethrough') {
            value = modifiedProperty.strikethrough;
        }
        else if (property === 'underline') {
            value = modifiedProperty.underline;
        }
        return value;
    };
    BaseHistoryInfo.prototype.addModifiedTableProperties = function (format, property, value) {
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var previousTableFormat = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            if (isNullOrUndefined(property)) {
                value = previousTableFormat;
                if (this.currentPropertyIndex < this.modifiedProperties.length) {
                    this.modifiedProperties[this.currentPropertyIndex] = format.cloneFormat();
                }
                else {
                    this.modifiedProperties[this.modifiedProperties.length - 1] = format.cloneFormat();
                }
                this.currentPropertyIndex++;
                return value;
            }
            if (property === 'shading') {
                value = previousTableFormat.shading;
            }
            else {
                value = previousTableFormat.getPropertyValue(property);
            }
            previousTableFormat.copyFormat(format);
            this.currentPropertyIndex++;
        }
        else {
            var currentFormat = new WTableFormat();
            currentFormat.copyFormat(format);
            this.modifiedProperties.push(currentFormat);
        }
        return value;
    };
    BaseHistoryInfo.prototype.addModifiedRowProperties = function (rowFormat, property, value) {
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var previousFormat = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            if (isNullOrUndefined(property)) {
                value = previousFormat;
                if (this.currentPropertyIndex < this.modifiedProperties.length) {
                    this.modifiedProperties[this.currentPropertyIndex] = rowFormat.cloneFormat();
                }
                else {
                    this.modifiedProperties[this.modifiedProperties.length - 1] = rowFormat.cloneFormat();
                }
                this.currentPropertyIndex++;
                return value;
            }
            value = previousFormat.getPropertyValue(property);
            previousFormat.copyFormat(rowFormat);
            this.currentPropertyIndex++;
        }
        else {
            var currentFormat = new WRowFormat();
            currentFormat.copyFormat(rowFormat);
            this.modifiedProperties.push(currentFormat);
        }
        return value;
    };
    BaseHistoryInfo.prototype.addModifiedCellProperties = function (cellFormat, property, value) {
        if (this.editorHistory.isUndoing || this.editorHistory.isRedoing) {
            var previousFormat = (this.currentPropertyIndex < this.modifiedProperties.length ? this.modifiedProperties[this.currentPropertyIndex] : this.modifiedProperties[this.modifiedProperties.length - 1]);
            if (isNullOrUndefined(property)) {
                value = previousFormat;
                if (this.currentPropertyIndex < this.modifiedProperties.length) {
                    this.modifiedProperties[this.currentPropertyIndex] = cellFormat.cloneFormat();
                }
                else {
                    this.modifiedProperties[this.modifiedProperties.length - 1] = cellFormat.cloneFormat();
                }
                this.currentPropertyIndex++;
                return value;
            }
            if (property === 'shading') {
                value = previousFormat.shading;
            }
            else {
                value = previousFormat.getPropertyValue(property);
            }
            previousFormat.copyFormat(cellFormat);
            this.currentPropertyIndex++;
        }
        else {
            var currentFormat = new WCellFormat();
            currentFormat.copyFormat(cellFormat);
            this.modifiedProperties.push(currentFormat);
        }
        return value;
    };
    /**
     * @private
     * @returns {void}
     */
    BaseHistoryInfo.prototype.destroy = function () {
        this.selectionStart = undefined;
        this.selectionEnd = undefined;
        this.insertPosition = undefined;
        this.endPosition = undefined;
        if (!isNullOrUndefined(this.modifiedNodeLength)) {
            this.modifiedNodeLength = [];
            this.modifiedNodeLength = undefined;
        }
        if (!isNullOrUndefined(this.modifiedProperties)) {
            for (var i = 0; i < this.modifiedProperties.length; i++) {
                var property = this.modifiedProperties[i];
                if (property instanceof WCharacterFormat) {
                    property.destroy();
                }
                else if (property instanceof WParagraphFormat) {
                    property.destroy();
                }
                else if (property instanceof WSectionFormat) {
                    property.destroy();
                }
                else if (property instanceof ModifiedLevel) {
                    property.destroy();
                }
                this.modifiedProperties.splice(this.modifiedProperties.indexOf(property), 1);
                i--;
            }
            this.modifiedPropertiesIn = undefined;
        }
        if (!isNullOrUndefined(this.removedNodes)) {
            for (var i = 0; i < this.removedNodes.length; i++) {
                var node = this.removedNodes[i];
                if (node instanceof ParagraphWidget) {
                    node.destroyInternal(this.viewer);
                }
                else if (node instanceof ElementBox && !(node instanceof CommentCharacterElementBox)) {
                    node.destroy();
                }
                this.removedNodes.splice(this.removedNodes.indexOf(node), 1);
                i--;
            }
            this.removedNodesIn = undefined;
        }
        if (!isNullOrUndefined(this.insertedNodes)) {
            for (var i = 0; i < this.insertedNodes.length; i++) {
                var node = this.insertedNodes[i];
                if (node instanceof ParagraphWidget) {
                    node.destroyInternal(this.viewer);
                }
                else if (node instanceof ElementBox && !(node instanceof CommentCharacterElementBox)) {
                    node.destroy();
                }
                this.insertedNodes.splice(this.insertedNodes.indexOf(node), 1);
                i--;
            }
            this.insertedNodes = undefined;
        }
        this.ownerIn = undefined;
    };
    /**
     * @private
     */
    BaseHistoryInfo.prototype.getDeleteOperationsForTrackChanges = function () {
        var operations = [];
        for (var i = this.removedNodes.length - 1; i >= 0; i--) {
            var element = this.removedNodes[i];
            var operation = this.getDeleteOperationForTrackChanges(element);
            if (!isNullOrUndefined(operation)) {
                operations.push(operation);
            }
        }
        return operations.reverse();
    };
    /**
     * @private
     */
    BaseHistoryInfo.prototype.getDeleteOperationForTrackChanges = function (element) {
        var operation;
        if (element instanceof TextElementBox || element instanceof ImageElementBox || element instanceof FieldElementBox || element instanceof BookmarkElementBox) {
            if (element.removedIds.length === 0) {
                operation = this.getFormatOperation(element);
            }
            else if (element.removedIds.length > 0) {
                var revisionId = element.removedIds[0];
                var revision = this.owner.editor.getRevision(revisionId);
                var currentUser = this.owner.currentUser === '' ? 'Guest user' : this.owner.currentUser;
                if (revision.revisionType === 'Insertion' && revision.author !== currentUser) {
                    operation = this.getFormatOperation(element);
                }
                else if (revision.revisionType === 'Insertion') {
                    operation = this.getDeleteOperation(this.action, undefined, this.getRemovedText(element));
                }
                else if (revision.revisionType === 'Deletion') {
                    if (revision.author !== currentUser) {
                        operation = this.getFormatOperation(element);
                        if (element.removedIds.length > 0) {
                            for (var i = 0; i < element.removedIds.length; i++) {
                                if (!isNullOrUndefined(operation.markerData.removedIds)) {
                                    operation.markerData.removedIds = [];
                                }
                                operation.markerData.removedIds.push(element.removedIds[i]);
                            }
                        }
                    }
                }
            }
        }
        else if (element instanceof ParagraphWidget) {
            if (element.characterFormat.revisions.length > 0) {
                operation = this.getDeleteOperation(this.action);
            }
            else if (element.characterFormat.removedIds.length > 0) {
                operation = this.getDeleteOperation(this.action);
            }
            else if (element.characterFormat.revisions.length === 0) {
                operation = this.getFormatOperation();
            }
            this.startIndex++;
        }
        if (this.action !== 'Enter' && !(element instanceof ParagraphWidget) && (isNullOrUndefined(operation) || operation.action !== 'Delete')) {
            this.startIndex += element.length;
        }
        return operation;
    };
    /**
     * @private
     */
    BaseHistoryInfo.prototype.getActionInfo = function (isInvertOperation) {
        var action = this.action;
        if (!isNullOrUndefined(this.isAcceptOrReject) && this.isAcceptOrReject === 'Reject') {
            action = 'Reject Change';
        }
        var operations = [];
        switch (action.toString()) {
            case 'Insert':
            case 'Enter':
            case 'InsertInline':
            case 'SectionBreak':
            case 'SectionBreakContinuous':
                if (this.removedNodes.length > 0 && isNullOrUndefined(this.dropDownIndex)) {
                    if (this.owner.enableTrackChanges) {
                        operations = this.getDeleteOperationsForTrackChanges();
                        if (action !== 'InsertInline') {
                            this.insertIndex = this.startIndex;
                        }
                    }
                    else {
                        operations.push(this.getDeleteOperation(action));
                    }
                }
                if (action === 'Enter' || this.insertedText.length > 0) {
                    var operation_1 = this.getInsertOperation(action);
                    if (this.owner.enableTrackChanges && this.action !== 'Enter') {
                        if (this.insertedElement instanceof FootnoteElementBox) {
                            operation_1.markerData = this.markerData[0];
                            this.markerData.splice(0, 1);
                            operation_1.text = CONTROL_CHARACTERS.Marker_Start;
                            operation_1.markerData.type = this.insertedElement.footnoteType;
                            operation_1.markerData.revisionForFootnoteEndnoteContent = this.markerData.pop();
                        }
                        // if (action === 'InsertInline') {
                        operations.push(operation_1);
                        // }
                        // else {
                        //     operations.splice(0, 0, operation);
                        // }
                        for (var i = 0; i < this.splittedRevisions.length; i++) {
                            if (isNullOrUndefined(operation_1.markerData)) {
                                operation_1.markerData = {};
                            }
                            if (isNullOrUndefined(operation_1.markerData.splittedRevisions)) {
                                operation_1.markerData.splittedRevisions = [];
                            }
                            operation_1.markerData.splittedRevisions.push(this.splittedRevisions[i]);
                        }
                    }
                    else {
                        operations.push(operation_1);
                    }
                }
                var operation2 = operations[operations.length - 1];
                if (action === 'Insert' && !isNullOrUndefined(operation2.text)) {
                    operation2.length = operation2.text.length;
                }
                if (!isNullOrUndefined(this.dropDownIndex)) {
                    operation2.markerData = { 'type': 'Field', 'dropDownIndex': this.dropDownIndex };
                    operation2.offset = this.getElementAbsolutePosition(this.fieldBegin);
                    operation2.type = 'DropDown';
                }
                break;
            case 'InsertTable':
            case 'InsertTableBelow':
            case 'InsertRowAbove':
            case 'InsertRowBelow':
                if (this.removedNodes.length > 0 && (action === 'InsertTable' || action === 'InsertTableBelow')) {
                    operations.push(this.getDeleteOperation(action));
                }
                var tableRowOperation = this.buildTableRowCellOperation(action);
                for (var i = 0; i < tableRowOperation.length; i++) {
                    operations.push(tableRowOperation[i]);
                }
                break;
            case 'InsertColumnLeft':
            case 'InsertColumnRight':
                var tableCellOperation = this.buildTableRowCellOperation(action);
                operations = tableCellOperation.reverse().slice();
                break;
            case 'BackSpace':
            case 'Delete':
            case 'Cut':
            case 'DeleteBookmark':
            case 'RemoveEditRange':
                if (this.removedNodes.length > 0) {
                    if (this.owner.enableTrackChanges) {
                        operations = this.getDeleteOperationsForTrackChanges();
                    }
                    else {
                        var deleteOperation_1 = this.getDeleteOperation(action);
                        operations.push(deleteOperation_1);
                        for (var i = 0; i < this.removedNodes.length; i++) {
                            var element = this.removedNodes[i];
                            if (element instanceof BodyWidget) {
                                var headersFooters = element.removedHeaderFooters;
                                for (var j = 0; j < headersFooters.length; j++) {
                                    var headerFooter = headersFooters[j];
                                    var keysLength = Object.keys(headerFooter).length;
                                    if (keysLength > 0) {
                                        operations.push(this.getDeleteOperation('DeleteHeaderFooter', undefined));
                                        break;
                                    }
                                }
                            }
                        }
                        if (action === 'DeleteBookmark' || action === 'RemoveEditRange') {
                            operations.push(this.getDeleteOperation(action, true));
                            if (action === 'RemoveEditRange') {
                                var operation_2 = operations[operations.length - 1];
                                operation_2.offset -= 1;
                            }
                        }
                    }
                }
                break;
            case 'ResolveComment':
            case 'EditComment':
                for (var i = 0; i < this.removedNodes.length; i++) {
                    var operation_3 = this.getUpdateOperation();
                    operations.push(this.getCommentOperation(operation_3, this.removedNodes[i]));
                }
                break;
            case 'ClearRevisions':
            case 'TrackingPageBreak':
                if (this.removedNodes.length > 0) {
                    var revision = this.owner.editor.getRevision(this.removedNodes[0]);
                    if (action === 'TrackingPageBreak') {
                        if (!(typeof this.removedNodes[0] === 'string')) {
                            var operation_4 = this.getDeleteOperation(action);
                            operation_4.markerData.isAcceptOrReject = 'Reject';
                            operations.push(operation_4);
                            break;
                        }
                    }
                    if (revision.revisionType === 'Insertion') {
                        // Accept operation - Insertion
                        this.markerData.push(this.owner.editor.getMarkerData(undefined, undefined, revision, 'Accept'));
                        var operation_5 = this.getFormatOperation();
                        operations.push(operation_5);
                    }
                    else if (revision.revisionType === 'Deletion') {
                        // Reject operation - Deletion
                        this.markerData.push(this.owner.editor.getMarkerData(undefined, undefined, revision, 'Reject'));
                        var operation_6 = this.getFormatOperation();
                        operations.push(operation_6);
                    }
                }
                break;
            case 'Reject Change':
                var operation = this.getDeleteOperation(action);
                operation.markerData.isAcceptOrReject = 'Reject';
                operations.push(operation);
                break;
            case 'Accept Change':
                var deleteOperation = this.getDeleteOperation(action);
                deleteOperation.markerData.isAcceptOrReject = 'Accept';
                operations.push(deleteOperation);
                break;
            case 'Paste':
                if (this.removedNodes.length > 0) {
                    operations.push(this.getDeleteOperation(action));
                }
                else {
                    var pasteOperation = {
                        action: 'Insert',
                        offset: this.startIndex,
                        length: this.getPasteContentLength(),
                        pasteContent: JSON.stringify(this.pasteContent),
                        type: this.type
                    };
                    operations.push(pasteOperation);
                }
                break;
            case 'InsertHyperlink':
                if (this.isEditHyperlink) {
                    operations = this.getEditHyperlinkOperation();
                }
                else {
                    operations = this.getFieldOperation();
                }
                break;
            case 'UpdateFormField':
                this.insertedText = '';
                var operation1 = this.getInsertOperation('UpdateFormField');
                operation1.text = CONTROL_CHARACTERS.Marker_Start;
                operation1.markerData = { 'type': 'Field', 'checkBoxValue': this.fieldBegin.formFieldData.checked };
                operation1.offset = this.getElementAbsolutePosition(this.fieldBegin);
                ;
                operations.push(operation1);
                break;
            case 'DeleteRow':
            case 'DeleteCells':
            case 'DeleteColumn':
            case 'DeleteTable':
            case 'ClearCells':
            case 'MergeCells':
                if (this.removedNodes.length > 0) {
                    if (this.cellOperation.length > 0) {
                        // For delete column and delete cell.
                        for (var i = 0; i < this.cellOperation.length; i++) {
                            operations.push(this.cellOperation[i]);
                        }
                        if (action === 'MergeCells') {
                            operations.push(this.getPasteMergeOperation());
                            operations.push(this.getFormatOperation());
                        }
                        else {
                            operations.reverse();
                        }
                    }
                    else {
                        if (this.owner.enableTrackChanges) {
                            operations.push(this.getFormatOperation(undefined, action));
                        }
                        else {
                            operations.push(this.getDeleteOperation(action));
                        }
                    }
                }
                break;
            case 'RemoveRowTrack':
                if (this.removedNodes.length > 0) {
                    if (this.removedNodes[0] instanceof TableWidget) {
                        // this.afterInsertTableRrevision();
                        operations = this.cellOperation.slice();
                    }
                }
                break;
            case 'RowResizing':
            case 'CellResizing':
                operations = this.getResizingOperation(action);
                break;
            case 'ImageResizing':
                operations.push(this.getFormatOperation());
                break;
            case 'Bold':
            case 'Italic':
            case 'Underline':
            case 'FontSize':
            case 'Strikethrough':
            case 'BaselineAlignment':
            case 'HighlightColor':
            case 'FontColor':
            case 'FontFamily':
            case 'Uppercase':
            case 'CharacterFormat':
                var charFormatOperation = this.buildFormatOperation(action, true, false);
                operations = charFormatOperation.slice();
                break;
            case 'AfterSpacing':
            case 'BeforeSpacing':
            case 'RightIndent':
            case 'LeftIndent':
            case 'FirstLineIndent':
            case 'LineSpacing':
            case 'LineSpacingType':
            case 'TextAlignment':
            case 'Borders':
            case 'TopBorder':
            case 'BottomBorder':
            case 'LeftBorder':
            case 'RightBorder':
            case 'HorizontalBorder':
            case 'VerticalBorder':
            case 'ListFormat':
            case 'ParagraphFormat':
            case 'StyleName':
            case 'ClearParagraphFormat':
            case 'SpaceBeforeAuto':
            case 'SpaceAfterAuto':
            case 'ParagraphBidi':
            case 'ContextualSpacing':
            case 'ContinueNumbering':
                if (action === 'ContinueNumbering') {
                    this.type = action.toString();
                }
                if (action === 'Borders' && this.removedNodes[this.removedNodes.length - 1] instanceof TableWidget) {
                    this.insertedText = CONTROL_CHARACTERS.Cell;
                    this.createCellFormat(action);
                    operations = this.getSelectedCellOperation(action, undefined, true);
                    break;
                }
                var paraFormatOperation = this.buildFormatOperation(action, false, false);
                operations = paraFormatOperation.slice();
                break;
            case 'TableAlignment':
            case 'DefaultCellSpacing':
            case 'TableLeftIndent':
            case 'DefaultCellLeftMargin':
            case 'DefaultCellRightMargin':
            case 'DefaultCellTopMargin':
            case 'DefaultCellBottomMargin':
            case 'TablePreferredWidth':
            case 'TablePreferredWidthType':
            case 'TableBidi':
                this.createTableFormat(action);
                operations.push(this.getFormatOperation());
                this.insertedTableFormat = undefined;
                break;
            case 'RestartNumbering':
                this.type = action.toString();
                var numberingOperation = this.getFormatOperation(undefined, action);
                this.createListFormat(action, numberingOperation);
                operations.push(numberingOperation);
                break;
            case 'Shading':
                this.createCellFormat(action);
                operations = this.getSelectedCellOperation(action, undefined, undefined, true);
                break;
            case 'TableAutoFitToContents':
            case 'TableAutoFitToWindow':
            case 'TableFixedColumnWidth':
                this.createTableFormat(action);
                operations.push(this.getFormatOperation(undefined, action));
                this.insertedTableFormat = undefined;
                break;
            case 'SectionFormat':
            case 'HeaderDistance':
            case 'FooterDistance':
            case 'DifferentFirstPage':
            case 'DifferentOddAndEvenPages':
            case 'PageWidth':
            case 'PageHeight':
            case 'LeftMargin':
            case 'TopMargin':
            case 'RightMargin':
            case 'BottomMargin':
            case 'RestartPageNumbering':
            case 'PageStartingNumber':
            case 'EndnoteNumberFormat':
            case 'FootNoteNumberFormat':
            case 'RestartIndexForEndnotes':
            case 'RestartIndexForFootnotes':
            case 'InitialFootNoteNumber':
            case 'InitialEndNoteNumber':
            case 'LineBetweenColumns':
            case 'EqualWidth':
            case 'BreakCode':
            case 'LinkToPrevious':
                this.createSectionFormat(action);
                operations.push(this.getFormatOperation(undefined, action));
                if (action === 'LinkToPrevious') {
                    var operation_7 = operations[operations.length - 1];
                    operation_7.offset = this.insertIndex;
                }
                break;
            case 'RowHeight':
            case 'RowHeightType':
            case 'AllowBreakAcrossPages':
            case 'RowHeader':
                this.createRowFormat(action);
                operations.push(this.getFormatOperation(undefined, action));
                this.insertedRowFormat = undefined;
                break;
            case 'CellContentVerticalAlignment':
            case 'CellLeftMargin':
            case 'CellRightMargin':
            case 'CellBottomMargin':
            case 'CellTopMargin':
            case 'CellPreferredWidth':
            case 'Shading':
            case 'CellPreferredWidthType':
                this.createCellFormat(action);
                operations = this.getSelectedCellOperation(action).slice();
                this.insertedCellFormat = undefined;
                break;
        }
        return operations;
    };
    /**
     * @private
     */
    BaseHistoryInfo.prototype.getElementAbsolutePosition = function (element) {
        if (element) {
            var offset = element.line.getOffset(element, element.length);
            var startOffset = this.owner.selection.getHierarchicalIndex(element.line.paragraph, offset.toString());
            var startIndex = this.owner.selection.getAbsolutePositionFromRelativePosition(startOffset);
            return startIndex;
        }
        return undefined;
    };
    /**
     * @private
     */
    BaseHistoryInfo.prototype.getFieldOperation = function () {
        var operations = [];
        var element = this.fieldBegin;
        var isFieldEnd = false;
        var elementOffset = this.insertIndex;
        if (!isNullOrUndefined(element)) {
            do {
                var insertedText = void 0;
                var Data = void 0;
                var elementLength = void 0;
                var characterFormat = void 0;
                var type = void 0;
                if (element instanceof FieldElementBox) {
                    if (element.fieldType === 0 && this.getRemovedText() !== '') {
                        operations.push(this.getDeleteOperation('Delete'));
                        var operation_8 = operations[operations.length - 1];
                        operation_8.offset = elementOffset;
                        if (!isNullOrUndefined(operation_8.markerData) && this.owner.enableTrackChanges) {
                            operation_8.markerData.isSkipTracking = true;
                        }
                    }
                    insertedText = element.fieldType === 0 ? CONTROL_CHARACTERS.Marker_Start : element.fieldType === 1 ? CONTROL_CHARACTERS.Marker_End : element.fieldType === 2 ? CONTROL_CHARACTERS.Field_Separator : '';
                    if (element.fieldType === 0 && element.formFieldData) {
                        type = this.formFieldType;
                        Data = this.markerData.pop();
                        if (isNullOrUndefined(Data)) {
                            Data = {};
                        }
                        Data.type = 'Field';
                        Data.formFieldData = element.formFieldData;
                    }
                    else {
                        Data = this.markerData.pop();
                        if (isNullOrUndefined(Data)) {
                            Data = {};
                        }
                        Data.type = 'Field';
                    }
                    elementLength = element.length;
                }
                else if (this.fieldBegin.formFieldData && element instanceof BookmarkElementBox) {
                    insertedText = element.bookmarkType === 0 ? CONTROL_CHARACTERS.Marker_Start : CONTROL_CHARACTERS.Marker_End;
                    Data = { 'bookmarkName': element.name, 'type': 'Bookmark' };
                    elementLength = element.length;
                }
                else if (element instanceof TextElementBox) {
                    insertedText = element.text;
                    elementLength = element.length;
                    Data = this.markerData.pop();
                }
                if (!(element instanceof BookmarkElementBox)) {
                    var characterData = {};
                    HelperMethods.writeCharacterFormat(characterData, false, element.characterFormat);
                    characterFormat = JSON.stringify(characterData);
                }
                var operation = {
                    action: 'Insert',
                    offset: elementOffset,
                    type: type,
                    text: insertedText,
                    length: elementLength,
                    markerData: Data,
                    characterFormat: characterFormat
                };
                operations.push(operation);
                elementOffset += element.length;
                Data = undefined;
                type = undefined;
                characterFormat = undefined;
                if (element instanceof FieldElementBox && element.fieldType === 1) {
                    isFieldEnd = true;
                    if (this.fieldBegin.formFieldData && element.nextNode instanceof BookmarkElementBox) {
                        var elementBox = element.nextNode;
                        insertedText = elementBox.bookmarkType === 0 ? CONTROL_CHARACTERS.Marker_Start : CONTROL_CHARACTERS.Marker_End;
                        Data = { 'bookmarkName': elementBox.name, 'type': 'Bookmark' };
                        elementLength = elementBox.length;
                        var operation_9 = {
                            action: 'Insert',
                            offset: elementOffset,
                            text: insertedText,
                            length: elementLength,
                            markerData: Data
                        };
                        operations.push(operation_9);
                    }
                }
                element = element.nextNode;
            } while (!isFieldEnd && !isNullOrUndefined(element));
        }
        return operations;
    };
    BaseHistoryInfo.prototype.getEditHyperlinkOperation = function () {
        var operations = [];
        var element = this.fieldBegin;
        if (element) {
            var startIndex = this.getElementAbsolutePosition(element);
            operations.push(this.getDeleteOperation('Delete'));
            var operation = operations[operations.length - 1];
            operation.offset = startIndex;
            var fieldCode = this.getRemovedFieldCode();
            operation.length = fieldCode.length;
            operation.text = fieldCode;
            operations.push(this.getInsertOperation('InsertHyperlink'));
            operation = operations[operations.length - 1];
            operation.offset = startIndex;
            fieldCode = this.owner.selection.getFieldCode(element);
            ;
            operation.text = fieldCode;
            operation.length = fieldCode.length;
        }
        return operations;
    };
    BaseHistoryInfo.prototype.getPasteContentLength = function () {
        var length = 0;
        for (var i = 0; i < this.insertedNodes.length; i++) {
            var block = this.insertedNodes[i];
            length += this.owner.selection.getBlockLength(undefined, block, 0, { done: false }, true, undefined, undefined);
        }
        return length;
    };
    /**
     * @private
     * @returns {Operation}
     */
    BaseHistoryInfo.prototype.getUpdateOperation = function () {
        var operation = {
            action: 'Update'
        };
        return operation;
    };
    BaseHistoryInfo.prototype.getResizingOperation = function (action) {
        var operations = [];
        var tableResize = this.owner.editor.tableResize;
        var table = tableResize.currentResizingTable;
        if (!isNullOrUndefined(table.childWidgets)) {
            table = table.combineWidget(this.owner.viewer);
            var resizerPosition = tableResize.resizerPosition;
            var paragraphInfo = { 'paragraph': null, 'offset': 0 };
            if (action == 'RowResizing') {
                var row = table.childWidgets[resizerPosition];
                this.insertIndex = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, row).position;
                var rowFormat = {};
                if (!isNullOrUndefined(this.owner.sfdtExportModule)) {
                    this.owner.sfdtExportModule.assignRowFormat(rowFormat, row.rowFormat, 0);
                }
                this.insertedRowFormat = JSON.stringify(rowFormat);
                this.insertedText = CONTROL_CHARACTERS.Row;
                operations.push(this.getFormatOperation());
            }
            else {
                var rightColumnIndex = resizerPosition;
                var leftColumnIndex = resizerPosition - 1;
                this.insertedText = CONTROL_CHARACTERS.Cell;
                var tableFormat = {};
                tableFormat = this.owner.sfdtExportModule ? this.owner.sfdtExportModule.writeTableFormat(table.tableFormat, 0) : {};
                this.insertedTableFormat = JSON.stringify(tableFormat);
                if (!this.owner.selection.isEmpty) {
                    var selectedCells = this.owner.selection.getSelectedCells();
                    var startCell = selectedCells[0];
                    var endCell = selectedCells[selectedCells.length - 1];
                    var rowStartIndex = table.childWidgets.indexOf(startCell.ownerRow);
                    var count = table.childWidgets.indexOf(endCell.ownerRow);
                    var row = table.childWidgets[rowStartIndex];
                    while (row && row.index <= count) {
                        var cell = row.firstChild;
                        while (cell) {
                            if (cell.index == rightColumnIndex || cell.index == leftColumnIndex) {
                                var rowFormat = {};
                                var cellFormat = {};
                                if (!isNullOrUndefined(this.owner.sfdtExportModule)) {
                                    this.owner.sfdtExportModule.assignRowFormat(rowFormat, row.rowFormat, 0);
                                    cellFormat = this.owner.sfdtExportModule.writeCellFormat(cell.cellFormat, 0);
                                }
                                this.insertIndex = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, cell).position;
                                this.insertedCellFormat = JSON.stringify(cellFormat);
                                this.insertedRowFormat = JSON.stringify(rowFormat);
                                operations.push(this.getFormatOperation());
                            }
                            cell = cell.nextWidget;
                        }
                        row = row.getSplitWidgets().pop().nextRenderedWidget;
                    }
                }
                else {
                    var row = table.firstChild;
                    while (row) {
                        var cell = row.firstChild;
                        while (cell) {
                            if (cell.index == rightColumnIndex || cell.index == leftColumnIndex) {
                                var rowFormat = {};
                                var cellFormat = {};
                                if (!isNullOrUndefined(this.owner.sfdtExportModule)) {
                                    this.owner.sfdtExportModule.assignRowFormat(rowFormat, row.rowFormat, 0);
                                    cellFormat = this.owner.sfdtExportModule.writeCellFormat(cell.cellFormat, 0);
                                }
                                this.insertIndex = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, cell).position;
                                this.insertedCellFormat = JSON.stringify(cellFormat);
                                this.insertedRowFormat = JSON.stringify(rowFormat);
                                operations.push(this.getFormatOperation());
                            }
                            cell = cell.nextWidget;
                        }
                        row = row.getSplitWidgets().pop().nextRenderedWidget;
                    }
                }
            }
            this.owner.documentHelper.layout.reLayoutTable(table);
        }
        this.insertedCellFormat = undefined;
        this.insertedRowFormat = undefined;
        this.insertedTableFormat = undefined;
        return operations;
    };
    /**
     * @private
     * @returns {Operation}
     */
    BaseHistoryInfo.prototype.getDeleteOperation = function (action, setEndIndex, text) {
        // if (action === 'Delete' && this.endIndex === this.startIndex) {
        //     this.startIndex++;
        //     this.endIndex++;
        // }
        if (action === 'Delete' && this.endIndex < this.startIndex) {
            var start = this.startIndex;
            this.startIndex = this.endIndex;
            this.endIndex = start;
        }
        if (this.endIndex === this.startIndex && action !== 'DeleteBookmark' && action !== 'RemoveEditRange' && this.action !== 'InsertHyperlink') {
            if (action === 'BackSpace') {
                this.startIndex--;
            }
            else {
                this.endIndex++;
            }
        }
        if (action === 'DeleteHeaderFooter') {
            this.startIndex = this.headerFooterStart;
            this.endIndex = this.headerFooterEnd;
        }
        var selectionLength = !isNullOrUndefined(text) ? text.length : this.endIndex - this.startIndex;
        var removedText;
        if (action === 'DeleteBookmark' || action === 'RemoveEditRange') {
            removedText = this.insertedText;
            selectionLength = 1;
        }
        else if (action === 'DeleteHeaderFooter') {
            removedText === '';
        }
        else if (action === 'DeleteTable' || action === 'DeleteRow' || action === 'DeleteColumn' || action === 'MergeCells' || action === 'RemoveRowTrack') {
            removedText = this.insertedText;
            if (action !== 'DeleteTable' && action !== 'DeleteRow') {
                selectionLength = this.tableRelatedLength;
            }
        }
        else {
            removedText = !isNullOrUndefined(text) ? text : this.getRemovedText();
        }
        if (action === 'Cut' && removedText[removedText.length - 1] === ' ' && selectionLength < removedText.length) {
            selectionLength = removedText.length;
        }
        var operation = {
            action: 'Delete',
            offset: setEndIndex ? this.endIndex : this.startIndex,
            text: removedText,
            length: (action === 'Paste' || selectionLength === 0) ? removedText.length : selectionLength,
            skipOperation: action === 'DeleteHeaderFooter' ? true : undefined,
            markerData: this.markerData[0],
        };
        if (this.removedNodes[0] instanceof FootnoteElementBox) {
            var element = this.removedNodes[0];
            var lastPara = element.bodyWidget.lastChild;
            var positionInfo = { position: 0, done: false };
            var paragraphInfo = { paragraph: lastPara, offset: this.owner.selection.getParagraphLength(lastPara) + 1 };
            this.owner.selection.getPositionInfoForBodyContent(paragraphInfo, positionInfo, element.bodyWidget.firstChild);
            operation.length += positionInfo.position;
        }
        return operation;
    };
    /**
     * @private
     * @returns {Operation}
     */
    BaseHistoryInfo.prototype.getInsertOperation = function (action) {
        var insertedText = action === 'Enter' ? '\n' : this.insertedText;
        var length;
        if (action === 'InsertTable' || action === 'InsertTableBelow' || action === 'InsertRowAbove' || action === 'InsertRowBelow'
            || action === 'InsertColumnLeft' || action === 'InsertColumnRight' || action === 'MergeCells' || action === 'RemoveRowTrack') {
            length = this.tableRelatedLength;
            if (this.action === 'InsertTable' || this.action === 'InsertTableBelow') {
                this.insertIndex = this.startIndex;
            }
        }
        else {
            if (!isNullOrUndefined(insertedText)) {
                length = insertedText.length;
            }
        }
        var operation = {
            action: 'Insert',
            offset: this.insertIndex,
            text: insertedText,
            type: this.type,
            length: length,
            skipOperation: false,
            imageData: this.insertedData,
            tableFormat: this.insertedTableFormat,
            rowFormat: this.insertedRowFormat,
            cellFormat: this.insertedCellFormat,
            paragraphFormat: this.insertedParagraphFormat,
            characterFormat: this.insertedCharacterFormat,
        };
        if (!isNullOrUndefined(this.markerData)) {
            operation.markerData = this.markerData.pop();
        }
        if (this.insertedElement instanceof FootnoteElementBox) {
            var lastPara = this.insertedElement.bodyWidget.lastChild;
            var positionInfo = { position: 0, done: false };
            var paragraphInfo = { paragraph: lastPara, offset: this.owner.selection.getParagraphLength(lastPara) + 1 };
            this.owner.selection.getPositionInfoForBodyContent(paragraphInfo, positionInfo, this.insertedElement.bodyWidget.firstChild);
            operation.length += positionInfo.position;
        }
        if (this.insertedElement instanceof FootnoteElementBox) {
            var lastPara = this.insertedElement.bodyWidget.lastChild;
            var positionInfo = { position: 0, done: false };
            var paragraphInfo = { paragraph: lastPara, offset: this.owner.selection.getParagraphLength(lastPara) + 1 };
            this.owner.selection.getPositionInfoForBodyContent(paragraphInfo, positionInfo, this.insertedElement.bodyWidget.firstChild);
            operation.length += positionInfo.position;
        }
        return operation;
    };
    // Builds the Table and Row operation.
    BaseHistoryInfo.prototype.buildTableRowCellOperation = function (action) {
        var operations = [];
        if (this.insertedNodes.length > 0) {
            if (this.insertedNodes[0] instanceof TableRowWidget) {
                var row = this.insertedNodes[0];
                var paragraphInfo = { 'paragraph': null, 'offset': 0 };
                this.insertIndex = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, this.insertedNodes[0]).position;
                var length_1 = this.insertedNodes.length;
                if (row.ownerTable.childWidgets.length === row.indexInOwner + length_1) {
                    this.insertIndex -= 1;
                }
            }
            for (var i = 0; i < this.insertedNodes.length; i++) {
                if (this.insertedNodes[i] instanceof ParagraphWidget && action === 'InsertTable') {
                    var enterOperation = this.getInsertOperation('Enter');
                    if (isNullOrUndefined(enterOperation.markerData)) {
                        enterOperation.markerData = {};
                    }
                    enterOperation.markerData.isSkipTracking = true;
                    operations.push(enterOperation);
                }
                else if (this.insertedNodes[i] instanceof TableWidget) {
                    var tableWidget = this.insertedNodes[i].combineWidget(this.owner.viewer);
                    this.tableRelatedLength = action === 'InsertTableBelow' ? 0 : 1;
                    this.insertedText = CONTROL_CHARACTERS.Table;
                    var tableFormat = this.owner.sfdtExportModule ? this.owner.sfdtExportModule.writeTableFormat(tableWidget.tableFormat, 0) : {};
                    this.insertedTableFormat = JSON.stringify(tableFormat);
                    operations.push(this.getInsertOperation(action));
                    this.insertedTableFormat = undefined;
                    for (var j = 0; j < tableWidget.childWidgets.length; j++) {
                        var row = tableWidget.childWidgets[j];
                        operations.push(this.buildRowOperation(row, action));
                        for (var k = 0; k < row.childWidgets.length; k++) {
                            var cell = row.childWidgets[k];
                            operations.push(this.buildCellOperation(cell, action, true));
                        }
                    }
                }
                else if (this.insertedNodes[i] instanceof TableRowWidget) {
                    var row = this.insertedNodes[i];
                    operations.push(this.buildRowOperation(row, action));
                    for (var j = 0; j < row.childWidgets.length; j++) {
                        var cell = row.childWidgets[j];
                        operations.push(this.buildCellOperation(cell, action, true));
                    }
                }
                else if (this.insertedNodes[i] instanceof TableCellWidget) {
                    var cell = this.insertedNodes[i];
                    var table = cell.ownerTable.combineWidget(this.owner.viewer);
                    var num = 0;
                    for (var j = 0; j < table.childWidgets.length; j++) {
                        i = this.insertedNodes.length;
                        var row = table.childWidgets[j];
                        for (var k = 0; k < row.childWidgets.length; k++) {
                            var cell_1 = row.childWidgets[k];
                            var paragraphInfo = { 'paragraph': null, 'offset': 0 };
                            if (this.insertedNodes.indexOf(cell_1) !== -1) {
                                var offset = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, cell_1).position;
                                this.insertIndex = offset - num;
                                if (cell_1.ownerTable.childWidgets.length === cell_1.ownerRow.indexInOwner + 1) {
                                    if (this.insertedNodes.indexOf(row.childWidgets[row.childWidgets.length - 1]) !== -1) {
                                        this.insertIndex -= 1;
                                    }
                                }
                                operations.push(this.buildCellOperation(cell_1, action, true));
                                num += 2;
                            }
                            else {
                                var offset = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, cell_1).position;
                                this.insertIndex = offset - num;
                                operations.push(this.buildCellOperation(cell_1, action, false));
                            }
                        }
                    }
                }
            }
        }
        return operations;
    };
    BaseHistoryInfo.prototype.assignRevisionData = function (type, author, date, revisionId) {
        var markerData = {
            revisionType: type,
            author: author,
            date: date,
            revisionId: revisionId
        };
        return markerData;
    };
    BaseHistoryInfo.prototype.createAcceptRejectOperation = function (action) {
        var start = this.owner.selection.start;
        if (!start.paragraph.isInsideTable) {
            return;
        }
        var row = start.paragraph.associatedCell.ownerRow;
        var length = 0;
        this.insertedText = CONTROL_CHARACTERS.Row;
        if (row.rowFormat.revisions.length > 0) {
            var revision = row.rowFormat.revisions[0];
            var isAcceptOrReject = void 0;
            if (action === 'Accept Change') {
                isAcceptOrReject = 'Accept';
            }
            else if (action === 'Reject Change') {
                isAcceptOrReject = 'Reject';
            }
            this.markerData.push(this.owner.editor.getMarkerData(undefined, undefined, revision, isAcceptOrReject));
        }
        var paragraphInfo = { 'paragraph': null, 'offset': 0 };
        var offset = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, row).position;
        if (row.rowFormat.revisions.length > 0) {
            if (row.rowFormat.revisions[0].revisionType === 'Insertion') {
                if (action === 'Accept Change') {
                    this.startIndex = offset;
                    this.tableRelatedLength = 1;
                    this.cellOperation.push(this.getFormatOperation(undefined, 'RemoveRowTrack'));
                }
                else if (action === 'Reject Change') {
                    this.startIndex = offset;
                    for (var j = 0; j < row.childWidgets.length; j++) {
                        length += this.owner.selection.calculateCellLength(row.childWidgets[j]) + 1;
                    }
                    this.tableRelatedLength = length;
                    this.cellOperation.push(this.getDeleteOperation('RemoveRowTrack'));
                }
            }
            else if (row.rowFormat.revisions[0].revisionType === 'Deletion') {
                if (action === 'Accept Change') {
                    this.startIndex = offset;
                    // this.tableRelatedLength = 0;
                    for (var j = 0; j < row.childWidgets.length; j++) {
                        length += this.owner.selection.calculateCellLength(row.childWidgets[j]) + 1;
                    }
                    this.tableRelatedLength = length;
                    this.cellOperation.push(this.getDeleteOperation('RemoveRowTrack'));
                }
                else if (action === 'Reject Change') {
                    this.startIndex = offset;
                    for (var j = 0; j < row.childWidgets.length; j++) {
                        length += this.owner.selection.calculateCellLength(row.childWidgets[j]) + 1;
                    }
                    this.tableRelatedLength = length;
                    this.cellOperation.push(this.getFormatOperation(undefined, 'RemoveRowTrack'));
                }
            }
            this.markerData = [];
        }
    };
    BaseHistoryInfo.prototype.beforeInsertTableRevision = function () {
        var start = this.owner.selection.start;
        var table = start.paragraph.associatedCell.ownerTable;
        for (var i = 0; i < table.childWidgets.length; i++) {
            var row = table.childWidgets[i];
            var length_2 = 0;
            for (var j = 0; j < row.childWidgets.length; j++) {
                length_2 += this.owner.selection.calculateCellLength(row.childWidgets[j]) + 1;
            }
            this.tableRelatedLength = length_2 + 1;
            this.insertedText = CONTROL_CHARACTERS.Row;
            var paragraphInfo = { 'paragraph': null, 'offset': 0 };
            this.startIndex = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, row).position;
            if (row.rowFormat.revisions.length > 0) {
                var revision = row.rowFormat.revisions[0];
                this.markerData.push(this.owner.editor.getMarkerData(undefined, undefined, revision));
            }
            this.cellOperation.push(this.getDeleteOperation('RemoveRowTrack'));
            this.markerData = [];
        }
        this.cellOperation.reverse();
    };
    BaseHistoryInfo.prototype.afterInsertTableRrevision = function () {
        var start = this.owner.selection.start;
        var table = start.paragraph.associatedCell.ownerTable;
        for (var i = 0; i < table.childWidgets.length; i++) {
            var row = table.childWidgets[i];
            var length_3 = 0;
            for (var j = 0; j < row.childWidgets.length; j++) {
                length_3 += this.owner.selection.calculateCellLength(row.childWidgets[j]) + 1;
            }
            this.tableRelatedLength = length_3 + 1;
            this.insertedText = CONTROL_CHARACTERS.Row;
            var paragraphInfo = { 'paragraph': null, 'offset': 0 };
            this.insertIndex = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, row).position;
            if (row.rowFormat.revisions.length > 0) {
                var revision = row.rowFormat.revisions[0];
                this.markerData.push(this.owner.editor.getMarkerData(undefined, undefined, revision));
            }
            var operation = this.getInsertOperation('RemoveRowTrack');
            operation.skipOperation = true;
            this.cellOperation.push(operation);
            this.markerData = [];
        }
    };
    BaseHistoryInfo.prototype.buildRowOperation = function (row, action) {
        this.insertedText = CONTROL_CHARACTERS.Row;
        var rowFormat = {};
        if (!isNullOrUndefined(this.owner.sfdtExportModule)) {
            this.owner.sfdtExportModule.assignRowFormat(rowFormat, row.rowFormat, 0);
        }
        this.insertedRowFormat = JSON.stringify(rowFormat);
        if (row.rowFormat.revisions.length > 0) {
            var revision = row.rowFormat.revisions[row.rowFormat.revisions.length - 1];
            var lastRevision = this.markerData[this.markerData.length - 1];
            if (!(!isNullOrUndefined(lastRevision) && lastRevision.revisionId === revision.revisionID)) {
                this.markerData.push(this.owner.editor.getMarkerData(undefined, undefined, revision));
            }
        }
        this.tableRelatedLength = 1;
        var operation = this.getInsertOperation(action);
        this.insertedRowFormat = undefined;
        this.markerData = [];
        return operation;
    };
    /**
     * @private
     */
    BaseHistoryInfo.prototype.buildRowOperationForTrackChanges = function (row, action) {
        var paragraphInfo = { 'paragraph': null, 'offset': 0 };
        var length = 0;
        var offset = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, row).position;
        this.startIndex = offset;
        for (var j = 0; j < row.childWidgets.length; j++) {
            length += this.owner.selection.calculateCellLength(row.childWidgets[j]) + 1;
        }
        this.tableRelatedLength = length;
        this.insertedText = CONTROL_CHARACTERS.Row;
        this.cellOperation.push(this.getFormatOperation(undefined, action));
    };
    BaseHistoryInfo.prototype.buildCellOperation = function (cell, action, isCellInserted) {
        var characterFormat = {};
        var paragraphFormat = {};
        this.tableRelatedLength = isCellInserted ? 2 : 0;
        this.insertedText = CONTROL_CHARACTERS.Cell;
        var cellFormat = !isNullOrUndefined(this.owner.sfdtExportModule) ? this.owner.sfdtExportModule.writeCellFormat(cell.cellFormat, 0) : {};
        this.insertedCellFormat = JSON.stringify(cellFormat);
        HelperMethods.writeParagraphFormat(paragraphFormat, true, cell.childWidgets[0].paragraphFormat);
        this.insertedParagraphFormat = JSON.stringify(paragraphFormat);
        HelperMethods.writeCharacterFormat(characterFormat, true, cell.childWidgets[0].characterFormat);
        this.insertedCharacterFormat = JSON.stringify(characterFormat);
        var operation = this.getInsertOperation(action);
        this.insertedCellFormat = undefined;
        this.insertedParagraphFormat = undefined;
        this.insertedCharacterFormat = undefined;
        return operation;
    };
    BaseHistoryInfo.prototype.deleteColumnOperation = function (action) {
        var startCell = this.owner.editor.getOwnerCell(this.owner.selection.isForward);
        var endCell = this.owner.editor.getOwnerCell(!this.owner.selection.isForward);
        var table = startCell.ownerTable.combineWidget(this.owner.viewer);
        var deleteCells = [];
        if (action === 'DeleteColumn') {
            deleteCells = table.getColumnCellsForSelection(startCell, endCell);
        }
        else {
            deleteCells = this.owner.selection.getSelectedCells();
        }
        for (var i = 0; i < deleteCells.length; i++) {
            if (action === 'ClearCells') {
                this.deleteCell(action, deleteCells[i]);
            }
            else if (action === 'MergeCells') {
                if (i !== 0) {
                    this.deleteCell(action, deleteCells[i]);
                }
            }
            else {
                this.deleteCell('DeleteColumn', deleteCells[i]);
            }
        }
        if (action === 'MergeCells') {
            this.cellOperation.reverse();
            this.deleteCell('ClearCells', deleteCells[0]);
        }
    };
    BaseHistoryInfo.prototype.getPasteMergeOperation = function () {
        var cell = this.owner.selection.start.paragraph.associatedCell;
        var paragraphInfo = { 'paragraph': null, 'offset': 0 };
        var offset = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, cell).position;
        var length = this.owner.selection.calculateCellLength(cell);
        var firstParagraph = this.owner.selection.getFirstParagraph(cell);
        var lastParagraph = this.owner.selection.getLastParagraph(cell);
        var startPos = new TextPosition(this.owner);
        var endPos = new TextPosition(this.owner);
        var startline = firstParagraph.firstChild;
        var lastLine = lastParagraph.lastChild;
        startPos.setPosition(startline, true);
        endPos.setPosition(lastLine, false);
        this.pasteContent = this.owner.sfdtExportModule.write((this.owner.documentEditorSettings.optimizeSfdt ? 1 : 0), firstParagraph.firstChild, startPos.offset, lastParagraph.lastChild, endPos.offset, false, true);
        this.startIndex = offset + 1;
        var pasteOperation = {
            action: 'Insert',
            offset: this.startIndex,
            length: length,
            pasteContent: JSON.stringify(this.pasteContent),
            type: 'Paste'
        };
        this.insertedText = CONTROL_CHARACTERS.Cell;
        this.startIndex = offset;
        this.endIndex = offset;
        this.insertedCellFormat = JSON.stringify(this.owner.sfdtExportModule.writeCellFormat(cell.cellFormat, 0));
        return pasteOperation;
    };
    BaseHistoryInfo.prototype.deleteCell = function (action, cell) {
        this.tableRelatedLength = this.owner.selection.calculateCellLength(cell) + 1;
        var paragraphInfo = { 'paragraph': null, 'offset': 0 };
        this.startIndex = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, cell).position;
        if (!this.owner.enableTrackChanges) {
            if (action === 'ClearCells') {
                var block = cell.childWidgets[0];
                if (cell.childWidgets.length === 1 && block instanceof ParagraphWidget && block.isEmpty()) {
                    return;
                }
                this.endIndex = this.startIndex + this.tableRelatedLength - 1;
                this.startIndex += 1;
                this.cellOperation.push(this.getDeleteOperation('ClearCells'));
            }
            else {
                this.cellOperation.push(this.getDeleteOperation('DeleteColumn'));
            }
        }
    };
    /**
     * @private
     * @returns {Operation}
     */
    BaseHistoryInfo.prototype.getFormatOperation = function (element, action) {
        var length = 0;
        if (action === 'RemoveRowTrack') {
            length = this.tableRelatedLength;
        }
        else if (action === 'RowResizing' || action === 'CellResizing' || action === 'ImageResizing') {
            length = this.insertedText.length;
        }
        else {
            length = !isNullOrUndefined(element) ? element.length : this.endIndex - this.startIndex;
        }
        var formatOperation = {
            action: 'Format',
            offset: this.startIndex,
            length: length,
            markerData: this.markerData[this.markerData.length - 1],
            imageData: this.insertedData,
            tableFormat: this.insertedTableFormat,
            rowFormat: this.insertedRowFormat,
            cellFormat: this.insertedCellFormat,
            text: this.insertedText,
            paragraphFormat: this.insertedParagraphFormat,
            characterFormat: this.insertedCharacterFormat,
            sectionFormat: this.insertedSectionFormat
        };
        this.markerData.pop();
        if (!isNullOrUndefined(action)) {
            formatOperation.type = action.toString();
        }
        return formatOperation;
    };
    BaseHistoryInfo.prototype.getRemovedText = function (element) {
        var text = '';
        if (!isNullOrUndefined(element)) {
            var node = element;
            if (node instanceof ParagraphWidget) {
                text += this.getParagraphText(node);
            }
            else if (node instanceof ElementBox) {
                if (node instanceof TextElementBox) {
                    text += node.text;
                }
                else {
                    text += ElementBox.objectCharacter;
                }
            }
            else if (node instanceof TableWidget) {
                text += this.getTableText(node);
            }
            else if (node instanceof TableRowWidget) {
                text += this.getRowText(node);
            }
        }
        else {
            for (var i = this.removedNodes.length - 1; i >= 0; i--) {
                var node = this.removedNodes[i];
                if (node instanceof ParagraphWidget) {
                    text += this.getParagraphText(node);
                }
                else if (node instanceof ElementBox) {
                    if (node instanceof TextElementBox) {
                        text += node.text;
                    }
                    else {
                        text += ElementBox.objectCharacter;
                    }
                }
                else if (node instanceof TableWidget) {
                    text += this.getTableText(node);
                }
                else if (node instanceof TableRowWidget) {
                    text += this.getRowText(node);
                }
            }
        }
        return text;
    };
    BaseHistoryInfo.prototype.getRemovedFieldCode = function () {
        var fieldCode = '';
        var isStarted = false;
        for (var i = this.removedNodes.length - 1; i >= 0; i--) {
            var node = this.removedNodes[i];
            if (node instanceof ElementBox) {
                if (node instanceof FieldElementBox && node.fieldType === 0) {
                    isStarted = true;
                }
                if (node && node instanceof TextElementBox) {
                    if (isStarted) {
                        fieldCode += node.text;
                    }
                }
                if (node instanceof FieldElementBox
                    && (node.fieldType === 2 || node.fieldType === 1)) {
                    return fieldCode;
                }
            }
            else if (node instanceof ParagraphWidget) {
                for (var i_1 = 0; i_1 < node.childWidgets.length; i_1++) {
                    var lineWidget = node.childWidgets[i_1];
                    for (var j = 0; j < lineWidget.children.length; j++) {
                        var element = lineWidget.children[j];
                        if (element instanceof FieldElementBox && element.fieldType === 0) {
                            isStarted = true;
                        }
                        if (element instanceof TextElementBox) {
                            if (isStarted) {
                                fieldCode += element.text;
                            }
                        }
                        if (element instanceof FieldElementBox
                            && (element.fieldType === 2 || element.fieldType === 1)) {
                            return fieldCode;
                        }
                    }
                }
            }
        }
        return undefined;
    };
    //  Add for loop to iterate paragraph child elements and get text 
    BaseHistoryInfo.prototype.getParagraphText = function (paragraph) {
        var text = '';
        if (!isNullOrUndefined(paragraph) && !isNullOrUndefined(paragraph.childWidgets)) {
            for (var i = 0; i < paragraph.childWidgets.length; i++) {
                var line = paragraph.childWidgets[i];
                for (var j = 0; j < line.children.length; j++) {
                    if (line.children[j] instanceof TextElementBox) {
                        text += line.children[j].text;
                    }
                    else {
                        text += ElementBox.objectCharacter;
                    }
                }
            }
            return text + '\n';
        }
        return text;
    };
    //  Add for loop to iterate table child elements and get text
    BaseHistoryInfo.prototype.getTableText = function (table) {
        var text = '';
        for (var i = 0; i < table.childWidgets.length; i++) {
            var row = table.childWidgets[i];
            text += this.getRowText(row);
        }
        return text;
    };
    // Add for loop to iterate table row child elements and get text
    BaseHistoryInfo.prototype.getRowText = function (row) {
        var text = '';
        for (var j = 0; j < row.childWidgets.length; j++) {
            var cell = row.childWidgets[j];
            for (var k = 0; k < cell.childWidgets.length; k++) {
                var block = cell.childWidgets[k];
                if (block instanceof ParagraphWidget) {
                    text += this.getParagraphText(block);
                }
                else {
                    text += this.getTableText(block);
                }
            }
        }
        return text;
    };
    /**
     * @private
     * @returns {Operation}
     */
    BaseHistoryInfo.prototype.getCommentOperation = function (operation, comment) {
        if (this.action === 'InsertInline' || this.action === 'RemoveInline') {
            var commentRangeElement = this.action === 'RemoveInline' ? this.removedNodes[0] : this.insertedElement;
            var commentElement = commentRangeElement.comment;
            operation.text = commentRangeElement.commentType === 0 ? CONTROL_CHARACTERS.Marker_Start : CONTROL_CHARACTERS.Marker_End;
            operation.markerData = {
                type: 'Comment',
                commentId: commentRangeElement.commentId,
                ownerCommentId: commentElement.isReply ? commentElement.ownerComment.commentId : undefined
            };
        }
        else if (this.action === 'InsertCommentWidget' || this.action === 'DeleteCommentWidget') {
            var comment_1 = this.removedNodes[0];
            operation.length = undefined;
            operation.action = 'Update';
            operation.offset = undefined;
            operation.text = CONTROL_CHARACTERS.Marker_Start + CONTROL_CHARACTERS.Marker_End;
            operation.markerData = {
                type: 'Comment',
                commentId: comment_1.commentId,
                author: comment_1.author,
                date: comment_1.date,
                commentIndex: comment_1.isReply ? comment_1.ownerComment.replyComments.indexOf(comment_1) : this.owner.documentHelper.comments.indexOf(comment_1),
                initial: comment_1.initial,
                done: comment_1.isResolved,
                text: comment_1.text,
                isReply: comment_1.isReply
            };
            if (!isNullOrUndefined(comment_1.ownerComment)) {
                operation.markerData.ownerCommentId = comment_1.ownerComment.commentId;
            }
        }
        else if (this.action === 'ResolveComment') {
            operation.text = CONTROL_CHARACTERS.Marker_Start + CONTROL_CHARACTERS.Marker_End;
            operation.markerData = {
                type: 'Comment',
                commentId: comment.commentId,
                done: comment.isResolved
            };
        }
        else if (this.action === 'EditComment') {
            operation.text = CONTROL_CHARACTERS.Marker_Start + CONTROL_CHARACTERS.Marker_End;
            operation.markerData = {
                type: 'Comment',
                commentId: comment.commentId,
                text: comment.text,
                isReply: comment.isReply,
                ownerCommentId: comment.isReply ? comment.ownerComment.commentId : undefined
            };
        }
        return operation;
    };
    /**
     * @private
     */
    BaseHistoryInfo.prototype.getDeleteCommentOperation = function (modifiedActions, operations) {
        for (var i = 0; i < modifiedActions.length; i++) {
            var currentHistory = modifiedActions[i];
            if (currentHistory instanceof HistoryInfo && (currentHistory.action === 'DeleteComment')) {
                this.getDeleteCommentOperation(currentHistory.modifiedActions, operations);
            }
            else {
                var operation = currentHistory.getDeleteOperation(currentHistory.action);
                operations.push(currentHistory.getCommentOperation(operation));
            }
        }
    };
    /**
     * @private
     * @returns {Operation}
     */
    BaseHistoryInfo.prototype.buildFormatOperation = function (action, ischarFormat, isCellFormat) {
        var operations = [];
        if ((action === 'ApplyStyle' || action === 'StyleName') && this.insertedFormat instanceof WParagraphStyle) {
            this.insertedFormat = this.insertedFormat.name;
            this.createParagraphFormat(action);
        }
        else {
            if (action === 'ApplyStyle' || action === 'StyleName') {
                this.insertedFormat = this.insertedFormat.name;
            }
            ischarFormat ? this.createCharacterFormat(action) : this.createParagraphFormat(action);
        }
        operations = this.getSelectedCellOperation(action, ischarFormat);
        this.insertedCharacterFormat = undefined;
        this.insertedParagraphFormat = undefined;
        this.insertedCellFormat = undefined;
        return operations;
    };
    /**
     * @private
     * @returns {Operation}
     */
    BaseHistoryInfo.prototype.getSelectedCellOperation = function (action, ischarFormat, isBorder, isShading) {
        var operations = [];
        var start = this.owner.selection.start;
        var end = this.owner.selection.end;
        if (start.paragraph.isInsideTable && end.paragraph.isInsideTable && (start.paragraph.associatedCell.ownerTable.equals(end.paragraph.associatedCell.ownerTable)
            && this.owner.selection.isCellSelected(start.paragraph.associatedCell, start, end))) {
            var selectCells = this.owner.selection.getSelectedCells();
            for (var i = 0; i < selectCells.length; i++) {
                var cell = selectCells[i];
                var paragraphInfo = { 'paragraph': null, 'offset': 0 };
                this.startIndex = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, cell).position + 1;
                var length_4 = this.owner.selection.calculateCellLength(cell) - 1;
                this.endIndex = this.startIndex + length_4;
                if (length_4 === 0 && ischarFormat) {
                    continue;
                }
                var cellFormat = {};
                if (isBorder) {
                    cellFormat['borders'] = HelperMethods.writeBorders(cell.cellFormat.borders, 0);
                    this.insertedCellFormat = JSON.stringify(cellFormat);
                }
                if (isShading) {
                    cellFormat['shading'] = this.owner.sfdtExportModule ? this.owner.sfdtExportModule.writeShading(cell.cellFormat.shading, 0) : {};
                    this.insertedCellFormat = JSON.stringify(cellFormat);
                }
                var formatOperation = void 0;
                if (action === 'ListFormat') {
                    formatOperation = this.getFormatOperation(undefined, action);
                    this.createListFormat(action, formatOperation);
                }
                else {
                    formatOperation = this.getFormatOperation();
                }
                operations.push(formatOperation);
            }
        }
        else {
            var operation = void 0;
            if (action === 'ListFormat') {
                operation = this.getFormatOperation(undefined, action);
                this.createListFormat(action, operation);
            }
            else {
                operation = this.getFormatOperation();
            }
            operations.push(operation);
        }
        return operations;
    };
    BaseHistoryInfo.prototype.createListFormat = function (action, operation) {
        var listId;
        var nsid;
        if (action === 'ListFormat') {
            listId = this.insertedFormat.listId;
            operation.text = action.toString().charAt(0).toLowerCase() + action.toString().slice(1);
        }
        else {
            listId = this.insertedFormat.listId;
            nsid = this.insertedFormat.nsid;
        }
        if (listId > -1) {
            var list = this.owner.documentHelper.getListById(listId);
            var listData = {};
            listData.optimizeSfdt = this.owner.documentEditorSettings.optimizeSfdt;
            if (!isNullOrUndefined(this.owner.sfdtExportModule)) {
                this.owner.sfdtExportModule.keywordIndex = this.owner.documentEditorSettings.optimizeSfdt ? 1 : 0;
                listData[listsProperty[this.owner.sfdtExportModule.keywordIndex]] = [];
                listData[listsProperty[this.owner.sfdtExportModule.keywordIndex]].push(this.owner.sfdtExportModule.writeList(list));
                listData[abstractListsProperty[this.owner.sfdtExportModule.keywordIndex]] = [];
                if (!isNullOrUndefined(list)) {
                    listData[abstractListsProperty[this.owner.sfdtExportModule.keywordIndex]].push(this.owner.sfdtExportModule.writeAbstractList(list.abstractList));
                }
                if (action == 'RestartNumbering') {
                    listData[listIdProperty[this.owner.sfdtExportModule.keywordIndex]] = listId;
                    listData[nsidProperty] = nsid;
                }
            }
            operation.listData = JSON.stringify(listData);
        }
    };
    BaseHistoryInfo.prototype.createCharacterFormat = function (action) {
        var characterFormat = {};
        if (action === 'Uppercase') {
            characterFormat.allCaps = true;
        }
        else if (action === 'ApplyStyle' || action === 'StyleName') {
            characterFormat.styleName = this.insertedFormat;
        }
        else if (action === 'CharacterFormat') {
            var charFormat = this.insertedFormat;
            characterFormat.bold = charFormat.hasValue('bold') ? charFormat.bold : characterFormat.bold;
            characterFormat.italic = charFormat.hasValue('italic') ? charFormat.italic : characterFormat.italic;
            characterFormat.fontSize = charFormat.hasValue('fontSize') ? charFormat.fontSize : characterFormat.fontSize;
            characterFormat.underline = charFormat.hasValue('underline') ? charFormat.underline : characterFormat.underline;
            characterFormat.strikethrough = charFormat.hasValue('strikethrough') ? charFormat.strikethrough : characterFormat.strikethrough;
            characterFormat.baselineAlignment = charFormat.hasValue('baselineAlignment') ? charFormat.baselineAlignment : characterFormat.baselineAlignment;
            characterFormat.highlightColor = charFormat.hasValue('highlightColor') ? charFormat.highlightColor : characterFormat.highlightColor;
            characterFormat.fontColor = charFormat.hasValue('fontColor') ? charFormat.fontColor : characterFormat.fontColor;
            characterFormat.fontFamily = charFormat.hasValue('fontFamily') ? charFormat.fontFamily : characterFormat.fontFamily;
            characterFormat.allCaps = charFormat.hasValue('allCaps') ? charFormat.allCaps : characterFormat.allCaps;
        }
        else {
            if (this.insertedFormat === 'increment' || this.insertedFormat === 'decrement') {
                this.type = this.insertedFormat;
                characterFormat.fontSize = 0;
            }
            else {
                if (action !== 'ClearFormat') {
                    characterFormat[action.toString().charAt(0).toLowerCase() + action.toString().slice(1)] = this.insertedFormat;
                }
            }
        }
        this.insertedCharacterFormat = JSON.stringify(characterFormat);
    };
    BaseHistoryInfo.prototype.createParagraphFormat = function (action) {
        var paragraphFormat = {};
        if (action === 'ParagraphFormat' || action === 'ContinueNumbering') {
            var paraFormat = this.insertedFormat;
            paragraphFormat.afterSpacing = paraFormat.hasValue('afterSpacing') ? paraFormat.afterSpacing : undefined;
            paragraphFormat.beforeSpacing = paraFormat.hasValue('beforeSpacing') ? paraFormat.beforeSpacing : undefined;
            paragraphFormat.spaceAfterAuto = paraFormat.hasValue('spaceAfterAuto') ? paraFormat.spaceAfterAuto : paragraphFormat.spaceAfterAuto;
            paragraphFormat.spaceBeforeAuto = paraFormat.hasValue('spaceBeforeAuto') ? paraFormat.spaceBeforeAuto : paragraphFormat.spaceBeforeAuto;
            paragraphFormat.rightIndent = paraFormat.hasValue('rightIndent') ? paraFormat.rightIndent : paragraphFormat.rightIndent;
            paragraphFormat.leftIndent = paraFormat.hasValue('leftIndent') ? paraFormat.leftIndent : paragraphFormat.leftIndent;
            paragraphFormat.firstLineIndent = paraFormat.hasValue('firstLineIndent') ? paraFormat.firstLineIndent : paragraphFormat.firstLineIndent;
            paragraphFormat.lineSpacing = paraFormat.hasValue('lineSpacing') ? paraFormat.lineSpacing : paragraphFormat.lineSpacing;
            paragraphFormat.lineSpacingType = paraFormat.hasValue('lineSpacingType') ? paraFormat.lineSpacingType : paragraphFormat.lineSpacingType;
            paragraphFormat.textAlignment = paraFormat.hasValue('textAlignment') ? paraFormat.textAlignment : paragraphFormat.textAlignment;
            paragraphFormat.borders = paraFormat.hasValue('borders') ? paraFormat.borders : paragraphFormat.borders;
            if (paraFormat.listFormat.listId !== -1) {
                var listFormat = {};
                listFormat.listId = paraFormat.listFormat.listId;
                listFormat.listLevelNumber = paraFormat.listFormat.listLevelNumber;
                listFormat.nsid = paraFormat.listFormat.nsid;
                paragraphFormat.listFormat = listFormat;
            }
            paragraphFormat.styleName = paraFormat.hasValue('styleName') ? paragraphFormat.styleName.name : undefined;
            paragraphFormat.contextualSpacing = paraFormat.hasValue('contextualSpacing') ? paraFormat.contextualSpacing : paragraphFormat.contextualSpacing;
            paragraphFormat.keepWithNext = paraFormat.hasValue('keepWithNext') ? paraFormat.keepWithNext : paragraphFormat.keepWithNext;
            paragraphFormat.keepLinesTogether = paraFormat.hasValue('keepLinesTogether') ? paraFormat.keepLinesTogether : paragraphFormat.keepLinesTogether;
            paragraphFormat.widowControl = paraFormat.hasValue('contextualSpacing') ? paraFormat.widowControl : paragraphFormat.widowControl;
        }
        else if (action === 'ListFormat') {
            var listFormat = {};
            listFormat.listId = this.insertedFormat.listId;
            listFormat.nsid = this.insertedFormat.nsid;
            listFormat.listLevelNumber = this.insertedFormat.listLevelNumber;
            paragraphFormat.listFormat = listFormat;
        }
        else if (action === 'ApplyStyle' || action === 'StyleName') {
            paragraphFormat.styleName = this.insertedFormat;
        }
        else if (action === 'ParagraphBidi') {
            paragraphFormat.bidi = this.insertedFormat;
        }
        else if (action === 'Borders') {
            paragraphFormat['borders'] = HelperMethods.writeBorders(this.insertedFormat, 0);
        }
        else {
            if (this.insertedFormat instanceof WParagraphFormat) {
                var paraFormat = this.owner.sfdtExportModule.writeParagraphFormat(this.insertedFormat);
                paragraphFormat[action.toString().charAt(0).toLowerCase() + action.toString().slice(1)] = paraFormat;
            }
            else {
                paragraphFormat[action.toString().charAt(0).toLowerCase() + action.toString().slice(1)] = this.insertedFormat;
            }
        }
        this.insertedParagraphFormat = JSON.stringify(paragraphFormat);
    };
    /**
     * @private
     * @returns {void}
     */
    BaseHistoryInfo.prototype.createTableFormat = function (action) {
        var paragraphInfo = { 'paragraph': null, 'offset': 0 };
        this.startIndex = this.owner.selection.getPositionInfoForHeaderFooter(paragraphInfo, { position: 0, done: false }, this.owner.selection.start.paragraph.associatedCell.ownerTable).position;
        this.endIndex = this.startIndex;
        var tableFormat = {};
        if (action === 'TableFormat') {
            var tabFormat = this.insertedFormat;
            if (!isNullOrUndefined(tabFormat)) {
                tableFormat.bidi = tabFormat.hasValue('bidi') ? tabFormat.bidi : undefined;
                tableFormat.preferredWidth = tabFormat.hasValue('preferredWidth') ? tabFormat.preferredWidth : undefined;
                tableFormat.preferredWidthType = tabFormat.hasValue('preferredWidthType') ? tabFormat.preferredWidthType : undefined;
                tableFormat.tableAlignment = tabFormat.hasValue('tableAlignment') ? tabFormat.tableAlignment : undefined;
                tableFormat.leftIndent = tabFormat.hasValue('leftIndent') ? tabFormat.leftIndent : undefined;
            }
        }
        else if (action === 'TableOptions') {
            var tableOption = this.owner.selection.start.paragraph.associatedCell.ownerTable.tableFormat;
            if (!isNullOrUndefined(tableOption)) {
                tableFormat.cellSpacing = tableOption.hasValue('cellSpacing') ? tableOption.cellSpacing : undefined;
                tableFormat.leftMargin = tableOption.hasValue('leftMargin') ? tableOption.leftMargin : undefined;
                tableFormat.topMargin = tableOption.hasValue('topMargin') ? tableOption.topMargin : undefined;
                tableFormat.rightMargin = tableOption.hasValue('rightMargin') ? tableOption.rightMargin : undefined;
                tableFormat.bottomMargin = tableOption.hasValue('bottomMargin') ? tableOption.bottomMargin : undefined;
            }
        }
        else if (action === 'BordersAndShading') {
            var tabBorderFormat = this.insertedFormat;
            tableFormat = !isNullOrUndefined(this.owner.sfdtExportModule) ? this.owner.sfdtExportModule.writeTableFormat(tabBorderFormat, 0) : {};
        }
        else {
            tableFormat[this.getTableFormatString(action)] = this.insertedFormat;
        }
        this.insertedTableFormat = JSON.stringify(tableFormat);
    };
    /**
     * @private
     * @returns {void}
     */
    BaseHistoryInfo.prototype.createRowFormat = function (action) {
        var rowFormat = {};
        if (action === 'RowFormat') {
            var rForamt = this.insertedFormat;
            if (!isNullOrUndefined(rForamt)) {
                rowFormat.height = rForamt.hasValue('height') ? rForamt.height : undefined;
                rowFormat.heightType = rForamt.hasValue('heightType') ? rForamt.heightType : undefined;
                rowFormat.isHeader = rForamt.hasValue('isHeader') ? rForamt.isHeader : undefined;
                rowFormat.allowBreakAcrossPages = rForamt.hasValue('allowBreakAcrossPages') ? rForamt.allowBreakAcrossPages : undefined;
            }
        }
        else {
            rowFormat[this.getRowString(action)] = this.insertedFormat;
        }
        this.insertedRowFormat = JSON.stringify(rowFormat);
    };
    /**
     * @private
     * @returns {void}
     */
    BaseHistoryInfo.prototype.createCellFormat = function (action) {
        var cellFormat = {};
        if (action === 'CellFormat') {
            var cFormat = this.insertedFormat;
            cellFormat.preferredWidth = cFormat.hasValue('preferredWidth') ? cFormat.preferredWidth : undefined;
            cellFormat.preferredWidthType = cFormat.hasValue('preferredWidthType') ? cFormat.preferredWidthType : undefined;
            cellFormat.verticalAlignment = cFormat.hasValue('verticalAlignment') ? cFormat.verticalAlignment : undefined;
        }
        else if (action === 'CellOptions') {
            var cellOption = this.insertedFormat;
            cellFormat.leftMargin = cellOption.leftMargin;
            cellFormat.rightMargin = cellOption.rightMargin;
            cellFormat.bottomMargin = cellOption.bottomMargin;
            cellFormat.topMargin = cellOption.topMargin;
        }
        else if (action === 'Shading') {
            cellFormat[this.getCellString(action)] = !isNullOrUndefined(this.owner.sfdtExportModule) ? this.owner.sfdtExportModule.writeShading(this.insertedFormat, 0) : {};
        }
        else if (action === 'Borders') {
            cellFormat['borders'] = HelperMethods.writeBorders(this.insertedFormat, 0);
        }
        else if (action === 'BordersAndShading') {
            cellFormat['shading'] = !isNullOrUndefined(this.owner.sfdtExportModule) ? this.owner.sfdtExportModule.writeShading(this.insertedFormat, 0) : {};
            cellFormat['borders'] = HelperMethods.writeBorders(this.insertedFormat.borders, 0);
        }
        else {
            cellFormat[this.getCellString(action)] = this.insertedFormat;
        }
        this.insertedCellFormat = JSON.stringify(cellFormat);
    };
    BaseHistoryInfo.prototype.getTableFormatString = function (property) {
        switch (property) {
            case 'TableAlignment':
                return 'tableAlignment';
            case 'TableLeftIndent':
                return 'leftIndent';
            case 'DefaultCellLeftMargin':
                return 'leftMargin';
            case 'DefaultCellRightMargin':
                return 'rightMargin';
            case 'DefaultCellBottomMargin':
                return 'bottomMargin';
            case 'DefaultCellTopMargin':
                return 'topMargin';
            case 'TablePreferredWidth':
                return 'preferredWidth';
            case 'TablePreferredWidthType':
                return 'preferredWidthType';
            case 'Shading':
                return 'shading';
            case 'TableBidi':
                return 'bidi';
            default:
                return 'cellSpacing';
        }
    };
    BaseHistoryInfo.prototype.createSectionFormat = function (action) {
        var sectionFormat = {};
        switch (action) {
            case 'SectionFormat':
                var secFormat = this.insertedFormat;
                this.owner.sfdtExportModule.writeSectionFormat(secFormat, sectionFormat, 0);
                break;
            case 'LinkToPrevious':
                var headerFooterWidget = this.owner.selection.start.paragraph.bodyWidget;
                var sectionIndex = headerFooterWidget.sectionIndex;
                var headerFooterType = headerFooterWidget.headerFooterType;
                this.insertedSectionFormat = JSON.stringify({ linkToPrevious: this.insertedFormat, sectionIndex: sectionIndex, headerFooterType: headerFooterType });
                return;
            default:
                sectionFormat[action[0].toLowerCase() + action.slice(1)] = this.insertedFormat;
        }
        this.insertedSectionFormat = JSON.stringify(sectionFormat);
    };
    BaseHistoryInfo.prototype.getRowString = function (property) {
        switch (property) {
            case 'RowHeight':
                return 'height';
            case 'RowHeightType':
                return 'heightType';
            case 'RowHeader':
                return 'isHeader';
            default:
                return 'allowBreakAcrossPages';
        }
    };
    BaseHistoryInfo.prototype.getCellString = function (property) {
        switch (property) {
            case 'CellContentVerticalAlignment':
                return 'verticalAlignment';
            case 'CellLeftMargin':
                return 'leftMargin';
            case 'CellRightMargin':
                return 'rightMargin';
            case 'CellBottomMargin':
                return 'bottomMargin';
            case 'CellTopMargin':
                return 'topMargin';
            case 'CellPreferredWidth':
                return 'preferredWidth';
            case 'Shading':
                return 'shading';
            default:
                return 'cellPreferredWidthType';
        }
    };
    return BaseHistoryInfo;
}());
export { BaseHistoryInfo };
