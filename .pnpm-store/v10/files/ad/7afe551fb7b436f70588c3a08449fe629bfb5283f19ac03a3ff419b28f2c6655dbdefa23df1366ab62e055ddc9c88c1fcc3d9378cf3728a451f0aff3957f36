@import 'ej2-base/styles/definition/tailwind-dark.scss';
@import 'ej2-buttons/styles/button/tailwind-dark-definition.scss';
@import 'ej2-buttons/styles/check-box/tailwind-dark-definition.scss';
@import 'ej2-buttons/styles/radio-button/tailwind-dark-definition.scss';
@import 'ej2-buttons/styles/switch/tailwind-dark-definition.scss';
@import 'ej2-navigations/styles/toolbar/tailwind-dark-definition.scss';
@import 'ej2-navigations/styles/tab/tailwind-dark-definition.scss';
@import 'ej2-navigations/styles/context-menu/tailwind-dark-definition.scss';
@import 'ej2-navigations/styles/menu/tailwind-dark-definition.scss';
@import 'ej2-navigations/styles/treeview/tailwind-dark-definition.scss';
@import 'ej2-grids/styles/excel-filter/tailwind-dark-definition.scss';
@import 'ej2-calendars/styles/datepicker/tailwind-dark-definition.scss';
@import 'ej2-calendars/styles/datetimepicker/tailwind-dark-definition.scss';
@import 'ej2-inputs/styles/color-picker/tailwind-dark-definition.scss';
@import 'ej2-splitbuttons/styles/drop-down-button/tailwind-dark-definition.scss';
@import 'ej2-splitbuttons/styles/split-button/tailwind-dark-definition.scss';
@import 'ej2-dropdowns/styles/list-box/tailwind-dark-definition.scss';
@import 'ej2-dropdowns/styles/drop-down-list/tailwind-dark-definition.scss';
@import 'ej2-dropdowns/styles/combo-box/tailwind-dark-definition.scss';
@import 'tailwind-dark-definition.scss';
@import 'icons/tailwind-dark.scss';
@import 'all.scss';
