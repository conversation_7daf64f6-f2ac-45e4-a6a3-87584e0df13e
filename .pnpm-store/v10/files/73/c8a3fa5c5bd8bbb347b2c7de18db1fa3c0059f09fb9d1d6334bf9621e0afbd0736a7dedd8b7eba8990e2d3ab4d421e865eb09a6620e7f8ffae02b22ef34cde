import { _PdfBaseStream, _PdfDictionary } from '@syncfusion/ej2-pdf';
/**
 * ImageStructureBase
 *
 * @hidden
 */
export declare class ImageStructureBase {
    private pdfViewer;
    private pdfViewerBase;
    private m_imageDictionary;
    private m_isImageStreamParsed;
    private m_isImageInterpolated;
    private m_imageFilter;
    private isDualFilter;
    private m_colorspace;
    private numberOfComponents;
    private internalColorSpace;
    private m_imageStream;
    constructor(stream: _PdfBaseStream, fontDictionary: _PdfDictionary);
    /**
     * @private
     *
     */
    getImageStream(): Uint8Array;
    private setColorSpace;
    private getColorSpace;
    private setImageFilter;
    private getImageFilter;
    private getImageInterpolation;
    private imageStream;
}
