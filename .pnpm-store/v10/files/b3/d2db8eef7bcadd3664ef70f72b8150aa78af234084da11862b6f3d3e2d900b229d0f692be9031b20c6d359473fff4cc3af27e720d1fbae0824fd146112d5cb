/* eslint-disable */
import { create<PERSON><PERSON>, L10n, classList, isNullOrUndefined, initializeCSPTemplate } from '@syncfusion/ej2-base';
import { ComboBox } from '@syncfusion/ej2-dropdowns';
import { Button } from '@syncfusion/ej2-buttons';
import { DropDownButton, SplitButton } from '@syncfusion/ej2-splitbuttons';
import { ColorPicker } from '@syncfusion/ej2-inputs';
import { Query } from '@syncfusion/ej2-data';
import { SanitizeHtmlHelper } from '@syncfusion/ej2-base';
/**
 * Text Properties
 *
 * @private
 */
var Text = /** @class */ (function () {
    /**
     * Initialize text properties.
     *
     * @param {DocumentEditorContainer} container - DocumentEditorContainer instance.
     * @param {boolean} isRtl - Specifies the RTL layout.
     */
    function Text(container, isRtl) {
        var _this = this;
        this.isRetrieving = false;
        this.appliedHighlightColor = 'rgb(255, 255, 0)';
        this.createChangecase = function (container) {
            var items = [{
                    text: _this.localObj.getConstant('UPPERCASE'), id: 'uppercase'
                }];
            _this.changeCaseDropdown = new DropDownButton({
                items: items,
                iconCss: 'e-icons e-de-ctnr-change-case',
                enableRtl: _this.isRtl
            });
            var changeCaseContainer = createElement('div', {
                id: container.id + '_changeCase', className: 'e-de-ctnr-group-btn'
            });
            var buttonElement = createElement('button', {
                id: changeCaseContainer.id + '_dropdownBtn',
                attrs: { type: 'button' }
            });
            changeCaseContainer.appendChild(buttonElement);
            container.appendChild(changeCaseContainer);
            _this.changeCaseDropdown.appendTo(buttonElement);
        };
        this.changeCase = function (args) {
            if (_this.isRetrieving) {
                return;
            }
            var text = args.item.id;
            switch (text) {
                case 'uppercase':
                    if (!_this.documentEditor.isReadOnly && _this.documentEditor.editor) {
                        _this.documentEditor.editor.changeCase('Uppercase');
                    }
                    break;
                default:
                    break;
            }
        };
        this.container = container;
        this.isRtl = isRtl;
    }
    Object.defineProperty(Text.prototype, "documentEditor", {
        get: function () {
            return this.container.documentEditor;
        },
        enumerable: true,
        configurable: true
    });
    Text.prototype.initializeTextPropertiesDiv = function (wholeDiv, isRtl) {
        this.localObj = new L10n('documenteditorcontainer', this.container.defaultLocale, this.container.locale);
        this.textProperties = wholeDiv;
        var element = this.documentEditor.element.id + '_font_properties';
        var textDiv = this.createDiv(element + '_text', wholeDiv);
        classList(textDiv, ['e-de-cntr-pane-padding', 'e-de-prop-separator-line'], []);
        var fontDiv = this.createDiv(element + '_sizeStyle', textDiv, 'display:inline-flex;');
        classList(fontDiv, ['e-de-ctnr-segment'], []);
        if (isRtl) {
            classList(fontDiv, ['e-de-ctnr-segment-rtl'], []);
        }
        var fontFamilyDiv = this.createDiv(element + '_fontFamilyDiv', fontDiv);
        var fontFamily = createElement('input', {
            id: element + '_fontFamily', className: 'e-prop-font-style'
        });
        fontFamilyDiv.appendChild(fontFamily);
        classList(fontFamilyDiv, ['e-de-panel-left-width'], []);
        this.createDropDownListForFamily(fontFamily);
        var fontSizeDiv = this.createDiv(element + '_fontSizeDiv', fontDiv);
        var divClassName = 'e-de-ctnr-group-btn e-de-char-fmt-btn-left e-btn-group';
        if (isRtl) {
            divClassName = 'e-rtl ' + divClassName;
        }
        var fontSize = createElement('input', {
            id: element + '_fontSize', innerHTML: 'type:number', className: 'e-prop-font-style'
        });
        fontSizeDiv.appendChild(fontSize);
        classList(fontSizeDiv, ['e-de-panel-right-width'], []);
        this.createDropDownListForSize(fontSize);
        var propertiesDiv = createElement('div', {
            id: element + '_properties', styles: 'display:inline-flex;', className: 'e-de-ctnr-segment'
        });
        if (isRtl) {
            classList(propertiesDiv, ['e-de-ctnr-segment-rtl'], []);
        }
        textDiv.appendChild(propertiesDiv);
        var leftDiv = createElement('div', {
            id: element + '_leftDiv', className: divClassName, styles: 'display:inline-flex;'
        });
        propertiesDiv.appendChild(leftDiv);
        this.bold = this.createButtonTemplate(element + '_bold', 'e-de-ctnr-bold e-icons', leftDiv, 'e-de-prop-font-button', '40.5', 'Bold Tooltip');
        this.italic = this.createButtonTemplate(element + '_italic', 'e-de-ctnr-italic e-icons', leftDiv, 'e-de-prop-font-button', '40.5', 'Italic Tooltip');
        this.underline = this.createButtonTemplate(element + '_underline', 'e-de-ctnr-underline e-icons', leftDiv, 'e-de-prop-font-button', '40.5', 'Underline Tooltip');
        this.strikethrough = this.createButtonTemplate(element + '_strikethrough', 'e-de-ctnr-strikethrough e-icons', leftDiv, 'e-de-prop-font-last-button', '40.5', 'Strikethrough');
        divClassName = 'e-de-ctnr-group-btn e-de-char-fmt-btn-right e-btn-group';
        if (isRtl) {
            divClassName = 'e-rtl ' + divClassName;
        }
        var rightDiv = createElement('div', { id: element + '_rightDiv', className: divClassName, styles: 'display:inline-flex;' });
        propertiesDiv.appendChild(rightDiv);
        this.superscript = this.createButtonTemplate(element + '_superscript', 'e-de-ctnr-superscript e-icons', rightDiv, 'e-de-prop-font-button', '38.5', 'Superscript Tooltip');
        this.subscript = this.createButtonTemplate(element + '_subscript', 'e-de-ctnr-subscript e-icons', rightDiv, 'e-de-prop-font-last-button', '38.5', 'Subscript Tooltip');
        var colorDiv = createElement('div', {
            id: element + '_colorDiv', styles: 'display:inline-flex;', className: 'e-de-ctnr-segment'
        });
        if (isRtl) {
            classList(propertiesDiv, ['e-de-ctnr-segment-rtl'], []);
        }
        var leftDiv2 = createElement('div', { id: element + '_color', className: 'e-de-font-clr-picker e-de-ctnr-group-btn', styles: 'display:inline-flex;' });
        if (isRtl) {
            classList(leftDiv2, ['e-rtl'], []);
        }
        colorDiv.appendChild(leftDiv2);
        textDiv.appendChild(colorDiv);
        this.fontColor = this.createFontColorPicker(element + '_textColor', 40.5, leftDiv2, this.localObj.getConstant('Font color'));
        classList(leftDiv2.firstElementChild.lastElementChild.lastElementChild.firstChild, ['e-de-ctnr-fontcolor', 'e-icons'], ['e-caret']);
        this.initializeHighlightColorElement();
        this.highlightColor = this.createHighlightColorSplitButton(element + '_highlightColor', 34.5, leftDiv2, this.localObj.getConstant('Text highlight color'));
        classList(this.highlightColor.element.nextElementSibling.firstElementChild, ['e-de-ctnr-highlight', 'e-icons'], ['e-caret']);
        this.highlightColorInputElement = this.highlightColor.element.firstChild;
        this.clearFormat = this.createButtonTemplate(element + '_clearFormat', 'e-de-ctnr-clearall e-icons', leftDiv2, 'e-de-prop-font-last-button', '40.5', 'Clear all formatting');
        var rightDiv2 = createElement('div', {
            id: element + '_rightDiv2', className: divClassName.replace('e-btn-group', ''), styles: 'display:inline-flex;'
        });
        if (isRtl) {
            classList(rightDiv2, ['e-rtl'], []);
        }
        rightDiv2.setAttribute('title', this.container.localObj.getConstant('Change case Tooltip'));
        textDiv.appendChild(rightDiv2);
        this.createChangecase(rightDiv2);
    };
    Text.prototype.createHighlightColorSplitButton = function (id, width, divElement, toolTipText) {
        var _this = this;
        var buttonElement = createElement('button', { id: id, attrs: { type: 'button' } });
        // buttonElement.style.width = width + 'px';
        // buttonElement.style.padding = '1px';
        // buttonElement.style.height = 30 + 'px';
        divElement.appendChild(buttonElement);
        var hgltSplitObj = new SplitButton({
            cssClass: 'e-de-btn-hghlclr',
            iconCss: 'e-de-ctnr-hglt-color',
            /* eslint-disable-next-line max-len */
            target: this.highlightColorElement, close: this.closePopup.bind(this), beforeOpen: this.openPopup.bind(this), enableRtl: this.isRtl
        });
        hgltSplitObj.appendTo(buttonElement);
        hgltSplitObj.click = function () {
            _this.applyHighlightColor(_this.highlightColorInputElement.style.backgroundColor);
        };
        hgltSplitObj.element.firstChild.style.backgroundColor = 'rgb(255, 255, 0)';
        hgltSplitObj.element.parentElement.setAttribute('title', toolTipText);
        hgltSplitObj.element.parentElement.setAttribute('aria-label', toolTipText);
        return hgltSplitObj;
    };
    Text.prototype.openPopup = function () {
        this.highlightColorElement.style.display = 'block';
    };
    Text.prototype.closePopup = function () {
        this.highlightColorElement.style.display = 'none';
    };
    Text.prototype.initializeHighlightColorElement = function () {
        this.highlightColorElement = createElement('div', {
            styles: 'display:none;width:157px',
            className: 'e-de-cntr-highlight-pane'
        });
        var yellowDiv = this.createHightlighColorPickerDiv('#ffff00', 'yellowDiv');
        var brightGreenDiv = this.createHightlighColorPickerDiv('#00ff00', 'brightGreenDiv');
        var turquoiseDiv = this.createHightlighColorPickerDiv('#00ffff', 'turquoiseDiv');
        var hotPinkDiv = this.createHightlighColorPickerDiv('#ff00ff', 'hotPinkDiv');
        var blueDiv = this.createHightlighColorPickerDiv('#0000ff', 'blueDiv');
        var redDiv = this.createHightlighColorPickerDiv('#ff0000', 'redDiv');
        var darkBlueDiv = this.createHightlighColorPickerDiv('#000080', 'darkBlueDiv');
        var tealDiv = this.createHightlighColorPickerDiv('#008080', 'tealDiv');
        var greenDiv = this.createHightlighColorPickerDiv('#008000', 'greenDiv');
        var violetDiv = this.createHightlighColorPickerDiv('#800080', 'violetDiv');
        var darkRedDiv = this.createHightlighColorPickerDiv('#800000', 'darkRedDiv');
        var darkYellowDiv = this.createHightlighColorPickerDiv('#808000', 'darkYellowDiv');
        var gray50Div = this.createHightlighColorPickerDiv('#808080', 'gray50Div');
        var gray25Div = this.createHightlighColorPickerDiv('#c0c0c0', 'gray25Div');
        var blackDiv = this.createHightlighColorPickerDiv('#000000', 'blackDiv');
        var nocolor = createElement('div', { className: 'e-hglt-no-color' });
        this.highlightColorElement.appendChild(nocolor);
        var nocolorDiv = createElement('div', { styles: 'width:24px;height:24px;background-color:#ffffff;margin:3px;', id: 'noColorDiv' });
        nocolor.appendChild(nocolorDiv);
        var nocolorDivValue = createElement('div', { innerHTML: this.localObj.getConstant('No color'), className: 'e-de-ctnr-hglt-no-color' });
        nocolorDiv.appendChild(nocolorDivValue);
        yellowDiv.addEventListener('click', this.onHighLightColor.bind(this));
        brightGreenDiv.addEventListener('click', this.onHighLightColor.bind(this));
        turquoiseDiv.addEventListener('click', this.onHighLightColor.bind(this));
        hotPinkDiv.addEventListener('click', this.onHighLightColor.bind(this));
        blueDiv.addEventListener('click', this.onHighLightColor.bind(this));
        redDiv.addEventListener('click', this.onHighLightColor.bind(this));
        darkBlueDiv.addEventListener('click', this.onHighLightColor.bind(this));
        tealDiv.addEventListener('click', this.onHighLightColor.bind(this));
        greenDiv.addEventListener('click', this.onHighLightColor.bind(this));
        violetDiv.addEventListener('click', this.onHighLightColor.bind(this));
        darkRedDiv.addEventListener('click', this.onHighLightColor.bind(this));
        darkYellowDiv.addEventListener('click', this.onHighLightColor.bind(this));
        gray50Div.addEventListener('click', this.onHighLightColor.bind(this));
        gray25Div.addEventListener('click', this.onHighLightColor.bind(this));
        blackDiv.addEventListener('click', this.onHighLightColor.bind(this));
        nocolor.addEventListener('click', this.onHighLightColor.bind(this));
    };
    Text.prototype.createHightlighColorPickerDiv = function (backgroundColor, id) {
        var colorDiv = createElement('div', { className: 'e-de-ctnr-hglt-btn', id: id });
        colorDiv.style.backgroundColor = backgroundColor;
        this.highlightColorElement.appendChild(colorDiv);
        return colorDiv;
    };
    /* eslint-disable @typescript-eslint/no-explicit-any */
    Text.prototype.onHighLightColor = function (event) {
        if (this.documentEditor.selection) {
            this.applyHighlightColor(event.currentTarget.style.backgroundColor);
            this.highlightColor.toggle();
        }
    };
    Text.prototype.applyHighlightColorAsBackground = function (color) {
        this.removeSelectedColorDiv();
        if (color === 'NoColor') {
            this.highlightColorElement.querySelector('#noColorDiv').classList.add('e-color-selected');
        }
        else if (color === 'Yellow') {
            this.highlightColorElement.querySelector('#yellowDiv').classList.add('e-color-selected');
        }
        else if (color === 'BrightGreen') {
            this.highlightColorElement.querySelector('#brightGreenDiv').classList.add('e-color-selected');
        }
        else if (color === 'Turquoise') {
            this.highlightColorElement.querySelector('#turquoiseDiv').classList.add('e-color-selected');
        }
        else if (color === 'Pink') {
            this.highlightColorElement.querySelector('#hotPinkDiv').classList.add('e-color-selected');
        }
        else if (color === 'Red') {
            this.highlightColorElement.querySelector('#redDiv').classList.add('e-color-selected');
        }
        else if (color === 'DarkBlue') {
            this.highlightColorElement.querySelector('#darkBlueDiv').classList.add('e-color-selected');
        }
        else if (color === 'Teal') {
            this.highlightColorElement.querySelector('#tealDiv').classList.add('e-color-selected');
        }
        else if (color === 'Green') {
            this.highlightColorElement.querySelector('#greenDiv').classList.add('e-color-selected');
        }
        else if (color === 'Violet') {
            this.highlightColorElement.querySelector('#violetDiv').classList.add('e-color-selected');
        }
        else if (color === 'DarkRed') {
            this.highlightColorElement.querySelector('#darkRedDiv').classList.add('e-color-selected');
        }
        else if (color === 'DarkYellow') {
            this.highlightColorElement.querySelector('#darkYellowDiv').classList.add('e-color-selected');
        }
        else if (color === 'Gray50') {
            this.highlightColorElement.querySelector('#gray50Div').classList.add('e-color-selected');
        }
        else if (color === 'Gray25') {
            this.highlightColorElement.querySelector('#gray25Div').classList.add('e-color-selected');
        }
        else if (color === 'Black') {
            this.highlightColorElement.querySelector('#blackDiv').classList.add('e-color-selected');
        }
        else if (color === 'Blue') {
            this.highlightColorElement.querySelector('#blueDiv').classList.add('e-color-selected');
        }
    };
    Text.prototype.removeSelectedColorDiv = function () {
        this.highlightColorElement.querySelector('#noColorDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#yellowDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#brightGreenDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#turquoiseDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#hotPinkDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#redDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#darkBlueDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#tealDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#greenDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#violetDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#darkRedDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#darkYellowDiv').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#gray50Div').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#gray25Div').classList.remove('e-color-selected');
        this.highlightColorElement.querySelector('#blackDiv').classList.remove('e-color-selected');
    };
    Text.prototype.applyHighlightColor = function (color) {
        this.appliedHighlightColor = color;
        var hgltColor = this.getHighLightColor(color);
        if (hgltColor === 'NoColor') {
            this.documentEditor.selection.characterFormat.highlightColor = null;
        }
        this.documentEditor.selection.characterFormat.highlightColor = hgltColor;
        this.documentEditor.focusIn();
    };
    Text.prototype.getHighLightColor = function (color) {
        switch (color) {
            case 'rgb(255, 255, 0)':
                return 'Yellow';
            case 'rgb(0, 255, 0)':
                return 'BrightGreen';
            case 'rgb(0, 255, 255)':
                return 'Turquoise';
            case 'rgb(255, 0, 255)':
                return 'Pink';
            case 'rgb(0, 0, 255)':
                return 'Blue';
            case 'rgb(255, 0, 0)':
                return 'Red';
            case 'rgb(0, 0, 128)':
                return 'DarkBlue';
            case 'rgb(0, 128, 128)':
                return 'Teal';
            case 'rgb(0, 128, 0)':
                return 'Green';
            case 'rgb(128, 0, 128)':
                return 'Violet';
            case 'rgb(128, 0, 0)':
                return 'DarkRed';
            case 'rgb(128, 128, 0)':
                return 'DarkYellow';
            case 'rgb(128, 128, 128)':
                return 'Gray50';
            case 'rgb(192, 192, 192)':
                return 'Gray25';
            case 'rgb(0, 0, 0)':
                return 'Black';
            default:
                return 'NoColor';
        }
    };
    Text.prototype.createDiv = function (id, parentDiv, style) {
        var div;
        if (style) {
            div = createElement('div', { id: id, styles: style });
        }
        else {
            div = createElement('div', { id: id });
        }
        parentDiv.appendChild(div);
        return div;
    };
    /* eslint-disable-next-line max-len */
    Text.prototype.createButtonTemplate = function (id, iconcss, div, buttonClass, width, toolTipText) {
        var button = createElement('Button', { id: id, attrs: { type: 'button' } });
        // button.style.width = width + 'px';
        // buttonElement.style.height = 32 + 'px';
        div.appendChild(button);
        var btn = new Button({
            cssClass: buttonClass, iconCss: iconcss, enableRtl: this.isRtl
        });
        btn.appendTo(button);
        button.setAttribute('title', this.localObj.getConstant(toolTipText));
        button.setAttribute('aria-label', this.localObj.getConstant(toolTipText));
        button.setAttribute('aria-pressed', 'false');
        switch (toolTipText) {
            case 'Bold Tooltip':
                this.boldBtn = btn;
                break;
            case 'Italic Tooltip':
                this.italicBtn = btn;
                break;
            case 'Underline Tooltip':
                this.underlineBtn = btn;
                break;
            case 'Strikethrough':
                this.strikethroughBtn = btn;
                break;
            case 'Superscript Tooltip':
                this.superscriptBtn = btn;
                break;
            case 'Subscript Tooltip':
                this.subscriptBtn = btn;
                break;
            default:
                this.clearFormatBtn = btn;
        }
        return button;
    };
    Text.prototype.createFontColorPicker = function (id, width, divElement, toolTipText) {
        var inputElement = createElement('input', { id: id, attrs: { 'type': 'color' } });
        inputElement.style.width = width + 'px';
        divElement.appendChild(inputElement);
        this.fontColorInputElement = new ColorPicker({ value: '#000000', showButtons: true, enableRtl: this.isRtl, locale: this.container.locale, enableOpacity: false }, inputElement);
        this.fontColorInputElement.element.parentElement.setAttribute('title', toolTipText);
        this.fontColorInputElement.element.parentElement.setAttribute('aria-label', toolTipText);
        return inputElement;
    };
    Text.prototype.createDropDownListForSize = function (fontSelectElement) {
        var _this = this;
        var fontSize = ['8', '9', '10', '11', '12', '14', '16', '18', '20', '22', '24', '26', '28', '36', '48', '72', '96'];
        this.fontSize = new ComboBox({
            dataSource: fontSize, popupHeight: '180px',
            popupWidth: '80px',
            cssClass: 'e-de-prop-dropdown',
            allowCustom: true,
            showClearButton: false,
            enableRtl: this.isRtl
        });
        this.fontSize.focus = function () {
            _this.isRetrieving = false;
            _this.fontSize.element.select();
        };
        this.fontSize.value = this.documentEditor.selection.characterFormat.fontSize.toString();
        this.fontSize.appendTo(fontSelectElement);
        this.fontSize.element.parentElement.setAttribute('title', this.localObj.getConstant('Font Size'));
    };
    Text.prototype.createDropDownListForFamily = function (fontSelectElement) {
        var _this = this;
        /* eslint-disable */
        var fontStyle;
        var isStringTemplate = false;
        var itemTemplate = '';
        if (!this.container.enableCsp) {
            itemTemplate = initializeCSPTemplate(function (data) { return "<span style=\"font-family: " + data.FontName + ";\">" + data.FontName + "</span>"; });
            isStringTemplate = true;
        }
        this.fontFamily = new ComboBox({
            dataSource: fontStyle,
            query: new Query().select(['FontName']),
            fields: { text: 'FontName', value: 'FontValue' },
            popupHeight: '150px',
            cssClass: 'e-de-prop-dropdown',
            allowCustom: true,
            showClearButton: false,
            enableRtl: this.isRtl,
            itemTemplate: itemTemplate
        });
        this.fontFamily.appendTo(fontSelectElement);
        this.fontFamily.isStringTemplate = isStringTemplate;
        var fontFamilyValue = this.container.documentEditorSettings.fontFamilies;
        for (var i = 0; i < fontFamilyValue.length; i++) {
            var fontValue = fontFamilyValue[i];
            var fontStyleValue = { 'FontName': fontValue, 'FontValue': fontValue };
            this.fontFamily.addItem(fontStyleValue, i);
        }
        this.fontFamily.focus = function () {
            _this.isRetrieving = false;
            _this.fontFamily.element.select();
        };
        this.fontFamily.element.parentElement.setAttribute('title', this.localObj.getConstant('Font'));
    };
    /* eslint-enable */
    Text.prototype.wireEvent = function () {
        var _this = this;
        this.fontFamily.addEventListener('change', function () {
            _this.changeFontFamily();
        });
        this.fontSize.addEventListener('change', function () {
            _this.changeFontSize();
        });
        this.bold.addEventListener('click', function () {
            _this.isRetrieving = false;
            _this.boldAction();
        });
        this.italic.addEventListener('click', function () {
            _this.isRetrieving = false;
            _this.italicAction();
        });
        this.underline.addEventListener('click', function () {
            _this.isRetrieving = false;
            _this.underlineAction();
        });
        this.strikethrough.addEventListener('click', function () {
            _this.isRetrieving = false;
            _this.strikethroughAction();
        });
        this.superscript.addEventListener('click', function () {
            _this.isRetrieving = false;
            _this.superscriptAction();
        });
        this.subscript.addEventListener('click', function () {
            _this.isRetrieving = false;
            _this.subscriptAction();
        });
        this.fontColorInputElement.addEventListener('change', function (args) {
            _this.isRetrieving = false;
            _this.changeFontColor(args);
        });
        this.clearFormat.addEventListener('click', function () {
            _this.isRetrieving = false;
            _this.clearFormatAction();
        });
        this.changeCaseDropdown.addEventListener('select', function (args) {
            _this.isRetrieving = false;
            _this.changeCase(args);
        });
    };
    Text.prototype.unwireEvents = function () {
        this.fontFamily.change = undefined;
        this.fontSize.change = undefined;
        this.bold.click = undefined;
        this.italic.click = undefined;
        this.underline.click = undefined;
        this.strikethrough.click = undefined;
        this.superscript.click = undefined;
        this.subscript.click = undefined;
        this.fontColorInputElement.change = undefined;
        this.highlightColorElement.click = undefined;
        this.highlightColor.click = undefined;
        this.clearFormat.click = undefined;
        this.changeCaseDropdown.select = undefined;
    };
    Text.prototype.boldAction = function () {
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.editor) {
            this.documentEditor.editor.toggleBold();
            this.documentEditor.focusIn();
        }
    };
    Text.prototype.italicAction = function () {
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.editor) {
            this.documentEditor.editor.toggleItalic();
            this.documentEditor.focusIn();
        }
    };
    Text.prototype.underlineAction = function () {
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.editor) {
            this.documentEditor.editor.toggleUnderline('Single');
            this.documentEditor.focusIn();
        }
    };
    Text.prototype.strikethroughAction = function () {
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.editor) {
            this.documentEditor.editor.toggleStrikethrough();
            this.documentEditor.focusIn();
        }
    };
    Text.prototype.clearFormatAction = function () {
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.editor) {
            this.documentEditor.editor.clearFormatting();
        }
    };
    Text.prototype.subscriptAction = function () {
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.editor) {
            this.documentEditor.editor.toggleSubscript();
            this.documentEditor.focusIn();
        }
    };
    Text.prototype.superscriptAction = function () {
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.editor) {
            this.documentEditor.editor.toggleSuperscript();
            this.documentEditor.focusIn();
        }
    };
    Text.prototype.changeFontColor = function (arg) {
        var _this = this;
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.selection) {
            this.documentEditor.selection.characterFormat.fontColor = arg.currentValue.hex;
            setTimeout(function () {
                _this.documentEditor.focusIn();
            }, 30);
        }
    };
    Text.prototype.changeFontFamily = function () {
        var _this = this;
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.selection && this.fontFamily.value !== '') {
            setTimeout(function () {
                _this.documentEditor.selection.characterFormat.fontFamily = SanitizeHtmlHelper.sanitize(_this.fontFamily.value);
            }, 10);
            this.documentEditor.focusIn();
        }
    };
    Text.prototype.changeFontSize = function () {
        var _this = this;
        if (this.isRetrieving) {
            return;
        }
        if (!this.documentEditor.isReadOnly && this.documentEditor.selection && this.fontSize.value !== '') {
            setTimeout(function () {
                _this.documentEditor.selection.characterFormat.fontSize = _this.fontSize.value;
            }, 10);
            this.documentEditor.focusIn();
        }
    };
    Text.prototype.onSelectionChange = function () {
        this.isRetrieving = true;
        if (this.documentEditor.selection) {
            //#region character format
            if (this.documentEditor.selection.characterFormat.fontFamily) {
                var fontFamily = void 0;
                if (!isNullOrUndefined(this.documentEditor.selection.characterFormat.renderedFontFamily) && !isNullOrUndefined(this.documentEditor.selection.characterFormat.fontFamily)) {
                    fontFamily = this.documentEditor.selection.characterFormat.renderedFontFamily;
                }
                else {
                    fontFamily = this.documentEditor.selection.characterFormat.fontFamily;
                }
                this.fontFamily.value = fontFamily;
                this.fontFamily.dataBind();
                if (isNullOrUndefined(this.fontFamily.getDataByValue(fontFamily))) {
                    var fontStyleValue = { 'FontName': fontFamily, 'FontValue': fontFamily };
                    this.fontFamily.addItem(fontStyleValue);
                }
            }
            else {
                this.fontFamily.value = '';
            }
            if (this.documentEditor.selection.characterFormat.fontSize) {
                this.fontSize.value = this.documentEditor.selection.characterFormat.fontSize.toString();
                this.fontSize.dataBind();
            }
            else {
                this.fontSize.value = '';
            }
            if (this.documentEditor.selection.characterFormat.bold) {
                if (!this.bold.classList.contains('e-btn-toggle')) {
                    this.bold.classList.add('e-btn-toggle');
                    this.bold.setAttribute('aria-pressed', 'true');
                }
            }
            else {
                if (this.bold.classList.contains('e-btn-toggle')) {
                    this.bold.classList.remove('e-btn-toggle');
                    this.bold.setAttribute('aria-pressed', 'false');
                }
            }
            if (this.documentEditor.selection.characterFormat.italic) {
                if (!this.italic.classList.contains('e-btn-toggle')) {
                    this.italic.classList.add('e-btn-toggle');
                    this.italic.setAttribute('aria-pressed', 'true');
                }
            }
            else {
                if (this.italic.classList.contains('e-btn-toggle')) {
                    this.italic.classList.remove('e-btn-toggle');
                    this.italic.setAttribute('aria-pressed', 'false');
                }
            }
            if (this.documentEditor.selection.characterFormat.underline
                && this.documentEditor.selection.characterFormat.underline !== 'None') {
                if (!this.underline.classList.contains('e-btn-toggle')) {
                    this.underline.classList.add('e-btn-toggle');
                    this.underline.setAttribute('aria-pressed', 'true');
                }
            }
            else {
                if (this.underline.classList.contains('e-btn-toggle')) {
                    this.underline.classList.remove('e-btn-toggle');
                    this.underline.setAttribute('aria-pressed', 'false');
                }
            }
            if (this.documentEditor.selection.characterFormat.strikethrough
                && this.documentEditor.selection.characterFormat.strikethrough !== 'None') {
                if (!this.strikethrough.classList.contains('e-btn-toggle')) {
                    this.strikethrough.classList.add('e-btn-toggle');
                    this.strikethrough.setAttribute('aria-pressed', 'true');
                }
            }
            else {
                if (this.strikethrough.classList.contains('e-btn-toggle')) {
                    this.strikethrough.classList.remove('e-btn-toggle');
                    this.strikethrough.setAttribute('aria-pressed', 'false');
                }
            }
            if (this.documentEditor.selection.characterFormat.baselineAlignment
                && this.documentEditor.selection.characterFormat.baselineAlignment === 'Subscript') {
                if (!this.subscript.classList.contains('e-btn-toggle')) {
                    this.subscript.classList.add('e-btn-toggle');
                    this.subscript.setAttribute('aria-pressed', 'true');
                }
            }
            else {
                if (this.subscript.classList.contains('e-btn-toggle')) {
                    this.subscript.classList.remove('e-btn-toggle');
                    this.subscript.setAttribute('aria-pressed', 'false');
                }
            }
            if (this.documentEditor.selection.characterFormat.baselineAlignment
                && this.documentEditor.selection.characterFormat.baselineAlignment === 'Superscript') {
                if (!this.superscript.classList.contains('e-btn-toggle')) {
                    this.superscript.classList.add('e-btn-toggle');
                    this.superscript.setAttribute('aria-pressed', 'true');
                }
            }
            else {
                if (this.superscript.classList.contains('e-btn-toggle')) {
                    this.superscript.classList.remove('e-btn-toggle');
                    this.superscript.setAttribute('aria-pressed', 'false');
                }
            }
            if (this.documentEditor.selection.characterFormat.fontColor) {
                var fontColor = this.documentEditor.selection.characterFormat.fontColor;
                // "empty" is old value used for auto color till v19.2.49. It is maintained for backward compatibility.
                if (fontColor === 'empty' || fontColor === '#00000000') {
                    fontColor = '#000000';
                }
                this.fontColorInputElement.value = fontColor;
            }
            if (this.documentEditor.selection.characterFormat.highlightColor) {
                this.highlightColorInputElement.style.backgroundColor = this.appliedHighlightColor;
                this.applyHighlightColorAsBackground(this.documentEditor.selection.characterFormat.highlightColor);
            }
            //#endregion
        }
    };
    Text.prototype.destroy = function () {
        if (this.highlightColor) {
            this.highlightColor.destroy();
            this.highlightColor = undefined;
        }
        if (this.fontColorInputElement) {
            this.fontColorInputElement.destroy();
            this.fontColorInputElement = undefined;
        }
        if (this.fontSize) {
            this.fontSize.destroy();
            this.fontSize = undefined;
        }
        if (this.fontFamily) {
            this.fontFamily.destroy();
            this.fontFamily = undefined;
        }
        if (this.changeCaseDropdown) {
            this.changeCaseDropdown.destroy();
            this.changeCaseDropdown = undefined;
        }
        if (this.boldBtn) {
            this.boldBtn.destroy();
            this.boldBtn = undefined;
        }
        if (this.italicBtn) {
            this.italicBtn.destroy();
            this.italicBtn = undefined;
        }
        if (this.underlineBtn) {
            this.underlineBtn.destroy();
            this.underlineBtn = undefined;
        }
        if (this.strikethroughBtn) {
            this.strikethroughBtn.destroy();
            this.strikethroughBtn = undefined;
        }
        if (this.subscriptBtn) {
            this.subscriptBtn.destroy();
            this.subscriptBtn = undefined;
        }
        if (this.superscriptBtn) {
            this.superscriptBtn.destroy();
            this.superscriptBtn = undefined;
        }
        if (this.clearFormatBtn) {
            this.clearFormatBtn.destroy();
            this.clearFormatBtn = undefined;
        }
        this.container = undefined;
    };
    return Text;
}());
export { Text };
