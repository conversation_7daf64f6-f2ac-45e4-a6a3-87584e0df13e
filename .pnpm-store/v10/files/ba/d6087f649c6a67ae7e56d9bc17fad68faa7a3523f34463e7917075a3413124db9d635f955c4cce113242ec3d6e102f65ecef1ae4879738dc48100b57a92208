import { ContextMenu as Context } from '@syncfusion/ej2-navigations';
import { PdfViewer, PdfViewerBase, IContextMenu } from '../index';
/**
 * ContextMenu module is used to handle the context menus used in the control.
 *
 * @hidden
 */
export declare class ContextMenu implements IContextMenu {
    /**
     * @private
     */
    contextMenuObj: Context;
    /**
     * @private
     */
    contextMenuElement: HTMLElement;
    private pdfViewer;
    private pdfViewerBase;
    private copyContextMenu;
    private contextMenuList;
    currentTarget: any;
    /**
     * @private
     */
    previousAction: string;
    /**
     * Initialize the constructor of ontextmenu
     *
     * @param { PdfViewer } pdfViewer - Specified PdfViewer class.
     * @param { PdfViewerBase } pdfViewerBase - The pdfViewerBase.
     */
    constructor(pdfViewer: PdfViewer, pdfViewerBase: PdfViewerBase);
    /**
     * @private
     * @returns {void}
     */
    createContextMenu(): void;
    private contextMenuOnCreated;
    private setTarget;
    private contextMenuOnBeforeOpen;
    private contextMenuItems;
    private processLocaleContent;
    private contextMenuCollection;
    private getEnabledItemCount;
    private hideContextItems;
    private enableCommentPanelItem;
    private onOpeningForShape;
    OnItemSelected(selectedMenu: string): void;
    private onMenuItemSelect;
    /**
     * @private
     * @returns {void}
     */
    destroy(): void;
    /**
     * @private
     * @returns {void}
     */
    close(): void;
    /**
     * open the context menu.
     * @param {number} top - The top.
     * @param {number} left - The left.
     * @param {HTMLElement} target - The target.
     * @returns {void}
     */
    open(top: number, left: number, target: HTMLElement): void;
}
